# PIS Earthing Calculations

A Flutter application for performing earthing system calculations according to BS 7430:2011 +A1:2015 standards.

## Overview

This application provides tools for calculating:
- Earthing system resistance for various configurations
- Required cable cross-sectional areas for fault conditions
- Parallel rod arrangements
- Buried conductor arrangements

## Features

- **Earthing Resistance Calculations**
  - Parallel rod arrangements
  - Buried straight conductors
  - Combined systems (rods + buried conductors)
  - Support for various soil resistivities

- **Cable Sizing**
  - Fault current calculations
  - Cross-sectional area determination
  - Standard cable size selection
  - Temperature rise considerations

- **Compliance**
  - Follows BS 7430:2011 +A1:2015 standards
  - Validated against manual calculation worksheets
  - Comprehensive test suite

## Technical Details

### Architecture
- Built with Flutter
- Uses BLoC pattern for state management
- Modular design with separate calculation engine
- Comprehensive unit tests

### Key Components
- `earthing_formulas.dart`: Core calculation engine
- `earthing_bloc/`: State management
- `calculation/`: Business logic and formulas
- `test/`: Unit tests and validation

## Development

### Prerequisites
- Flutter SDK
- Dart SDK
- Git

### Setup
1. Clone the repository
```bash
git clone [repository-url]
cd pis_core
```

2. Install dependencies
```bash
flutter pub get
```

3. Run tests
```bash
flutter test
```

### Testing
The project includes comprehensive tests that validate calculations against manual worksheets. Key test files:
- `earthing_manual_example_test.dart`: Validates against real-world examples
- `earthing_formulas_test.dart`: Unit tests for calculation engine

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]

# Engineering Earthing System Calculation App — AI/Developer Prompt & Best Practices

This document serves as a **prompt and workflow guide** for any AI or developer working on this project, ensuring all code, tests, and documentation are consistent, traceable, and scientifically sound.

---

## 1. **Always Use Engineering References and Formulas**
- All calculations must be based on provided engineering sheets, standards (e.g., BS 7430:2011+A1:2015), and reference images.

- Do not invent or guess formulas—trace every variable and equation to a source.

## 2. **Constants, Data Tables, and Engineering Specs**
- Store all standard values (e.g., rod/cable sizes, diameters, lambda tables) in a central constants file (`bs7430_constants.dart`).
- Use data classes (e.g., `CableRodSpec`) for structured tables.
- Provide lookup functions for lambda, rod diameters, etc.

## 3. **Freezed for Models and BLoC State**
- Use Freezed for all data models and BLoC state/events for immutability, copyWith, and default values.
- The BLoC should only perform calculations and emit new states using copyWith.
- All calculation input and result data should be in the state.

## 4. **Test Structure and Precision**
- Write tests for every engineering example and reference sheet.
- Compare results to 3 decimal places using `.toStringAsFixed(3)`.
- Print all input and output values in tests for traceability.
- Use `closeTo` for floating-point comparisons if needed.

## 5. **Print/Debug Output**
- Always print all input parameters, intermediate, and final results in tests.
- Clearly label each value and indicate expected values from the sheet.

## 6. **Equation Handling**
- Implement formulas exactly as written in the engineering reference.
- Double-check variable order and log/ln base.
- Document each function with the formula and reference.

## 7. **Code Structure and Organization**
- Organize code into clear folders: `calculation/`, `bloc/`, `constants/`.
- Use a barrel file for calculation exports.
- Keep calculation logic, models, and constants separate.

## 8. **Floating-Point Precision**
- Always compare and print results to 3 decimal places.
- Use `.toStringAsFixed(3)` for both assertions and output.

## 9. **Documentation and Explanation**
- Document every variable, function, and constant with references to the engineering sheet or standard.
- Explain what each value represents in both code and test output.

## 10. **Handling New Images/Equations**
- When new images or equations are provided, extract all variables and update constants, models, and tests accordingly.
- Add new test cases for every new example.

## 11. **Using and Updating Constants**
- Always use constants and lookup tables for standard values (e.g., rod diameter, lambda).
- Update the constants file when new specs are provided.

## 12. **Traceability to Engineering Sheets**
- Ensure every calculation and test can be traced back to a specific cell or value in the reference sheet.
- Print both calculated and expected values in tests.

## 13. **Logarithm Handling (log/ln)**
- Use Dart's `log(x)` for natural logarithm (ln, logₑ).
- Alias as `ln(x)` if needed for clarity.
- Confirm with a test that `log(e) == 1.0`.

## 14. **Test Output and Debugging**
- Print all values in tests, especially when a test fails.
- Clearly show both actual and expected values.

## 15. **Lambda and Rod/Cable Tables**
- Use a function to calculate lambda for any n: `lambda = 2 * (1/2 + ... + 1/n)`.
- Store rod/cable specs in a structured list for lookup.

## 16. **Wenner Method Handling**
- Provide a function to calculate soil resistivity from X and R: `rho = 2 * pi * R * X`.
- Provide a function to average multiple measurements.
- Add tests to verify Wenner calculations match the sheet.

## 17. **Code Expansion and Refactoring**
- When expanding or refactoring, always:
  - Update constants and data tables.
  - Add new tests for every new formula or example.
  - Keep code modular and well-documented.

---

**Follow this workflow and structure for all future work on this project.**
- If in doubt, print and compare all values.
- Always trace every calculation to the engineering reference.
- Keep everything clear, testable, and maintainable.





# Earthing System Design Workflow

## Overview
This document outlines the step-by-step process for designing an earthing system, with verification against target resistance values.

## Design Steps

### 1. Soil Resistivity (ρ)
- Obtain soil resistivity from:
  - Soil measurement report
  - Manual entry
  - System configurations list
- **Note**: Add `isClosedLoop` parameter to `EarthingSystemConfiguration` to indicate system topology

### 2. Rod Selection
- Select rod diameter from standard `rodDiameters` list
- Choose rod length:
  - 1.2 m
  - 1.5 m

### 3. Cable Sizing
Two options available:
- Direct diameter entry
- Automatic calculation using:
  - S and K calculations
  - LV/MV time constants

### 4. Well Depth
- Typically multiples of rod length
- Custom values permitted

### 5. Rod Resistance Calculation
Input parameters:
- ρ (soil resistivity)
- L (depth)
- d (rod diameter)
- S (spacing between rods, default = depth)

### 6. Number of Wells
- User-defined value (n)
- Used to calculate:
  - Rod resistance
  - Cable resistance
  - Total system resistance

### 7. Cable Length Calculation
Based on system topology:
- Closed loop: `(n × s) + n`
- Open loop: `((n-1) × s) + n`

### 8. Drilling Depth (h)
- Standard values: 0.6 m, 0.7 m, 0.8 m
- Default: 0.6 m
- Custom values permitted

### 9. Total Resistance
- Calculate using available system equation
- Combines:
  - Rod resistance (Rrods)
  - Cable resistance (Rcable)

### 10. Documentation
- Generate comprehensive report
- Export as PDF

## Notes
- All calculations verified against engineering standards
- K values required for each configuration (update standards file)
- Current implementation uses bloc pattern for input handling
