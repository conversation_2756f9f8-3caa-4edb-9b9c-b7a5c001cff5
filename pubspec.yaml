name: pis_core
description: "Professional calculation software for earthing and lightning protection systems by PIS Professional Integrated Solutions."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.3
  hydrated_bloc: ^9.1.5
  path_provider: ^2.1.5
  json_annotation: ^4.9.0
  flutter_hooks: ^0.21.2
  go_router: ^14.6.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  freezed: ^3.0.6
  freezed_annotation: ^3.0.0
  bloc_test: ^9.1.7
  talker_bloc_logger: ^4.6.4
  flutter_math_fork: ^0.7.4
  flutter_launcher_icons: ^0.14.4
  flutter_svg: ^2.2.0
  syncfusion_flutter_pdf: ^30.1.38
  animated_text_kit: ^4.2.3
  window_manager: ^0.4.2
  share_plus: ^11.0.0
  pdf: ^3.10.7
  printing: ^5.12.0

dev_dependencies:
  build_runner: ^2.4.15
  json_serializable: ^6.9.5
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  rename: ^3.1.0
  inno_bundle: ^0.9.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/logo/
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
  
  fonts:
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
        - asset: assets/fonts/NotoSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/NotoSans-Bold.ttf
          weight: 700

# Flutter Launcher Icons Configuration
flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/logo/PIS logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/logo/PIS logo.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/logo/PIS logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/logo/PIS logo.png"

inno_bundle:
  # Keep this GUID stable once you’ve shipped to users
  id: 583cca46-cae0-5273-b438-d78249307a55

  # Display names used throughout the installer and in Add-/Remove-programs
  name: "PIS Core"
  publisher: "PIS Professional Integrated Solutions"

  # Optional hyperlinks shown inside the wizard
  url: "https://professional-egypt.com"          # “Publisher website”
  support_url: "mailto:<EMAIL>"

  # Long description (shows in the welcome page and metadata)
  description: |
    PIS Core is a professional calculation software for earthing and lightning
    protection systems, developed by PIS (Professional Integrated Solutions for
    Trade and Construction). With over 20 years of experience in the energy
    sector across Egypt, PIS has established itself as a leading provider of
    state-of-the-art solutions for quality earthing networks and lightning
    arresters in various applications, from residential to sophisticated
    industrial installations. This software follows international standards and
    provides accurate calculations for electrical protection systems.

  # Legal / licence text
  license_file: "LICENSE.txt"

  # Branding (ICO ≥ 256×256)
  installer_icon: "assets/logo/PIS logo.ico"

  # Localisation
  languages:
    - english

  # Runtime + install scope
  arch: x64_compatible    # 64-bit binaries (still runs on WoW64 if needed)
  vc_redist: true         # bundles the VC++ runtime DLLs
  admin: auto             # lets user choose per-user or system-wide install
