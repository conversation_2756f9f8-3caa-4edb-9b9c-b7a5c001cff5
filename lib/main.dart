import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/core/theme/theme_settings_bloc.dart';
import 'package:pis_core/core/router/app_router.dart';
import 'dart:io' show Platform;
import 'package:window_manager/window_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Configure window size for desktop platforms (not web)
  if (!kIsWeb && (Platform.isWindows || Platform.isMacOS || Platform.isLinux)) {
    await windowManager.ensureInitialized();

    WindowOptions windowOptions = const WindowOptions(
      size: Size(1024, 600), // Reduced height for smaller screens
      minimumSize: Size(1024, 600), // Minimum size with reduced height
      center: true,
      backgroundColor: Colors.transparent,
      skipTaskbar: false,
      titleBarStyle: TitleBarStyle.normal,
    );

    windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
    });
  }

  // Initialize HydratedBloc storage
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: await getApplicationDocumentsDirectory(),
  );

  // Bloc.observer = TalkerBlocObserver();
  runApp(const PISCoreApp());
}

class PISCoreApp extends StatelessWidget {
  const PISCoreApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ThemeSettingsBloc(),
      child: BlocBuilder<ThemeSettingsBloc, ThemeSettingsState>(
        buildWhen: (previous, current) => true, // Always rebuild on state changes
        builder: (context, themeState) {
          return MaterialApp.router(
            title: 'PIS Core',
            theme: AppTheme.lightTheme(themeState.fontSize),
            routerConfig: AppRouter.router,
          );
        },
      ),
    );
  }
}
