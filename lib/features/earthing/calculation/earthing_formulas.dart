// earthing_calculations.dart — PIS project complete helper library
// =================================================================================
// Implements every numerical step described in the user‑supplied "Earthing System
// Design Workflow" and cross‑checked against BS 7430:2011 +A1:2015 clauses.
// No map literals are used; enums / constant lists provide all look‑ups for full
// type‑safety and readability.
// =================================================================================
//   Step‑1  Soil‑resistivity helpers                      (general)
//   Step‑2  Standard rod catalogue  + validation          (Clause 9.5 family)
//   Step‑3  Adiabatic cable‑sizing incl. Table 7 & K₁    (Clause 9.7 & App‑F)
//   Step‑4  Well/depth helpers                           (workflow spec)
//   Step‑5  Electrode‑resistance equations 9.5.2‑9.5.8   (§9.5.xx)
//   Step‑6  Well‑count validation                        (workflow spec)
//   Step‑7  Cable‑length helper (closed  / open loop)     (workflow spec)
//   Step‑8  Drilling‑depth list & validator              (workflow spec)
//   Step‑9  Parallel‑resistance combiner                (general)
//   Step‑10 StandardCableSize enum (S ↔ rod advice)      (user table img)
// =================================================================================
// © Auther: Ibrahim Nasreddin Ibrahim

import 'dart:math' as math;

// -----------------------------------------------------------------------------
// Common typedefs -------------------------------------------------------------

typedef SoilResistivity = double; // Ω·m
typedef Length = double; // m
typedef Diameter = double; // m
typedef Spacing = double; // m
typedef Depth = double; // m
typedef Count = int; // integer count
typedef CrossSectionArea = double; // mm²

double _ln(num x) => math.log(x);
double _sqrt(num x) => math.sqrt(x);

// -----------------------------------------------------------------------------
// STEP‑1  Soil resistivity -----------------------------------------------------

bool isValidSoilResistivity(SoilResistivity rho) => rho > 0;

/// Calculates soil resistivity ρ (Ω·m) using the Wenner four-pin method.
///
/// BS 7430 Clause 10.2.2 gives the simple relation:
///   ρ = 2 π R X
/// where *R* is the measured resistance in ohms and *X* is the electrode
/// spacing in metres.  *X* is assumed » > electrode depth so the simple form
/// applies (no depth correction factor).
SoilResistivity calcWennerRho({
  required double resistanceOhm,
  required double spacingM,
}) => 2 * math.pi * resistanceOhm * spacingM;

/// Convenience helper: returns *ρ* values for each (R, X) pair supplied.
List<SoilResistivity> calcWennerRhoList({
  required List<double> resistancesOhm,
  required List<double> spacingsM,
}) {
  assert(resistancesOhm.length == spacingsM.length,
      'R and X lists must be the same length');
  final out = <SoilResistivity>[];
  for (var i = 0; i < resistancesOhm.length; i++) {
    out.add(calcWennerRho(
      resistanceOhm: resistancesOhm[i],
      spacingM: spacingsM[i],
    ));
  }
  return out;
}

/// Average ρ (Ω·m) across multiple Wenner readings.
SoilResistivity averageWennerRho({
  required List<double> resistancesOhm,
  required List<double> spacingsM,
}) {
  final rhos = calcWennerRhoList(
    resistancesOhm: resistancesOhm,
    spacingsM: spacingsM,
  );
  if (rhos.isEmpty) return 0;
  return rhos.reduce((a, b) => a + b) / rhos.length;
}

// -----------------------------------------------------------------------------
// STEP‑2  Standard rod catalogue  (Clause 10 Table 10 + user image) ----------

/// BS 7430 popular diameters converted from 5/8" & 3/4" rods.
/// `dTable` adopts the exact metric shown in the user‑provided table; this is the
/// value referenced in all resistance formulas so that the computed resistances
/// reproduce the handbook / spreadsheet numbers.
///
/// | Name    | Nominal d (m) | Table d (m) |
/// |---------|---------------|-------------|
/// | rod5_8  | 0.0159        | 0.0142      |
/// | rod3_4  | 0.0191        | 0.0172      |
///
/// *Clause reference*: sizes ≥ 14 mm satisfy Clause 9.10/Table 10 requirements.
///
enum StandardRod {
  rod5_8(0.0159, 0.0142, '5/8" (14.2 mm)'),
  rod3_4(0.0191, 0.0172, '3/4" (17.2 mm)');

  const StandardRod(this.dNominal, this.dTable, this.label);
  final Diameter dNominal;
  final Diameter dTable;
  final String label;

  /// Use the normative 'table' value in calculations so they align with sizing
  /// advice image (5fbe3202‑…).
  Diameter get use => dTable;
}

const List<Length> standardRodLengths = [1.2, 1.5];

bool isValidRod(Diameter d, Length L) =>
    StandardRod.values.any((r) => (r.use - d).abs() < 1e-4) &&
    standardRodLengths.contains(L);

// -----------------------------------------------------------------------------
// STEP‑3  Cable sizing & K‑constant (Clause 9.7 + Table 7 + App‑F.4) ----------

enum ConductorMaterial {
  copper('Copper'),
  aluminium('Aluminium');

  const ConductorMaterial(this.label);
  final String label;
}

extension _MaterialData on ConductorMaterial {
  /// Returns K₁ & β from BS 7430 Table 7 (Clause 9.7)
  (double k1, double beta) get _tbl => switch (this) {
    ConductorMaterial.copper => (226, 234.5),
    ConductorMaterial.aluminium => (148, 228),
  };
  double _rho20() => switch (this) {
    ConductorMaterial.copper => 1.72e-8, // Ω·m @ 20 °C
    ConductorMaterial.aluminium => 2.83e-8,
  };
}

enum SystemVoltageType {
  lv('LV'),
  mv('MV');

  const SystemVoltageType(this.label);
  final String label;
}

(double min, double max) _faultWindow(SystemVoltageType v) =>
    v == SystemVoltageType.lv ? (0.4, 1.0) : (0.5, 3.0);

double _clamp(double v, double lo, double hi) =>
    v < lo ? lo : (v > hi ? hi : v);

/// Conductor cross‑section geometry
enum ConductorShape {
  strip('Strip'),
  round('Round');

  const ConductorShape(this.label);
  final String label;
}

/// Arrangement‑factor K₁ (App‑F.4) for buried conductor layouts.
/// *Used both in adiabatic sizing & buried‑electrode formulas.*
enum CableArrangementK1 {
  straight(1.36, 1.83), // Clause 9.5.5
  lShape(1.21, 0.813), // Clause *******
  star(0.734, 0.499), // Clause *******
  cruciform(0.219, 0.133); // Clause *******

  const CableArrangementK1(this.kStrip, this.kRound);
  final double kStrip, kRound;
  double of(ConductorShape s) => s == ConductorShape.strip ? kStrip : kRound;
}

/// Calculates **K** constant (BS 7430 eqn. (9)) for the adiabatic equation.
/// *Clause ref*: 9.7 Table 7.
///
/// `finalTempC` default 450 °C corresponds to copper conductors (BS 7430 text).
/// For aluminium choose 300 °C to stay below its annealing point.

double calcKConstant({
  required ConductorMaterial material,
  double initialTempC = 30,
  double finalTempC = 450,
}) {
  final (k1, beta) = material._tbl;
  return k1 * _ln((finalTempC + beta) / (initialTempC + beta));
}

/// Required CSA from adiabatic formula S = I·√t / (K·K₁)
CrossSectionArea calcRequiredCSA({
  required double faultCurrentA,
  required double faultDurationS,
  required double kConst,
}) => (faultCurrentA * _sqrt(faultDurationS)) / (kConst);

/// DC resistance of a conductor at 20 °C over `length` metres.
double calcCableResistance({
  required Length length,
  required CrossSectionArea areaMm2,
  ConductorMaterial material = ConductorMaterial.copper,
}) {
  final areaM2 = areaMm2 * 1e-6; // mm² → m²
  return (material._rho20() * length) / areaM2;
}

// -----------------------------------------------------------------------------
// STEP‑10  Standard cable sizes table (user image) ---------------------------

/// Enum rows replicate the 16‑row table supplied by the user (image id
/// 5fbe3202‑…). Each entry binds an *install CSA* to a rod‑installation advice.
/// Implementation via enum keeps the code map‑free.

enum StandardCableSize {
  // area (mm²),  diameter (m),  rods, rodLen (m)
  s10(10, 0.0037, 2, 1.2),
  s16(16, 0.0047, 2, 1.5),
  s25(25, 0.0058, 3, 1.2),
  s35(35, 0.0069, 3, 1.5),
  s50(50, 0.0082, 4, 1.2),
  s70(70, 0.0098, 4, 1.5),
  s95(95, 0.0113, 5, 1.2),
  s120(120, 0.0127, 5, 1.5),
  s150(150, 0.0141, 6, 1.2),
  s185(185, 0.0158, 6, 1.5),
  s240(240, 0.0182, 7, 1.2),
  s300(300, 0.0206, 7, 1.5),
  s400(400, 0.0232, 8, 1.2),
  s500(500, 0.0266, 8, 1.5);

  const StandardCableSize(this.areaMm2, this.diameterM, this.rods, this.rodLen);
  final CrossSectionArea areaMm2;
  final double diameterM;
  final Count rods;
  final Length rodLen;
  Length totalRodLen() => rods * rodLen;

  /// Select the *smallest* entry whose CSA ≥ `requiredArea`.
  static StandardCableSize select(double requiredArea) {
    for (final s in StandardCableSize.values) {
      if (s.areaMm2 >= requiredArea - 1e-6) return s;
    }
    return StandardCableSize.s500; // fallback (largest table size)
  }
}

/// Bundled result for auto sizing.
({StandardCableSize size, double resistance}) _autoCable({
  required Length length,
  required double requiredCsa,
  required ConductorMaterial material,
}) {
  final chosen = StandardCableSize.select(requiredCsa);
  final r = calcCableResistance(
    length: length,
    areaMm2: chosen.areaMm2,
    material: material,
  );
  return (size: chosen, resistance: r);
}

/// Convenience front‑end — clamps fault duration to LV/MV window,
/// selects CSA, returns cable resistance + chosen enum.
({double rCable, StandardCableSize chosen, double requiredCsa})
calcCableResistanceAuto({
  required Length length,
  required double faultCurrentA,
  required SystemVoltageType voltage,
  ConductorMaterial material = ConductorMaterial.copper,
  double? faultDurationS,
  CableArrangementK1? arrangement,
  ConductorShape conductorShape = ConductorShape.strip,
  double initialTempC = 30,
  double finalTempC = 450,
}) {
  final (lo, hi) = _faultWindow(voltage);
  final t = _clamp(faultDurationS ?? lo, lo, hi);
  final kConst = calcKConstant(
    material: material,
    initialTempC: initialTempC,
    finalTempC: finalTempC,
  );
  final reqCsa = calcRequiredCSA(
    faultCurrentA: faultCurrentA,
    faultDurationS: t,
    kConst: kConst,
  );
  final auto = _autoCable(
    length: length,
    requiredCsa: reqCsa,
    material: material,
  );
  return (rCable: auto.resistance, chosen: auto.size, requiredCsa: reqCsa);
}

// -----------------------------------------------------------------------------
// STEP‑4  Well‑depth helpers ---------------------------------------------------

bool isValidWellDepth(Depth h, Length rodLen) =>
    h > 0 && (h % rodLen).abs() < 1e-6;

// -----------------------------------------------------------------------------
// STEP‑5  Electrode‑resistance formulas (Clauses 9.5.2 ‑ 9.5.8) -------------

enum EarthingArrangement {
  // Vertical rod families
  singleRod('Single Rod'), // 9.5.3
  parallelRodLine('Parallel Rod Line'), // 9.5.4
  triangleRod('Triangle Rod'), // 9.5.8.1
  hollowSquareRod('Hollow-Square Rod'), // 9.5.8.5
  // Buried conductor families
  buriedStraight('Buried Straight'), // 9.5.5
  buriedLShape('Buried L-Shape'), // *******
  buriedStar('Buried Star'), // *******
  buriedCruciform('Buried Cruciform'), // *******
  // Surface / plate
  meshGrid('Mesh Grid'), // 9.5.6
  plate('Plate'); // 9.5.2

  const EarthingArrangement(this.label);
  final String label;
}

// ---------------------------------------------------------------------------
// EarthingArrangement ► closed / open loop helper
// ---------------------------------------------------------------------------

extension EarthingLoop on EarthingArrangement {
  /// `true`  → the conductors form a closed geometric loop
  /// `false` → open-ended (line / L / cruciform / single rod)
  bool get isClosedLoop {
    switch (this) {
      // ---------- OPEN geometries ------------------------------------------
      case EarthingArrangement.singleRod:
      case EarthingArrangement.parallelRodLine:
      case EarthingArrangement.buriedStraight:
      case EarthingArrangement.buriedLShape:
      case EarthingArrangement.buriedCruciform:
        return false;

      // ---------- CLOSED geometries ----------------------------------------
      case EarthingArrangement.triangleRod: // equilateral perimeter
      case EarthingArrangement.hollowSquareRod: // square perimeter
      case EarthingArrangement.buriedStar: // 3-leg star (closed path)
      case EarthingArrangement.meshGrid: // mesh / grid
      case EarthingArrangement.plate: // solid plate
        return true;
    }
  }
}

// ---------------- Vertical rods ---------------------------------------------

double calcSingleRod({
  required SoilResistivity rho,
  required Length L,
  required Diameter d,
}) => (rho / (2 * math.pi * L)) * (_ln(8 * L / d) - 1);

/// Parallel rods in one line — λ estimation via harmonic series (Clause 9.5.4)
double calcParallelRodLine({
  required SoilResistivity rho,
  required Length L,
  required Diameter d,
  required Spacing s,
  required Count n,
}) {
  double lambda = 0; // Σ 1/i, i=2…n
  for (var i = 2; i <= n; i++) {
    lambda += 1 / i;
  }
  lambda *= 2; // group factor approximation
  return (1 / n) *
      (rho / (2 * math.pi * L)) *
      (_ln(8 * L / d) - 1 + lambda * L / s);
}

/// Three rods arranged in equilateral triangle (Clause 9.5.8.1)
double calcTriangleRod({
  required SoilResistivity rho,
  required Length L,
  required Diameter d,
  required Spacing side,
}) => (rho / (3 * math.pi * L)) * (_ln(8 * L / d) - 1 + 2 * _ln(L / side));

// Hollow‑square rod array (Clause 9.5.8.5) ------------------------------------

double _lambdaSquare(int nPerSide) {
  const table = <int, double>{
    2: 2.71,
    3: 4.51,
    4: 5.46,
    5: 6.14,
    6: 6.63,
    7: 7.03,
    8: 7.30,
    9: 7.65,
    10: 7.90,
    12: 8.22,
    14: 8.67,
    16: 8.95,
    18: 9.22,
    20: 9.40,
  };
  return table[nPerSide] ?? (2 * _ln(1.781 * nPerSide / math.e));
}

double calcHollowSquareRod({
  required SoilResistivity rho,
  required Length L,
  required Diameter d,
  required Spacing s,
  required Count rodsPerSide,
}) {
  final r1 = calcSingleRod(rho: rho, L: L, d: d);
  final lam = _lambdaSquare(rodsPerSide);
  final nTot = 4 * (rodsPerSide - 1);
  final alpha = rho / (2 * math.pi * r1 * s);
  return r1 * (1 + (lam * alpha) / nTot);
}

// ---------------- Buried conductors (strip / round) -------------------------

enum _BuriedK { straight, lShape, star, cruciform }

const _kStrip = <_BuriedK, double>{
  _BuriedK.straight: 1.36,
  _BuriedK.lShape: 1.21,
  _BuriedK.star: 0.734,
  _BuriedK.cruciform: 0.219,
};
const _kRound = <_BuriedK, double>{
  _BuriedK.straight: 1.83,
  _BuriedK.lShape: 0.813,
  _BuriedK.star: 0.499,
  _BuriedK.cruciform: 0.133,
};

double _kVal(_BuriedK k, ConductorShape s) =>
    s == ConductorShape.strip ? _kStrip[k]! : _kRound[k]!;

/// Generic buried‑electrode resistance equation (Clauses 9.5.5 / 9.5.8.x)
/// R = ρ/(2πL) · ln( L² / (k·h·d) )
/// Where *k* differs per arrangement & geometry (Table F.4 constants above).

double _buriedFormula({
  required SoilResistivity rho,
  required Length L,
  required Depth h,
  required Diameter d,
  required _BuriedK kType,
  required ConductorShape shape,
}) => (rho / (2 * math.pi * L)) * _ln((L * L) / (_kVal(kType, shape) * h * d));

// ------- Dedicated wrappers with clause comment -----------------------------

double calcBuriedStraight({
  required SoilResistivity rho,
  required Length L,
  required Depth h,
  required Diameter d,
  ConductorShape shape = ConductorShape.strip, // Clause 9.5.5
}) => _buriedFormula(
  rho: rho,
  L: L,
  h: h,
  d: d,
  kType: _BuriedK.straight,
  shape: shape,
);

double calcBuriedLShape({
  required SoilResistivity rho,
  required Length L,
  required Depth h,
  required Diameter d,
  ConductorShape shape = ConductorShape.strip, // Clause *******
}) => _buriedFormula(
  rho: rho,
  L: L,
  h: h,
  d: d,
  kType: _BuriedK.lShape,
  shape: shape,
);

double calcBuriedStar({
  required SoilResistivity rho,
  required Length L,
  required Depth h,
  required Diameter d,
  ConductorShape shape = ConductorShape.strip, // Clause *******
}) => _buriedFormula(
  rho: rho,
  L: L,
  h: h,
  d: d,
  kType: _BuriedK.star,
  shape: shape,
);

double calcBuriedCruciform({
  required SoilResistivity rho,
  required Length L,
  required Depth h,
  required Diameter d,
  ConductorShape shape = ConductorShape.strip, // Clause *******
}) => _buriedFormula(
  rho: rho,
  L: L,
  h: h,
  d: d,
  kType: _BuriedK.cruciform,
  shape: shape,
);

// ---------------- Mesh & plate electrodes -----------------------------------

double calcMeshGrid({
  required SoilResistivity rho,
  required double area,
  required double perimeter,
}) => (rho / (math.pi * perimeter)) * (_ln((16 * area) / perimeter) - 1);

double calcPlate({
  required SoilResistivity rho,
  required double side, // square plate side (m)
}) => rho / (4 * side);

// -----------------------------------------------------------------------------
// STEP‑6   Well‑count -----------------------------------------------------------

bool isValidWellCount(Count n) => n > 0;

// -----------------------------------------------------------------------------
// STEP‑7   Cable‑length helper --------------------------------------------------

Length calcCableLength({
  required bool isClosedLoop,
  required Count wells,
  required Spacing spacing,
}) =>
    isClosedLoop ? (wells * spacing) + wells : ((wells - 1) * spacing) + wells;

// -----------------------------------------------------------------------------
// STEP‑8   Drilling‑depths ------------------------------------------------------

const List<Depth> standardDrillDepths = [0.6, 0.7, 0.8];
bool isStandardDrillDepth(Depth h) => standardDrillDepths.contains(h);

// -----------------------------------------------------------------------------
// STEP‑9   Parallel resistance utilities --------------------------------------

double combineParallel(List<double> rs) {
  if (rs.isEmpty) return double.maxFinite;
  var acc = 0.0;
  for (final r in rs) {
    if (r <= 0) return 0; // perfect short or invalid input
    acc += 1 / r;
  }
  return 1 / acc;
}

double calcTotalParallel({required double rRods, required double rCable}) =>
    combineParallel([rRods, rCable]);

/// Public wrapper to get λ factor for hollow-square rod arrays.
double calcLambdaSquare(int nPerSide) => _lambdaSquare(nPerSide);

// ---------------------------------------------------------------------------
// Encased vertical rods (Clause 9.5.7) --------------------------------------

/// Resistance of **one** vertical rod encased in a low-resistivity material
/// such as concrete or bentonite.  The surrounding infill (ρc, outer
/// diameter *D*) and the native soil (ρ) each contribute a logarithmic term.
///
/// R = ρ /(2πL)·ln(8L/D) − ρ /(2πL) + ρc/(2πL)·ln(8L/d) − ρc/(2πL)
///   = ρ /(2πL)·(ln(8L/D) − 1) + ρc/(2πL)·(ln(8L/d) − 1)
///
/// *Clause reference*: BS 7430:2011 §9.5.7 + Fig. 15.
double calcSingleRodEncased({
  required SoilResistivity rho, // native soil
  required Length L,
  required Diameter d, // steel rod diameter
  required Diameter infillD, // D – overall diameter of encasing material
  required SoilResistivity rhoC, // ρc – resistivity of encasing
}) {
  final outer = (rho / (2 * math.pi * L)) * (_ln(8 * L / infillD) - 1);
  final inner = (rhoC / (2 * math.pi * L)) * (_ln(8 * L / d) - 1);
  return outer + inner;
}

/// n **parallel** encased rods in a straight line (λ approximation identical
/// to Clause 9.5.4).  Both the soil and the infill share the same λ group
/// factor because geometrically the current dispersion for each region is
/// identical.
double calcParallelRodLineEncased({
  required SoilResistivity rho,
  required SoilResistivity rhoC,
  required Length L,
  required Diameter d,
  required Diameter infillD,
  required Spacing s,
  required Count n,
}) {
  double lambda = 0;
  for (var i = 2; i <= n; i++) {
    lambda += 1 / i;
  }
  lambda *= 2; // same harmonic-series approximation as plain rods

  final common = (_ln(8 * L / infillD) - 1 + lambda * L / s);
  final inner = (_ln(8 * L / d) - 1 + lambda * L / s);

  final termSoil = (rho / (2 * math.pi * L)) * common;
  final termInfill = (rhoC / (2 * math.pi * L)) * inner;

  return (1 / n) * (termSoil + termInfill);
}

// ============================================================================
// End of earthing_calculations.dart
// ============================================================================
