// earthing_input.dart
// ---------------------------------------------------------------------------
// All user-supplied parameters needed by earthing_calculations.dart.
// Safe engineering defaults are provided where practical.
// ---------------------------------------------------------------------------

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';

part 'earthing_input.freezed.dart';

@freezed
@Assert('rodEncased == false || (enhancementResistivity != null && infillDiameter != null)',
    'When rods are encased you must provide both enhancementResistivity and infillDiameter')
abstract class EarthingCalculationInput with _$EarthingCalculationInput {
  const EarthingCalculationInput._(); // enables custom getters

  const factory EarthingCalculationInput({
    // ---------------- Project Information ---------------------------------
    @Default('') String projectName,
    @Default('') String mainConstructor,

    // ---------------- Step‑1 ----------------------------------------------
    @Default(25.8) SoilResistivity soilResistivity,
    @Default(2) double targetTotalResistance,

    // ---------------- Step‑2  (rod selection) -----------------------------
    @Default(StandardRod.rod5_8) StandardRod rodType,
    // Optional manual override (falls back to rodType.use when null)
    Diameter? customRodDiameter,
    @Default(2.4) Length rodLength,

    // ---------------- Step‑3  (cable sizing) ------------------------------
    @Default(SystemVoltageType.mv) SystemVoltageType systemVoltage,
    @Default(26_000.0) double faultCurrentA,
    @Default(1) double faultDurationS,
    @Default(ConductorMaterial.copper) ConductorMaterial conductorMaterial,
    @Default(30) double initialTempC,
    @Default(450) double finalTempC,

    // ---------------- Step‑4  Well / depth --------------------------------
    @Default(0.8) Depth burialDepth,

    // ---------------- Step‑5  Electrode arrangement -----------------------
    @Default(EarthingArrangement.singleRod) EarthingArrangement arrangement,
    @Default(ConductorShape.strip) ConductorShape conductorShape,
    @Default(5) Spacing rodSpacing,
    @Default(3) Count numberOfWells,
    @Default(2) int rodsPerSide,
    @Default(0.0) double meshArea,
    @Default(0.0) double meshPerimeter,
    @Default(0.5) double plateSide,

    // ---------------- Step‑6/7 -------------------------------------------
    @Default(true) bool isClosedLoop,

    // ---------------- Step‑8  Drilling depth -----------------------------
    // @Default(0.6) Depth drillingDepth,

    // ---------------- Wenner soil-test raw readings ----------------------
    @Default(<List<double>>[]) List<List<double>> wennerRReadings,
    @Default(<List<double>>[]) List<List<double>> wennerXReadings,
    @Default(<String>[]) List<String> wennerZoneAliases,

    // ---------------- Encased rod extras (Clause 9.5.7) -------------------
    // When `rodEncased` is true the encased-rod resistance formulas are
    // used instead of the plain rod equations.  Both extra parameters must
    // be supplied in that case.
    @Default(false) bool rodEncased,
    SoilResistivity? enhancementResistivity, // ρc — Ω·m of infill material
    Diameter? infillDiameter,               // D  — outer diameter of infill (m)

    // ---------------- Manual cable overrides -----------------------------
    // Allow expert users to bypass the auto-selection of cable CSA/length
    // so that the bloc can recreate hand-picked spreadsheet examples.
    StandardCableSize? manualCableSize,
    Length? manualCableLength,
  }) = _EarthingCalculationInput;

  // Quick helpers
  bool get isMesh => arrangement == EarthingArrangement.meshGrid;
  bool get isPlate => arrangement == EarthingArrangement.plate;
  bool get isRodSet => arrangement.name.contains('Rod');
  Diameter get rodDiameter => customRodDiameter ?? rodType.use;

  @override
  String toString() {
    return '''Input:
  soilResistivity        = $soilResistivity Ω·m
  targetTotalResistance  = $targetTotalResistance Ω
  rodType                = $rodType
  customRodDiameter      = $customRodDiameter
  rodLength              = $rodLength m
  systemVoltage          = $systemVoltage
  faultCurrentA          = $faultCurrentA A
  faultDurationS         = $faultDurationS s
  conductorMaterial      = $conductorMaterial
  initialTempC           = $initialTempC °C
  finalTempC             = $finalTempC °C
  burialDepth            = $burialDepth m
  arrangement            = $arrangement
  conductorShape         = $conductorShape
  rodSpacing             = $rodSpacing m
  numberOfWells          = $numberOfWells
  rodsPerSide            = $rodsPerSide
  meshArea               = $meshArea m²
  meshPerimeter          = $meshPerimeter m
  plateSide              = $plateSide m
  isClosedLoop           = $isClosedLoop
  drillingDepth          = drillingDepth m
  wennerRReadings        = $wennerRReadings
  wennerXReadings        = $wennerXReadings
  rodEncased             = $rodEncased
  enhancementResistivity = $enhancementResistivity Ω·m
  infillDiameter         = $infillDiameter m
  manualCableSize        = $manualCableSize
  manualCableLength      = $manualCableLength m''';
  }
}
