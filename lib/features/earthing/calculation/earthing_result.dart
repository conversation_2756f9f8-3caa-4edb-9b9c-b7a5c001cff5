// earthing_result.dart
// ---------------------------------------------------------------------------

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';

part 'earthing_result.freezed.dart';

@freezed
abstract class EarthingCalculationResult with _$EarthingCalculationResult {
  const EarthingCalculationResult._();
  const factory EarthingCalculationResult({
    // --- Core outputs ------------------------------------------------------
    required double rodResistance,
    required double cableResistance,
    required double totalResistance,
    required bool meetsTarget,

    // ADD: optional single-rod resistance when parallel arrays are derived
    double? singleRodResistance,

    // --- Cable sizing extras ---------------------------------------------
    StandardCableSize? chosenCableSize,
    double? requiredCsaMm2,

    // --- Rod sizing advice (from cable table) ----------------------------
    Count? suggestedRodCount,
    Length? suggestedRodLen,

    // --- Extra -------------------------------------------------------------
    double? soilResistivityUsed,
    Diameter? cableDiameterUsed,
    double? lambda,
    double? conductorLength,
  }) = _EarthingCalculationResult;

  @override
  String toString() =>
      '''Result:
  rodResistance        = ${rodResistance.toStringAsFixed(4)} Ω
  cableResistance      = ${cableResistance.toStringAsFixed(4)} Ω
  totalResistance      = ${totalResistance.toStringAsFixed(4)} Ω
  meetsTarget          = $meetsTarget
  singleRodResistance  = $singleRodResistance Ω
  chosenCableSize      = $chosenCableSize
  requiredCsaMm2       = $requiredCsaMm2 mm²
  suggestedRodCount    = $suggestedRodCount
  suggestedRodLen      = $suggestedRodLen m
  soilResistivityUsed  = $soilResistivityUsed Ω·m
  cableDiameterUsed    = $cableDiameterUsed m
  lambda               = $lambda
  conductorLength      = $conductorLength m''';
}
