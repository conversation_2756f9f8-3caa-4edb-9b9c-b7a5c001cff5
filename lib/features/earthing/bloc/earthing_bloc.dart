import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';
import 'package:pis_core/features/earthing/calculation/earthing_result.dart';
import 'dart:math' as math;
import 'dart:developer' as developer;

part 'earthing_state.dart';
part 'earthing_event.dart';
part 'earthing_bloc.freezed.dart';

// earthing_bloc.dart
// ---------------------------------------------------------------------------
class EarthingBloc extends Bloc<EarthingBlocEvent, EarthingBlocState> {
  EarthingBloc() : super(const EarthingBlocState()) {
    on<UpdateInput>(_onUpdateInput);
  }

  // -------------------------------------------------------------------------
  void _onUpdateInput(UpdateInput e, Emitter<EarthingBlocState> emit) {
    final res = _compute(e.input);
    emit(state.copyWith(input: e.input, result: res));
  }

  // -------------------------------------------------------------------------
  EarthingCalculationResult _compute(EarthingCalculationInput i) {
    // If Wenner reading lists are provided, derive ρ from them (per-zone
    // averages then overall mean) and override the direct soilResistivity.
    double rhoUsed;
    if (i.wennerRReadings.isNotEmpty && i.wennerXReadings.isNotEmpty) {
      final zoneCnt = math.min(i.wennerRReadings.length, i.wennerXReadings.length);
      final perZoneAvg = <double>[];
      for (var z = 0; z < zoneCnt; z++) {
        final r = i.wennerRReadings[z];
        final x = i.wennerXReadings[z];
        if (r.length != x.length || r.isEmpty) continue;
        perZoneAvg.add(averageWennerRho(resistancesOhm: r, spacingsM: x));
      }
      if (perZoneAvg.isNotEmpty) {
        rhoUsed = perZoneAvg.reduce((a, b) => a + b) / perZoneAvg.length;
      } else {
        rhoUsed = i.soilResistivity;
      }
    } else {
      rhoUsed = i.soilResistivity;
    }

    // ---------------- Step-5  Electrode resistance ------------------------
    double? singleRod; // capture R₁ when available

    final rRod = () {
      // Handle encased rods first --------------------------------------
      if (i.rodEncased) {
        // Encased rods logic unchanged
        if (i.arrangement == EarthingArrangement.parallelRodLine) {
          // Compute both single-rod (encased) and multi-rod values so that
          // the UI/log can always show the one-rod baseline.
          singleRod = calcSingleRodEncased(
            rho: rhoUsed,
            rhoC: i.enhancementResistivity!,
            L: i.rodLength,
            d: i.rodDiameter,
            infillD: i.infillDiameter!,
          );
          return calcParallelRodLineEncased(
            rho: rhoUsed,
            rhoC: i.enhancementResistivity!,
            L: i.rodLength,
            d: i.rodDiameter,
            infillD: i.infillDiameter!,
            s: i.rodSpacing,
            n: i.numberOfWells,
          );
        } else {
          singleRod = calcSingleRodEncased(
            rho: rhoUsed,
            rhoC: i.enhancementResistivity!,
            L: i.rodLength,
            d: i.rodDiameter,
            infillD: i.infillDiameter!,
          );
          return singleRod!; // only one rod
        }
      }

      // ---- Plain (non-encased) rods -----------------------------------
      switch (i.arrangement) {
        case EarthingArrangement.singleRod:
          singleRod = calcSingleRod(rho: rhoUsed, L: i.rodLength, d: i.rodDiameter);
          return singleRod!;

        case EarthingArrangement.parallelRodLine:
          // Capture the single-rod resistance and rely on the standard
          // BS-7430 expression for the multi-rod value to ensure accuracy.
          singleRod = calcSingleRod(rho: rhoUsed, L: i.rodLength, d: i.rodDiameter);
          return calcParallelRodLine(
            rho: rhoUsed,
            L: i.rodLength,
            d: i.rodDiameter,
            s: i.rodSpacing,
            n: i.numberOfWells,
          );

        case EarthingArrangement.triangleRod:
          return calcTriangleRod(
            rho: rhoUsed,
            L: i.rodLength,
            d: i.rodDiameter,
            side: i.rodSpacing,
          );

        case EarthingArrangement.hollowSquareRod:
          return calcHollowSquareRod(
            rho: rhoUsed,
            L: i.rodLength,
            d: i.rodDiameter,
            s: i.rodSpacing,
            rodsPerSide: i.rodsPerSide,
          );

        case EarthingArrangement.buriedStraight:
          return calcBuriedStraight(
            rho: rhoUsed,
            L: i.rodLength,
            h: i.burialDepth,
            d: i.rodDiameter,
            shape: i.conductorShape,
          );

        case EarthingArrangement.buriedLShape:
          return calcBuriedLShape(
            rho: rhoUsed,
            L: i.rodLength,
            h: i.burialDepth,
            d: i.rodDiameter,
            shape: i.conductorShape,
          );

        case EarthingArrangement.buriedStar:
          return calcBuriedStar(
            rho: rhoUsed,
            L: i.rodLength,
            h: i.burialDepth,
            d: i.rodDiameter,
            shape: i.conductorShape,
          );

        case EarthingArrangement.buriedCruciform:
          return calcBuriedCruciform(
            rho: rhoUsed,
            L: i.rodLength,
            h: i.burialDepth,
            d: i.rodDiameter,
            shape: i.conductorShape,
          );

        case EarthingArrangement.meshGrid:
          return calcMeshGrid(
            rho: rhoUsed,
            area: i.meshArea,
            perimeter: i.meshPerimeter,
          );

        case EarthingArrangement.plate:
          return calcPlate(
            rho: rhoUsed,
            side: i.plateSide,
          );
      }
    }();

    // For cable sizing we default to a straight buried conductor irrespective
    // of the rod arrangement (per user requirement).
    const arrK1 = CableArrangementK1.straight;

    // ---------------- Step-7  Cable length --------------------------------
    final cableLen = i.manualCableLength ?? calcCableLength(
      isClosedLoop: i.isClosedLoop,
      wells: i.numberOfWells,
      spacing: i.rodSpacing,
    );

    // Print all cable resistance related inputs
    developer.log('--- Cable Resistance Calculation Inputs ---');
    developer.log('manualCableSize:      \\${i.manualCableSize}');
    developer.log('manualCableLength:    \\${i.manualCableLength}');
    developer.log('cableLen (used):      \\$cableLen');
    developer.log('conductorMaterial:    \\${i.conductorMaterial}');
    developer.log('systemVoltage:        \\${i.systemVoltage}');
    developer.log('faultCurrentA:        \\${i.faultCurrentA}');
    developer.log('faultDurationS:       \\${i.faultDurationS}');
    developer.log('initialTempC:         \\${i.initialTempC}');
    developer.log('finalTempC:           \\${i.finalTempC}');
    developer.log('arrangement:          \\${i.arrangement}');
    developer.log('conductorShape:       \\${i.conductorShape}');
    developer.log('arrK1:                \\$arrK1');
    developer.log('arrK1 value:          \\${arrK1.of(i.conductorShape)}');
  
    // ---------------- Step-3  Cable sizing / resistance -------------------
    StandardCableSize? chosenSize;
    double? requiredCsa;
    double? kConst;
    double? k1Val;

    // 1) Resolve cable sizing (manual or auto)
    if (i.manualCableSize != null) {
      chosenSize = i.manualCableSize!;
      requiredCsa = chosenSize.areaMm2.toDouble();
    } else {
      final auto = calcCableResistanceAuto(
        length: cableLen,
        faultCurrentA: i.faultCurrentA,
        voltage: i.systemVoltage,
        material: i.conductorMaterial,
        faultDurationS: i.faultDurationS,
        arrangement: arrK1,
        conductorShape: i.conductorShape,
        initialTempC: i.initialTempC,
        finalTempC: i.finalTempC,
      );
      chosenSize = auto.chosen;
      requiredCsa = auto.requiredCsa;
    }

    // Common constants for debug even when manual
    kConst = calcKConstant(
      material: i.conductorMaterial,
      initialTempC: i.initialTempC,
      finalTempC: i.finalTempC,
    );
    k1Val = arrK1.of(i.conductorShape);
  
    // 2) Calculate cable-to-earth resistance – always use buried-straight
    final rCable = calcBuriedStraight(
      rho: rhoUsed,
      L: cableLen,
      h: i.burialDepth,
      d: chosenSize.diameterM,
      shape: i.conductorShape,
    );

    developer.log('--- Cable Sizing Outputs ---');
    developer.log('requiredCsa:          \\$requiredCsa mm²');
    developer.log('chosenCableSize:      \\$chosenSize');
    developer.log('chosenCableSize.area: \\${chosenSize.areaMm2} mm²');
    developer.log('chosenCableSize.diam: \\${chosenSize.diameterM} m');
    developer.log('kConst (K):           \\$kConst');
    developer.log('k1 (arrangement K1):  \\$k1Val');
    developer.log('cableLen (used):      \\$cableLen m');
    developer.log('rCable (output):      \\$rCable Ω');
    if (singleRod != null) {
      developer.log('One Rod Resistance:   \\$singleRod Ω');
    }
    developer.log('Rod Array Resistance: \\$rRod Ω');

    // Log total system resistance after combination
    final total = calcTotalParallel(rRods: rRod, rCable: rCable);
    developer.log('Total Resistance:     \\$total Ω');

    // Lambda (group factor) only for certain rod arrangements
    double? lambda;
    if (i.arrangement == EarthingArrangement.parallelRodLine) {
      // replicate lambda calc
      double l = 0;
      for (var idx = 2; idx <= i.numberOfWells; idx++) {
        l += 1 / idx;
      }
      lambda = l * 2;
      developer.log('lambda (parallelRodLine): \\$lambda');
    } else if (i.arrangement == EarthingArrangement.hollowSquareRod) {
      // approximate lambda using helper from formulas
      lambda = calcLambdaSquare(i.rodsPerSide);
      developer.log('lambda (hollowSquareRod):  \\$lambda');
    }

    // ---------------- Step-9  Total resistance ----------------------------
    final ok = total <= i.targetTotalResistance;

    return EarthingCalculationResult(
      rodResistance: rRod,
      cableResistance: rCable,
      totalResistance: total,
      meetsTarget: ok,
      singleRodResistance: singleRod,
      soilResistivityUsed: rhoUsed,
      chosenCableSize: chosenSize,
      requiredCsaMm2: requiredCsa,
      suggestedRodCount: chosenSize.rods,
      suggestedRodLen: chosenSize.rodLen,
      cableDiameterUsed: chosenSize.diameterM,
      lambda: lambda,
      conductorLength: cableLen,
    );
  }
}
