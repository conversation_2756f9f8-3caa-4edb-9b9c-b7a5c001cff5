part of 'earthing_bloc.dart';

/// The single state for the EarthingBloc, holding all input and result data.
// earthing_state.dart
// ---------------------------------------------------------------------------
// *Single* state object that carries both the current input and the latest
// result ­— exactly like your original pattern, but with new model names.
// ---------------------------------------------------------------------------

@freezed
abstract class EarthingBlocState with _$EarthingBlocState {
  const EarthingBlocState._();
  const factory EarthingBlocState({
    @Default(EarthingCalculationInput())  EarthingCalculationInput  input,
    @Default(EarthingCalculationResult(
      rodResistance: 0,
      cableResistance: 0,
      totalResistance: 0,
      meetsTarget: false,
    )) EarthingCalculationResult result,
  }) = _EarthingBlocState;

  @override
  String toString() => 'State:\n$input\n$result';
}