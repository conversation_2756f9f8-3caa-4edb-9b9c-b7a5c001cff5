import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/services/report_to_pdf_service.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/core/widgets/report_preview/common_report_success_dialog.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';
import 'package:pis_core/features/earthing/calculation/earthing_result.dart';
import 'package:pis_core/features/earthing/ui/report_preview/widgets/earthing_report_header_section.dart';
import 'package:pis_core/features/earthing/ui/report_preview/widgets/earthing_project_info_section.dart';
import 'package:pis_core/features/earthing/ui/report_preview/widgets/earthing_executive_summary_section.dart';
import 'package:pis_core/features/earthing/ui/report_preview/widgets/earthing_input_parameters_section.dart';
import 'package:pis_core/features/earthing/ui/report_preview/widgets/earthing_calculation_results_section.dart';
import 'package:pis_core/features/earthing/ui/report_preview/widgets/earthing_equations_section.dart';
import 'package:pis_core/features/earthing/ui/report_preview/widgets/earthing_technical_notes_section.dart';
import 'dart:developer' as developer;

class EarthingReportPreviewPage extends StatefulWidget {
  const EarthingReportPreviewPage({super.key});

  @override
  State<EarthingReportPreviewPage> createState() => _EarthingReportPreviewPageState();
}

class _EarthingReportPreviewPageState extends State<EarthingReportPreviewPage> {
  bool _isGeneratingPdf = false;
  final GlobalKey _repaintBoundaryKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text('Earthing System Report Preview'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        elevation: 0,
        actions: [
          BlocBuilder<EarthingBloc, EarthingBlocState>(
            builder: (context, state) {
              // Check if we have meaningful calculation results
              if (state.result.totalResistance == 0) {
                return const SizedBox();
              }

              return Padding(
                padding: const EdgeInsets.only(right: 16),
                child: ElevatedButton.icon(
                  onPressed: _isGeneratingPdf ? null : () => _generatePdf(state.input, state.result),
                  icon: _isGeneratingPdf
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.picture_as_pdf),
                  label: Text(_isGeneratingPdf ? 'Generating...' : 'Generate PDF'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryRed,
                    foregroundColor: AppColors.white,
                    elevation: 2,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: BlocBuilder<EarthingBloc, EarthingBlocState>(
        builder: (context, state) {
          if (state.result.totalResistance == 0) {
            return const Center(
              child: Text('No calculation results available'),
            );
          }

          return _buildReportPreview(state.input, state.result);
        },
      ),
    );
  }

  Widget _buildReportPreview(EarthingCalculationInput input, EarthingCalculationResult result) {
    return Container(
      color: Colors.grey[100],
      child: Center(
        child: Container(
          width: 800, // A4-like width
          margin: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: RepaintBoundary(
              key: _repaintBoundaryKey,
              child: Container(
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Report Header
                    EarthingReportHeaderSection(),

                    const SizedBox(height: 30),

                    // Project Information
                    EarthingProjectInfoSection(input: input),

                    const SizedBox(height: 30),

                    // Executive Summary
                    EarthingExecutiveSummarySection(result: result),

                    const SizedBox(height: 30),
                
                // Input Parameters
                EarthingInputParametersSection(input: input),
                
                const SizedBox(height: 30),
                
                // Calculation Results
                EarthingCalculationResultsSection(result: result),

                const SizedBox(height: 30),

                // Calculation Equations
                EarthingEquationsSection(input: input),

                const SizedBox(height: 30),

                    // Technical Notes
                    EarthingTechnicalNotesSection(input: input, result: result),

                    const SizedBox(height: 50),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _generatePdf(EarthingCalculationInput input, EarthingCalculationResult result) async {
    if (_isGeneratingPdf) return;

    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      // Generate PDF from the exact preview widget
      final File reportFile = await ReportToPdfService.captureWidgetAsPdf(
        repaintBoundaryKey: _repaintBoundaryKey,
        filename: ReportToPdfService.generateFilename('Earthing_System'),
      );

      if (mounted) {
        CommonReportSuccessDialog.show(context, reportFile, 'Earthing System Calculation Report');
      }
    } catch (e, stackTrace) {
      developer.log(
        'Error generating earthing PDF report',
        error: e,
        stackTrace: stackTrace,
        name: 'EarthingReportPreview',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to generate PDF report. Please try again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      setState(() {
        _isGeneratingPdf = false;
      });
    }
  }




}
