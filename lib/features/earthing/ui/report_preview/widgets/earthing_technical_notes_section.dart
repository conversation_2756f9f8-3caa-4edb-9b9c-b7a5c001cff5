import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';
import 'package:pis_core/features/earthing/calculation/earthing_result.dart';

class EarthingTechnicalNotesSection extends StatelessWidget {
  final EarthingCalculationInput input;
  final EarthingCalculationResult result;

  const EarthingTechnicalNotesSection({
    super.key,
    required this.input,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.notes, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'TECHNICAL NOTES & RECOMMENDATIONS',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Technical Notes Content
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryBlue, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Methodology
                _buildNoteSection(
                  context,
                  'CALCULATION METHODOLOGY',
                  'This earthing system calculation follows BS 7430:2011 standard.',
                  Icons.science,
                  AppColors.primaryBlue,
                ),
                
                const SizedBox(height: 20),
                
                // Rod Resistance Calculation
                _buildNoteSection(
                  context,
                  'ROD RESISTANCE CALCULATION',
                  '• Method: ${input.arrangement.name}\n'
                  '• Formula: Based on BS 7430 Clause 9.5\n'
                  '• Single rod resistance: ${result.singleRodResistance?.toStringAsFixed(4) ?? 'N/A'} Ω\n'
                  '• Total rod resistance: ${result.rodResistance.toStringAsFixed(4)} Ω',
                  Icons.electrical_services,
                  AppColors.primaryRed,
                ),
                
                const SizedBox(height: 20),
                
                // Cable Sizing
                _buildNoteSection(
                  context,
                  'CABLE SIZING',
                  '• Adiabatic equation per BS 7430 Clause 9.7\n'
                  '• Material: ${input.conductorMaterial.name}\n'
                  '• Temperature rise: ${input.initialTempC}°C to ${input.finalTempC}°C\n'
                  '• Required CSA: ${result.requiredCsaMm2?.toStringAsFixed(2) ?? 'N/A'} mm²\n'
                  '• Chosen cable size: ${result.chosenCableSize?.areaMm2.toString() ?? 'N/A'} mm²',
                  Icons.cable,
                  Colors.orange,
                ),
                
                const SizedBox(height: 20),
                
                // System Configuration
                _buildNoteSection(
                  context,
                  'SYSTEM CONFIGURATION',
                  '• Rod length: ${input.rodLength} m\n'
                  '• Rod diameter: ${input.rodDiameter.toStringAsFixed(4)} m\n'
                  '• Burial depth: ${input.burialDepth} m\n'
                  '• Soil resistivity: ${input.soilResistivity} Ω·m\n'
                  '• Conductor length: ${result.conductorLength?.toStringAsFixed(2) ?? 'N/A'} m',
                  Icons.settings,
                  Colors.purple,
                ),
                
                const SizedBox(height: 20),
                
                // Compliance Result
                _buildHighlightedResult(context),
                
                const SizedBox(height: 20),
                
                // Safety Considerations
                _buildSafetyConsiderations(context),
                
                const SizedBox(height: 20),
                
                // Installation Recommendations
                _buildInstallationRecommendations(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteSection(
    BuildContext context,
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 18),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.darkGrey,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHighlightedResult(BuildContext context) {
    final Color resultColor = result.meetsTarget ? Colors.green : Colors.red;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: resultColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: resultColor, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                result.meetsTarget ? Icons.check_circle : Icons.warning,
                color: resultColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'CALCULATION RESULT',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: resultColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            result.meetsTarget 
                ? 'The earthing system design MEETS the target resistance requirement.'
                : 'The earthing system design does NOT MEET the target resistance requirement.',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: resultColor,
              fontWeight: FontWeight.w600,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: resultColor.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Target: Target Met',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.darkGrey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  'Achieved: ${result.totalResistance.toStringAsFixed(4)} Ω',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: resultColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSafetyConsiderations(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.security, color: Colors.orange, size: 18),
              const SizedBox(width: 8),
              Text(
                'SAFETY CONSIDERATIONS',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildSafetyItem(context, 'All calculations include appropriate safety factors'),
          _buildSafetyItem(context, 'Installation must comply with local electrical codes'),
          _buildSafetyItem(context, 'Regular testing and maintenance required'),
          _buildSafetyItem(context, 'Proper grounding of all electrical equipment essential'),
        ],
      ),
    );
  }

  Widget _buildInstallationRecommendations(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.construction, color: Colors.grey[600], size: 18),
              const SizedBox(width: 8),
              Text(
                'INSTALLATION RECOMMENDATIONS',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.grey[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (result.meetsTarget) ...[
            _buildRecommendationItem(context, 'Design meets target resistance requirements'),
            _buildRecommendationItem(context, 'Proceed with installation as calculated'),
          ] else ...[
            _buildRecommendationItem(context, 'Consider increasing number of rods to ${result.suggestedRodCount}'),
            _buildRecommendationItem(context, 'Consider increasing rod length to ${result.suggestedRodLen ?? 'N/A'} m'),
            _buildRecommendationItem(context, 'Review soil resistivity measurements'),
          ],
          _buildRecommendationItem(context, 'Professional installation recommended'),
          _buildRecommendationItem(context, 'Test system resistance after installation'),
        ],
      ),
    );
  }

  Widget _buildSafetyItem(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '⚠ ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.orange,
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.darkGrey,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationItem(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[700],
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
