import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/calculation/earthing_result.dart';

class EarthingCalculationResultsSection extends StatelessWidget {
  final EarthingCalculationResult result;

  const EarthingCalculationResultsSection({
    super.key,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryRed,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calculate,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'CALCULATION RESULTS',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Results Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryRed, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              children: [
                // Key Results
                _buildCategoryHeader(context, 'Key Calculation Results'),
                _buildResultRow(
                  context,
                  'Total Resistance',
                  '${result.totalResistance.toStringAsFixed(4)} Ω',
                  isHighlighted: true,
                  statusColor: result.meetsTarget ? Colors.green : Colors.red,
                ),
                _buildResultRow(
                  context,
                  'Rod Resistance',
                  '${result.rodResistance.toStringAsFixed(4)} Ω',
                  isHighlighted: true,
                ),
                _buildResultRow(
                  context,
                  'Cable Resistance',
                  '${result.cableResistance.toStringAsFixed(4)} Ω',
                  isHighlighted: true,
                ),
                _buildResultRow(
                  context,
                  'Meets Target',
                  result.meetsTarget ? 'YES ✓' : 'NO ✗',
                  isHighlighted: true,
                  statusColor: result.meetsTarget ? Colors.green : Colors.red,
                ),
                _buildResultRow(
                  context,
                  'Compliance Status',
                  result.meetsTarget ? 'COMPLIANT' : 'NON-COMPLIANT',
                  statusColor: result.meetsTarget ? Colors.green : Colors.red,
                ),
                
                // Design Parameters
                _buildCategoryHeader(context, 'Design Parameters'),
                _buildResultRow(
                  context,
                  'Single Rod Resistance',
                  result.singleRodResistance != null ? '${result.singleRodResistance!.toStringAsFixed(4)} Ω' : 'N/A',
                ),
                _buildResultRow(
                  context,
                  'Chosen Cable Size',
                  result.chosenCableSize?.areaMm2.toString() ?? 'N/A',
                ),
                _buildResultRow(
                  context,
                  'Required CSA',
                  result.requiredCsaMm2 != null ? '${result.requiredCsaMm2!.toStringAsFixed(2)} mm²' : 'N/A',
                ),
                _buildResultRow(
                  context,
                  'Suggested Rod Count',
                  result.suggestedRodCount?.toString() ?? 'N/A',
                ),
                _buildResultRow(
                  context,
                  'Suggested Rod Length',
                  result.suggestedRodLen != null ? '${result.suggestedRodLen} m' : 'N/A',
                ),
                
                // Calculation Details
                _buildCategoryHeader(context, 'Calculation Details'),
                _buildResultRow(
                  context,
                  'Soil Resistivity Used',
                  result.soilResistivityUsed != null ? '${result.soilResistivityUsed!.toStringAsFixed(2)} Ω·m' : 'N/A',
                ),
                _buildResultRow(
                  context,
                  'Cable Diameter Used',
                  result.cableDiameterUsed != null ? '${result.cableDiameterUsed!.toStringAsFixed(4)} m' : 'N/A',
                ),
                _buildResultRow(
                  context,
                  'Conductor Length',
                  result.conductorLength != null ? '${result.conductorLength!.toStringAsFixed(2)} m' : 'N/A',
                ),
                
                // Standards and Methods
                _buildCategoryHeader(context, 'Standards and Methods'),
                _buildResultRow(
                  context,
                  'Standard',
                  'BS 7430:2011',
                ),
                _buildResultRow(
                  context,
                  'Calculation Method',
                  'Clause 9.5 - Electrode Resistance',
                ),
                _buildResultRow(
                  context,
                  'Cable Sizing Method',
                  'Clause 9.7 - Adiabatic Equation',
                  isLast: true,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Compliance Summary Card
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: _getComplianceColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: _getComplianceColor(), width: 2),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _getComplianceIcon(),
                      color: _getComplianceColor(),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'DESIGN COMPLIANCE SUMMARY',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: _getComplianceColor(),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  result.meetsTarget
                      ? 'The calculated earthing system design meets the target resistance requirement. The total system resistance is ${result.totalResistance.toStringAsFixed(4)} Ω.'
                      : 'The calculated earthing system design does NOT meet the target resistance requirement. The total system resistance is ${result.totalResistance.toStringAsFixed(4)} Ω. Consider increasing the number of rods or rod length.',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: _getComplianceColor(),
                    fontWeight: FontWeight.w600,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'All calculations follow BS 7430:2011 standard for earthing system design and cable sizing using the adiabatic equation.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.darkGrey,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryHeader(BuildContext context, String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primaryRed.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: AppColors.primaryRed.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppColors.primaryRed,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildResultRow(
    BuildContext context,
    String parameter,
    String value, {
    bool isHighlighted = false,
    bool isLast = false,
    Color? statusColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isHighlighted 
            ? AppColors.primaryRed.withValues(alpha: 0.03)
            : Colors.transparent,
        border: isLast 
            ? null 
            : Border(
                bottom: BorderSide(
                  color: AppColors.primaryRed.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                parameter,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGrey,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                padding: statusColor != null 
                    ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
                    : EdgeInsets.zero,
                decoration: statusColor != null
                    ? BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: statusColor, width: 1),
                      )
                    : null,
                child: Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: statusColor ?? (isHighlighted ? AppColors.primaryRed : AppColors.darkGrey),
                    fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getComplianceColor() {
    return result.meetsTarget ? Colors.green : Colors.red;
  }

  IconData _getComplianceIcon() {
    return result.meetsTarget ? Icons.check_circle : Icons.warning;
  }
}
