import 'package:flutter/material.dart';
import 'package:flutter_math_fork/flutter_math.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/core/widgets/report_preview/common_section_container.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';
import 'package:pis_core/features/earthing/ui/equations.dart';

class EarthingEquationsSection extends StatelessWidget {
  final EarthingCalculationInput input;

  const EarthingEquationsSection({
    super.key,
    required this.input,
  });

  @override
  Widget build(BuildContext context) {
    return CommonSectionContainer(
      title: 'CALCULATION EQUATIONS',
      icon: Icons.functions,
      headerColor: AppColors.primaryGreen,
      borderColor: AppColors.primaryGreen,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main equation for the selected arrangement
              _buildEquationCard(context),
              
              const SizedBox(height: 20),
              
              // Additional formulas used
              _buildAdditionalFormulas(context),
              
              const SizedBox(height: 20),
              
              // Variable definitions
              _buildVariableDefinitions(context),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEquationCard(BuildContext context) {
    final equation = eqMap[input.arrangement];
    if (equation == null) return const SizedBox();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primaryGreen.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primaryGreen.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calculate,
                color: AppColors.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Primary Resistance Equation',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Arrangement: ${input.arrangement.label}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Standard Reference: ${equation.$2}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 16),
          // Display the LaTeX equation
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Center(
              child: Math.tex(
                equation.$1,
                textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontSize: 18,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalFormulas(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Additional Formulas Used',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primaryBlue,
          ),
        ),
        const SizedBox(height: 12),
        
        // Cable sizing formula
        _buildFormulaRow(
          context,
          'Cable Sizing (Adiabatic Equation)',
          r'S = \sqrt{\frac{I^2 \cdot t}{k^2}}',
          'BS 7430 Clause 9.7',
        ),
        
        const SizedBox(height: 12),
        
        // Wenner formula (if applicable)
        if (input.wennerRReadings.isNotEmpty)
          _buildFormulaRow(
            context,
            'Soil Resistivity (Wenner Method)',
            r'\rho = 2\pi \cdot X \cdot R',
            'BS 7430 Clause 9.4',
          ),
      ],
    );
  }

  Widget _buildFormulaRow(BuildContext context, String title, String latex, String reference) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.functions,
                color: AppColors.primaryBlue,
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            reference,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Math.tex(
              latex,
              textStyle: Theme.of(context).textTheme.bodyLarge,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVariableDefinitions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Variable Definitions',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primaryBlue,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildVariableRow('R', 'Resistance (Ω)'),
              _buildVariableRow('ρ', 'Soil resistivity (Ω·m)'),
              _buildVariableRow('L', 'Rod length (m)'),
              _buildVariableRow('d', 'Rod diameter (m)'),
              _buildVariableRow('n', 'Number of rods'),
              _buildVariableRow('S', 'Cable cross-sectional area (mm²)'),
              _buildVariableRow('I', 'Fault current (A)'),
              _buildVariableRow('t', 'Fault duration (s)'),
              _buildVariableRow('k', 'Material constant'),
              if (input.wennerRReadings.isNotEmpty) ...[
                _buildVariableRow('X', 'Electrode spacing (m)'),
                _buildVariableRow('R', 'Measured resistance (Ω)'),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVariableRow(String variable, String definition) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 30,
            child: Math.tex(
              variable,
              textStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            '= ',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Expanded(
            child: Text(
              definition,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
