import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/calculation/earthing_result.dart';

class EarthingExecutiveSummarySection extends StatelessWidget {
  final EarthingCalculationResult result;

  const EarthingExecutiveSummarySection({
    super.key,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryRed,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.summarize,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'EXECUTIVE SUMMARY',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Resistance Summary
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryRed, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Resistance Achievement Card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _getComplianceColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _getComplianceColor(), width: 2),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getComplianceIcon(),
                        color: _getComplianceColor(),
                        size: 32,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Total Resistance: ${result.totalResistance.toStringAsFixed(4)} Ω',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                color: _getComplianceColor(),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              result.meetsTarget ? 'TARGET ACHIEVED' : 'TARGET NOT MET',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: _getComplianceColor(),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Summary Table
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!, width: 1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      _buildSummaryRow(
                        context,
                        'Total Resistance',
                        '${result.totalResistance.toStringAsFixed(4)} Ω',
                        isHighlighted: true,
                      ),
                      _buildSummaryRow(
                        context,
                        'Target Resistance',
                        'Target Met',
                        isHighlighted: true,
                      ),
                      _buildSummaryRow(
                        context,
                        'Rod Resistance',
                        '${result.rodResistance.toStringAsFixed(4)} Ω',
                      ),
                      _buildSummaryRow(
                        context,
                        'Cable Resistance',
                        '${result.cableResistance.toStringAsFixed(4)} Ω',
                      ),
                      _buildSummaryRow(
                        context,
                        'Compliance Status',
                        result.meetsTarget ? 'COMPLIANT' : 'NON-COMPLIANT',
                        isLast: true,
                        statusColor: result.meetsTarget ? Colors.green : Colors.red,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Design Summary Box
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.engineering,
                            color: AppColors.primaryBlue,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'DESIGN SUMMARY',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildDesignItem(context, 'Suggested Rod Count', '${result.suggestedRodCount}'),
                      _buildDesignItem(context, 'Suggested Rod Length', result.suggestedRodLen != null ? '${result.suggestedRodLen} m' : 'N/A'),
                      _buildDesignItem(context, 'Chosen Cable Size', result.chosenCableSize?.areaMm2.toString() ?? 'N/A'),
                      _buildDesignItem(context, 'Required CSA', result.requiredCsaMm2 != null ? '${result.requiredCsaMm2!.toStringAsFixed(2)} mm²' : 'N/A'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String parameter,
    String value, {
    bool isHighlighted = false,
    bool isLast = false,
    Color? statusColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isHighlighted 
            ? AppColors.primaryRed.withValues(alpha: 0.05)
            : Colors.transparent,
        border: isLast 
            ? null 
            : Border(
                bottom: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                parameter,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGrey,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                padding: statusColor != null 
                    ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
                    : EdgeInsets.zero,
                decoration: statusColor != null
                    ? BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: statusColor, width: 1),
                      )
                    : null,
                child: Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: statusColor ?? (isHighlighted ? AppColors.primaryRed : AppColors.darkGrey),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesignItem(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          Text(
            '• ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.primaryBlue,
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              '$label: $value',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.darkGrey,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getComplianceColor() {
    return result.meetsTarget ? Colors.green : Colors.red;
  }

  IconData _getComplianceIcon() {
    return result.meetsTarget ? Icons.check_circle : Icons.warning;
  }
}
