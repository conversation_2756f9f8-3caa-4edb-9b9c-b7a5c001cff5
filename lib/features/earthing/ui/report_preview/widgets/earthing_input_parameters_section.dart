import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';

class EarthingInputParametersSection extends StatelessWidget {
  final EarthingCalculationInput input;

  const EarthingInputParametersSection({
    super.key,
    required this.input,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.input,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'INPUT PARAMETERS',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Input Parameters Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryBlue, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              children: [
                // System Requirements
                _buildCategoryHeader(context, 'System Requirements'),
                _buildInputRow(
                  context,
                  'Target Resistance',
                  '${input.targetTotalResistance} Ω',
                  isImportant: true,
                ),
                _buildInputRow(
                  context,
                  'Soil Resistivity',
                  '${input.soilResistivity} Ω·m',
                  isImportant: true,
                ),
                _buildInputRow(
                  context,
                  'System Voltage',
                  input.systemVoltage.name.toUpperCase(),
                ),
                
                // Rod Configuration
                _buildCategoryHeader(context, 'Rod Configuration'),
                _buildInputRow(
                  context,
                  'Rod Length',
                  '${input.rodLength} m',
                  isImportant: true,
                ),
                _buildInputRow(
                  context,
                  'Rod Type',
                  _getRodTypeDescription(input.rodType),
                ),
                _buildInputRow(
                  context,
                  'Rod Diameter',
                  '${input.rodDiameter.toStringAsFixed(4)} m',
                ),
                _buildInputRow(
                  context,
                  'Arrangement',
                  input.arrangement.name,
                ),
                
                // Electrical Parameters
                _buildCategoryHeader(context, 'Electrical Parameters'),
                _buildInputRow(
                  context,
                  'Fault Current',
                  '${input.faultCurrentA} A',
                  isImportant: true,
                ),
                _buildInputRow(
                  context,
                  'Fault Duration',
                  '${input.faultDurationS} s',
                ),
                _buildInputRow(
                  context,
                  'Conductor Material',
                  input.conductorMaterial.name,
                ),
                _buildInputRow(
                  context,
                  'Initial Temperature',
                  '${input.initialTempC}°C',
                ),
                _buildInputRow(
                  context,
                  'Final Temperature',
                  '${input.finalTempC}°C',
                ),
                
                // Installation Details
                _buildCategoryHeader(context, 'Installation Details'),
                _buildInputRow(
                  context,
                  'Burial Depth',
                  '${input.burialDepth} m',
                ),
                _buildInputRow(
                  context,
                  'Conductor Shape',
                  input.conductorShape.name,
                ),
                if (input.arrangement != EarthingArrangement.singleRod) ...[
                  _buildInputRow(
                    context,
                    'Rod Spacing',
                    '${input.rodSpacing} m',
                  ),
                  _buildInputRow(
                    context,
                    'Number of Wells',
                    '${input.numberOfWells}',
                  ),
                  _buildInputRow(
                    context,
                    'Rods Per Side',
                    '${input.rodsPerSide}',
                  ),
                  _buildInputRow(
                    context,
                    'Is Closed Loop',
                    input.isClosedLoop ? 'Yes' : 'No',
                  ),
                ],
                _buildInputRow(
                  context,
                  'Rod Encased',
                  input.rodEncased ? 'Yes' : 'No',
                ),
                
                // Manual Overrides (if any)
                if (input.manualCableSize != null || input.manualCableLength != null) ...[
                  _buildCategoryHeader(context, 'Manual Overrides'),
                  if (input.manualCableSize != null)
                    _buildInputRow(
                      context,
                      'Manual Cable Size',
                      '${input.manualCableSize!.areaMm2} mm²',
                      isImportant: true,
                    ),
                  if (input.manualCableLength != null)
                    _buildInputRow(
                      context,
                      'Manual Cable Length',
                      '${input.manualCableLength} m',
                      isImportant: true,
                      isLast: true,
                    ),
                ] else
                  _buildInputRow(
                    context,
                    'Manual Overrides',
                    'None',
                    isLast: true,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryHeader(BuildContext context, String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: AppColors.primaryBlue.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppColors.primaryBlue,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildInputRow(
    BuildContext context,
    String parameter,
    String value, {
    bool isImportant = false,
    bool isLast = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isImportant 
            ? AppColors.primaryBlue.withValues(alpha: 0.03)
            : Colors.transparent,
        border: isLast 
            ? null 
            : Border(
                bottom: BorderSide(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                parameter,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGrey,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isImportant ? AppColors.primaryBlue : AppColors.darkGrey,
                  fontWeight: isImportant ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getRodTypeDescription(StandardRod rodType) {
    switch (rodType) {
      case StandardRod.rod5_8:
        return '5/8" (14.2 mm)';
      case StandardRod.rod3_4:
        return '3/4" (17.2 mm)';
    }
  }
}
