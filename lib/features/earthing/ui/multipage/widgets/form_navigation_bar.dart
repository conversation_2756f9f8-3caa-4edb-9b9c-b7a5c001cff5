import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/core/theme/theme_helper.dart';
import 'package:pis_core/features/earthing/ui/multipage/earthing_multipage_form.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/ui/report_preview/earthing_report_preview_page.dart';

class ValidationError {
  final String field;
  final String message;
  final int stepIndex;
  final String stepName;

  ValidationError({
    required this.field,
    required this.message,
    required this.stepIndex,
    required this.stepName,
  });
}

class FormNavigationBar extends StatefulWidget {
  const FormNavigationBar({
    super.key,
    required this.isFirstStep,
    required this.isLastStep,
    required this.onPrevious,
    required this.onNext,
    required this.currentStep,
    this.onNavigateToStep,
  });

  final bool isFirstStep;
  final bool isLastStep;
  final VoidCallback onPrevious;
  final VoidCallback onNext;
  final EarthingFormStep currentStep;
  final Function(int stepIndex)? onNavigateToStep;

  @override
  State<FormNavigationBar> createState() => _FormNavigationBarState();
}

class _FormNavigationBarState extends State<FormNavigationBar> {
  void _navigateToReportPreview(BuildContext context) {
    final earthingBloc = context.read<EarthingBloc>();
    final state = earthingBloc.state;

    // Validate required fields
    final validationResult = _validateRequiredFields(state.input);
    if (validationResult != null) {
      _showValidationDialog(context, validationResult);
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (newContext) => BlocProvider.value(
          value: earthingBloc,
          child: const EarthingReportPreviewPage(),
        ),
      ),
    );
  }

  ValidationError? _validateRequiredFields(input) {
    // Check project name
    if (input.projectName.isEmpty) {
      return ValidationError(
        field: 'Project Name',
        message: 'Project name is required for the report',
        stepIndex: 0,
        stepName: 'Soil & Target',
      );
    }

    // Check main constructor
    if (input.mainConstructor.isEmpty) {
      return ValidationError(
        field: 'Main Constructor',
        message: 'Main constructor name is required for the report',
        stepIndex: 0,
        stepName: 'Soil & Target',
      );
    }

    // Check target resistance
    if (input.targetTotalResistance <= 0) {
      return ValidationError(
        field: 'Target Resistance',
        message: 'Target resistance must be greater than 0',
        stepIndex: 0,
        stepName: 'Soil & Target',
      );
    }

    // Check soil resistivity
    if (input.soilResistivity <= 0) {
      return ValidationError(
        field: 'Soil Resistivity',
        message: 'Soil resistivity must be greater than 0',
        stepIndex: 0,
        stepName: 'Soil & Target',
      );
    }

    // Check rod length
    if (input.rodLength <= 0) {
      return ValidationError(
        field: 'Rod Length',
        message: 'Rod length must be greater than 0',
        stepIndex: 1,
        stepName: 'Rod Setup',
      );
    }

    // Check fault current
    if (input.faultCurrentA <= 0) {
      return ValidationError(
        field: 'Fault Current',
        message: 'Fault current must be greater than 0',
        stepIndex: 2,
        stepName: 'Fault & Cable',
      );
    }

    return null; // All validations passed
  }

  void _showValidationDialog(BuildContext context, ValidationError error) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange, size: 28),
              const SizedBox(width: 12),
              const Text('Missing Required Field'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('The following field is required to generate the report:'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Field: ${error.field}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text('Location: ${error.stepName}'),
                    const SizedBox(height: 4),
                    Text('Issue: ${error.message}'),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to the specific step
                _navigateToStep(error.stepIndex);
              },
              icon: const Icon(Icons.navigate_next),
              label: const Text('Go to Field'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }

  void _navigateToStep(int stepIndex) {
    if (widget.onNavigateToStep != null) {
      widget.onNavigateToStep!(stepIndex);
    } else {
      // Fallback message if navigation callback is not provided
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please navigate to step ${stepIndex + 1} to fill the required field'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: context.responsiveAllPadding(24),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Previous Button
          if (!widget.isFirstStep)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: widget.onPrevious,
                icon: const Icon(Icons.arrow_back),
                label: const Text('Previous'),
                style: OutlinedButton.styleFrom(
                  padding: context.responsiveSymmetricPadding(vertical: 16),
                  side: BorderSide(color: AppColors.primaryBlue, width: 2),
                  foregroundColor: AppColors.primaryBlue,
                ),
              ),
            )
          else
            const Expanded(child: SizedBox()),

          SizedBox(width: context.scaledFontSize(16)),

          // Next/Finish Button
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: widget.isLastStep
                  ? () => _navigateToReportPreview(context)
                  : widget.onNext,
              icon: Icon(widget.isLastStep ? Icons.preview : Icons.arrow_forward),
              label: Text(widget.isLastStep ? 'Preview Report' : 'Next Step'),
              style: ElevatedButton.styleFrom(
                padding: context.responsiveSymmetricPadding(vertical: 16),
                backgroundColor: widget.isLastStep ? AppColors.primaryRed : AppColors.primaryBlue,
                foregroundColor: AppColors.white,
                elevation: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
