import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/ui/soil_report_preview/soil_report_preview_page.dart';

class WennerZoneInterface extends HookWidget {
  const WennerZoneInterface({super.key});

  @override
  Widget build(BuildContext context) {
    final input = context.watch<EarthingBloc>().state.input;
    final zones = useState<List<WennerZone>>(
      _initializeZones(input.wennerRReadings, input.wennerXReadings, input.wennerZoneAliases)
    );

    void updateBloc() {
      final bloc = context.read<EarthingBloc>();
      final current = bloc.state.input;
      
      bloc.add(EarthingBlocEvent.updateInput(current.copyWith(
        wennerRReadings: zones.value.map((z) => z.rReadings).toList(),
        wennerXReadings: zones.value.map((z) => z.xReadings).toList(),
        wennerZoneAliases: zones.value.map((z) => z.alias).toList(),
      )));
    }

    void addZone() {
      zones.value = [
        ...zones.value,
        WennerZone(
          alias: 'Zone ${zones.value.length + 1}',
          rReadings: [0, 0, 0, 0],
          xReadings: [1.0, 2.0, 3.0, 4.0], // Standard Wenner spacings
        ),
      ];
      updateBloc();
    }

    void removeZone(int index) {
      if (zones.value.length > 1) {
        zones.value = [
          ...zones.value.sublist(0, index),
          ...zones.value.sublist(index + 1),
        ];
        updateBloc();
      }
    }

    void updateZone(int index, WennerZone updatedZone) {
      zones.value = [
        ...zones.value.sublist(0, index),
        updatedZone,
        ...zones.value.sublist(index + 1),
      ];
      updateBloc();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with add button
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Wenner Four-Pin Test Zones',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Add measurement zones with custom aliases for better organization',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.darkGrey,
                    ),
                  ),
                ],
              ),
            ),
            ElevatedButton.icon(
              onPressed: addZone,
              icon: const Icon(Icons.add, size: 18),
              label: const Text('Add Zone'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryRed,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Zone cards
        if (zones.value.isEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.outline, style: BorderStyle.solid),
            ),
            child: Column(
              children: [
                Icon(Icons.add_location_alt, size: 48, color: AppColors.mediumGrey),
                const SizedBox(height: 16),
                Text(
                  'No zones added yet',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.darkGrey,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Click "Add Zone" to start adding Wenner test measurements',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.mediumGrey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )
        else
          ...zones.value.asMap().entries.map((entry) {
            final index = entry.key;
            final zone = entry.value;
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: WennerZoneCard(
                zone: zone,
                zoneIndex: index,
                canDelete: zones.value.length > 1,
                onUpdate: (updatedZone) => updateZone(index, updatedZone),
                onDelete: () => removeZone(index),
              ),
            );
          }),

        // Soil Resistivity Report Button (only show if zones have data)
        if (zones.value.isNotEmpty && _hasZoneData(zones.value))
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: SoilResistivityReportButton(zones: zones.value),
          ),

        const SizedBox(height: 16),

        // Info box
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.info_outline, color: AppColors.primaryBlue, size: 20),
                  const SizedBox(width: 12),
                  Text(
                    'Wenner Four-Pin Method',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Enter resistance (R) and reactance (X) readings for each electrode spacing. The system will calculate the apparent resistivity for each zone.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.primaryBlue,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<WennerZone> _initializeZones(
    List<List<double>> rReadings,
    List<List<double>> xReadings,
    List<String> aliases,
  ) {
    if (rReadings.isEmpty) {
      return [
        WennerZone(
          alias: 'Zone 1',
          rReadings: [0, 0, 0, 0],
          xReadings: [1.0, 2.0, 3.0, 4.0], // Standard Wenner spacings
        ),
      ];
    }

    final zones = <WennerZone>[];
    for (int i = 0; i < rReadings.length; i++) {
      zones.add(WennerZone(
        alias: i < aliases.length ? aliases[i] : 'Zone ${i + 1}',
        rReadings: rReadings[i],
        xReadings: [1.0, 2.0, 3.0, 4.0], // Always use standard Wenner spacings
      ));
    }
    return zones;
  }

  // Helper function to check if zones have data
  bool _hasZoneData(List<WennerZone> zones) {
    return zones.any((zone) =>
      zone.rReadings.any((reading) => reading > 0) ||
      zone.xReadings.any((reading) => reading > 0)
    );
  }
}

class SoilResistivityReportButton extends StatefulWidget {
  final List<WennerZone> zones;

  const SoilResistivityReportButton({
    super.key,
    required this.zones,
  });

  @override
  State<SoilResistivityReportButton> createState() => _SoilResistivityReportButtonState();
}

class _SoilResistivityReportButtonState extends State<SoilResistivityReportButton> {
  void _navigateToReportPreview() {
    final earthingBloc = context.read<EarthingBloc>();
    final input = earthingBloc.state.input;

    // Validate required fields
    if (input.projectName.isEmpty) {
      _showValidationDialog('Project Name is required for the soil resistivity report. Please fill it in the project information section above.');
      return;
    }

    // Navigate to the soil report preview page with input data
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (newContext) => SoilReportPreviewPage(input: input),
      ),
    );
  }



  void _showValidationDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange, size: 28),
              const SizedBox(width: 12),
              const Text('Missing Information'),
            ],
          ),
          content: Text(message),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }



  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primaryGreen.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primaryGreen.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.terrain,
                  color: AppColors.primaryGreen,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Soil Resistivity Report',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.primaryGreen,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Generate a professional soil resistivity measurement report using your Wenner zone data',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.darkGrey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _navigateToReportPreview,
              icon: const Icon(Icons.preview),
              label: const Text('Review Soil Resistivity Report'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class WennerZone {
  final String alias;
  final List<double> rReadings;
  final List<double> xReadings;

  WennerZone({
    required this.alias,
    required this.rReadings,
    required this.xReadings,
  });

  WennerZone copyWith({
    String? alias,
    List<double>? rReadings,
    List<double>? xReadings,
  }) {
    return WennerZone(
      alias: alias ?? this.alias,
      rReadings: rReadings ?? this.rReadings,
      xReadings: xReadings ?? this.xReadings,
    );
  }
}

class WennerZoneCard extends HookWidget {
  const WennerZoneCard({
    super.key,
    required this.zone,
    required this.zoneIndex,
    required this.canDelete,
    required this.onUpdate,
    required this.onDelete,
  });

  final WennerZone zone;
  final int zoneIndex;
  final bool canDelete;
  final Function(WennerZone) onUpdate;
  final VoidCallback onDelete;

  @override
  Widget build(BuildContext context) {
    final aliasController = useTextEditingController(text: zone.alias);
    final isExpanded = useState(false);

    final rControllers = zone.rReadings.map((r) =>
      useTextEditingController(text: r.toString())
    ).toList();

    void updateZone() {
      final newRReadings = rControllers.map((c) => double.tryParse(c.text) ?? 0).toList();

      onUpdate(zone.copyWith(
        alias: aliasController.text.trim().isEmpty ? 'Zone ${zoneIndex + 1}' : aliasController.text.trim(),
        rReadings: newRReadings,
        // Use standard Wenner spacings for calculations
        xReadings: [1.0, 2.0, 3.0, 4.0],
      ));
    }

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColors.outline.withValues(alpha: 0.5)),
      ),
      child: ExpansionTile(
        title: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.primaryRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  '${zoneIndex + 1}',
                  style: TextStyle(
                    color: AppColors.primaryRed,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: aliasController,
                onChanged: (_) => updateZone(),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                decoration: InputDecoration(
                  hintText: 'Zone ${zoneIndex + 1}',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Show calculated soil resistivity for this zone
            if (_hasValidReadings(zone)) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primaryGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: AppColors.primaryGreen.withValues(alpha: 0.3)),
                ),
                child: Text(
                  '${_calculateZoneResistivity(zone).toStringAsFixed(1)} Ω·m',
                  style: TextStyle(
                    color: AppColors.primaryGreen,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
            ],
            if (canDelete)
              IconButton(
                onPressed: onDelete,
                icon: Icon(Icons.delete_outline, color: AppColors.error),
                tooltip: 'Delete zone',
              ),
            Icon(
              isExpanded.value ? Icons.expand_less : Icons.expand_more,
              color: AppColors.primaryBlue,
            ),
          ],
        ),
        onExpansionChanged: (expanded) => isExpanded.value = expanded,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Electrode Spacing Measurements',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Measurement table
                Table(
                  columnWidths: const {
                    0: FlexColumnWidth(2),
                    1: FlexColumnWidth(3),
                    2: FlexColumnWidth(3),
                  },
                  children: [
                    // Header
                    TableRow(
                      decoration: BoxDecoration(
                        color: AppColors.surface,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(12),
                          child: Text(
                            'Spacing',
                            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(12),
                          child: Text(
                            'Resistance (Ω)',
                            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(12),
                          child: Text(
                            'Resistivity (Ω·m)',
                            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    // Data rows
                    ...List.generate(4, (i) {
                      final spacings = ['1m', '2m', '3m', '4m'];
                      return TableRow(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(12),
                            child: Text(
                              spacings[i],
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8),
                            child: TextField(
                              controller: rControllers[i],
                              onChanged: (_) => updateZone(),
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                isDense: true,
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                              decoration: BoxDecoration(
                                color: AppColors.primaryGreen.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(color: AppColors.primaryGreen.withValues(alpha: 0.3)),
                              ),
                              child: Text(
                                _calculateResistivityForSpacing(zone, i).toStringAsFixed(2),
                                style: TextStyle(
                                  color: AppColors.primaryGreen,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
                    }),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to check if zone has valid readings
  bool _hasValidReadings(WennerZone zone) {
    return zone.rReadings.any((reading) => reading > 0);
  }

  // Helper method to calculate zone soil resistivity using Wenner formula
  double _calculateZoneResistivity(WennerZone zone) {
    final spacings = [1.0, 2.0, 3.0, 4.0]; // Standard Wenner spacings
    final validReadings = <double>[];
    final validSpacings = <double>[];

    for (int i = 0; i < zone.rReadings.length && i < spacings.length; i++) {
      if (zone.rReadings[i] > 0) {
        validReadings.add(zone.rReadings[i]);
        validSpacings.add(spacings[i]);
      }
    }

    if (validReadings.isEmpty) return 0.0;

    // Calculate resistivity for each valid reading using ρ = 2πaR
    final resistivities = <double>[];
    for (int i = 0; i < validReadings.length; i++) {
      final rho = 2 * 3.14159 * validSpacings[i] * validReadings[i];
      resistivities.add(rho);
    }

    // Return average resistivity
    return resistivities.reduce((a, b) => a + b) / resistivities.length;
  }

  // Helper method to calculate resistivity for a specific spacing
  double _calculateResistivityForSpacing(WennerZone zone, int spacingIndex) {
    final spacings = [1.0, 2.0, 3.0, 4.0]; // Standard Wenner spacings

    if (spacingIndex >= zone.rReadings.length || spacingIndex >= spacings.length) {
      return 0.0;
    }

    final resistance = zone.rReadings[spacingIndex];
    final spacing = spacings[spacingIndex];

    if (resistance <= 0) return 0.0;

    // Calculate resistivity using ρ = 2πXR where X is spacing and R is resistance
    return 2 * 3.14159 * spacing * resistance;
  }
}
