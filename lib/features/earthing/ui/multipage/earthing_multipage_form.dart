import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/ui/multipage/pages/soil_target_page.dart';
import 'package:pis_core/features/earthing/ui/multipage/pages/rod_configuration_page.dart';
import 'package:pis_core/features/earthing/ui/multipage/pages/fault_cable_page.dart';
import 'package:pis_core/features/earthing/ui/multipage/pages/results_report_page.dart';
import 'package:pis_core/features/earthing/ui/multipage/widgets/form_progress_indicator.dart';
import 'package:pis_core/features/earthing/ui/multipage/widgets/form_navigation_bar.dart';

enum EarthingFormStep {
  soilTarget(0, 'Soil & Target', 'Configure soil resistivity and target resistance'),
  rodConfiguration(1, 'Rod Setup', 'Configure earthing rod parameters'),
  faultCable(2, 'Fault & Cable', 'Set fault current and cable parameters'),
  results(3, 'Results', 'View calculations and generate report');

  const EarthingFormStep(this.stepIndex, this.title, this.description);
  final int stepIndex;
  final String title;
  final String description;

  static EarthingFormStep fromIndex(int index) {
    return EarthingFormStep.values.firstWhere((step) => step.stepIndex == index);
  }
}

class EarthingMultipageForm extends HookWidget {
  const EarthingMultipageForm({super.key, this.onExit});

  final VoidCallback? onExit;

  @override
  Widget build(BuildContext context) {
    final currentStepIndex = useState(0);
    final pageController = usePageController();
    
    final currentStep = EarthingFormStep.fromIndex(currentStepIndex.value);
    final isLastStep = currentStepIndex.value == EarthingFormStep.values.length - 1;
    final isFirstStep = currentStepIndex.value == 0;

    void goToStep(int stepIndex) {
      if (stepIndex >= 0 && stepIndex < EarthingFormStep.values.length) {
        currentStepIndex.value = stepIndex;
        pageController.animateToPage(
          stepIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }

    void nextStep() {
      if (!isLastStep) {
        goToStep(currentStepIndex.value + 1);
      }
    }

    void previousStep() {
      if (!isFirstStep) {
        goToStep(currentStepIndex.value - 1);
      }
    }

    final pages = [
      const SoilTargetPage(),
      const RodConfigurationPage(),
      const FaultCablePage(),
      const ResultsReportPage(),
    ];

    return Scaffold(
      backgroundColor: AppColors.surface,
      body: Column(
        children: [
          // Compact Progress Indicator with Exit Button
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            decoration: BoxDecoration(
              color: AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Exit button
                IconButton(
                  onPressed: onExit,
                  icon: const Icon(Icons.arrow_back),
                  style: IconButton.styleFrom(
                    foregroundColor: AppColors.primaryBlue,
                  ),
                ),
                const SizedBox(width: 8),
                // PIS Logo
                Container(
                  width: 24,
                  height: 24,
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(2),
                    child: Image.asset(
                      'assets/logo/PIS logo.png',
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // Progress indicator
                Expanded(
                  child: FormProgressIndicator(
                    currentStep: currentStepIndex.value,
                    totalSteps: EarthingFormStep.values.length,
                    onStepTapped: goToStep,
                  ),
                ),
              ],
            ),
          ),
          
          // Page Content with minimal spacing
          Expanded(
            child: PageView(
              controller: pageController,
              onPageChanged: (index) {
                currentStepIndex.value = index;
              },
              children: pages,
            ),
          ),

          // Navigation Bar
          FormNavigationBar(
            isFirstStep: isFirstStep,
            isLastStep: isLastStep,
            onPrevious: previousStep,
            onNext: nextStep,
            currentStep: currentStep,
            onNavigateToStep: goToStep,
          ),
        ],
      ),
    );
  }
}
