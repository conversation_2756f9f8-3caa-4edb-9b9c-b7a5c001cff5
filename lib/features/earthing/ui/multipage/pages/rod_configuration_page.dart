import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';
import 'package:flutter_math_fork/flutter_math.dart';
import 'package:pis_core/features/earthing/ui/equations.dart';

class RodConfigurationPage extends HookWidget {
  const RodConfigurationPage({super.key});

  @override
  Widget build(BuildContext context) {
    final input = context.watch<EarthingBloc>().state.input;

    final rodLengthCtrl = useTextEditingController(text: input.rodLength.toString());
    final rodSpacingCtrl = useTextEditingController(text: input.rodSpacing.toString());
    final rodsPerSideCtrl = useTextEditingController(text: input.rodsPerSide.toString());
    final numberOfWellsCtrl = useTextEditingController(text: input.numberOfWells.toString());
    final plateSideCtrl = useTextEditingController(text: input.plateSide.toString());
    final meshAreaCtrl = useTextEditingController(text: input.meshArea.toString());
    final meshPerimeterCtrl = useTextEditingController(text: input.meshPerimeter.toString());
    final burialDepthCtrl = useTextEditingController(text: input.burialDepth.toString());
    final customRodDiameterCtrl = useTextEditingController(text: (input.customRodDiameter ?? 0.0).toString());

    void pushBlocUpdate() {
      final bloc = context.read<EarthingBloc>();
      final current = bloc.state.input;

      bloc.add(EarthingBlocEvent.updateInput(current.copyWith(
        rodLength: double.tryParse(rodLengthCtrl.text) ?? current.rodLength,
        rodSpacing: double.tryParse(rodSpacingCtrl.text) ?? current.rodSpacing,
        rodsPerSide: int.tryParse(rodsPerSideCtrl.text) ?? current.rodsPerSide,
        numberOfWells: int.tryParse(numberOfWellsCtrl.text) ?? current.numberOfWells,
        plateSide: double.tryParse(plateSideCtrl.text) ?? current.plateSide,
        meshArea: double.tryParse(meshAreaCtrl.text) ?? current.meshArea,
        meshPerimeter: double.tryParse(meshPerimeterCtrl.text) ?? current.meshPerimeter,
        burialDepth: double.tryParse(burialDepthCtrl.text) ?? current.burialDepth,
        customRodDiameter: double.tryParse(customRodDiameterCtrl.text),
      )));
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final isWide = constraints.maxWidth > 800;

        if (isWide) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Row(
              children: [
                // Left Panel - Arrangement & Rod Type
                Expanded(
                  flex: 4,
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                  // Header
                  Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppColors.primaryRed.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.construction,
                          color: AppColors.primaryRed,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Arrangement & Type',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                color: AppColors.primaryBlue,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.visible,
                              softWrap: true,
                            ),
                            Text(
                              'Configure earthing system arrangement',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: AppColors.darkGrey,
                              ),
                              overflow: TextOverflow.visible,
                              softWrap: true,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 32),

                  // Arrangement selector
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'System Arrangement',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildArrangementSelector(context, input.arrangement, (arrangement) {
                          final bloc = context.read<EarthingBloc>();
                          bloc.add(EarthingBlocEvent.updateInput(
                            bloc.state.input.copyWith(
                              arrangement: arrangement,
                              isClosedLoop: arrangement.isClosedLoop, // Automatically set based on arrangement
                            )
                          ));
                        }),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Rod type selector
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Rod Type',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildRodTypeSelector(context, input.rodType, (rodType) {
                          final bloc = context.read<EarthingBloc>();
                          bloc.add(EarthingBlocEvent.updateInput(
                            bloc.state.input.copyWith(rodType: rodType)
                          ));
                        }),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Conductor properties
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Conductor Properties',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildConductorSelectors(context, input, (material, shape) {
                          final bloc = context.read<EarthingBloc>();
                          bloc.add(EarthingBlocEvent.updateInput(
                            bloc.state.input.copyWith(
                              conductorMaterial: material,
                              conductorShape: shape,
                            )
                          ));
                        }),
                      ],
                    ),
                  ),
                    ],
                  ),
                    ),
                  ),
                ),

                // Right Panel - Dimensions & Parameters
                Expanded(
                  flex: 6,
                  child: Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.straighten,
                                  color: AppColors.primaryBlue,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Dimensions & Parameters',
                                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        color: AppColors.primaryBlue,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                    Text(
                                      'Configure system dimensions and parameters',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: AppColors.darkGrey,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 32),

                          // Equation Display
                          _buildEquationDisplay(context, input.arrangement),

                          const SizedBox(height: 32),

                          // Parameters content
                          _buildParametersContent(context, input, pushBlocUpdate, {
                            'rodLength': rodLengthCtrl,
                            'rodSpacing': rodSpacingCtrl,
                            'rodsPerSide': rodsPerSideCtrl,
                            'numberOfWells': numberOfWellsCtrl,
                            'plateSide': plateSideCtrl,
                            'meshArea': meshAreaCtrl,
                            'meshPerimeter': meshPerimeterCtrl,
                            'burialDepth': burialDepthCtrl,
                            'customRodDiameter': customRodDiameterCtrl,
                          }),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          // Mobile layout - single column
          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Column(
              children: [
                // Arrangement & Rod Type Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Arrangement & Type',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.visible,
                        softWrap: true,
                      ),
                      const SizedBox(height: 24),
                      _buildArrangementSelector(context, input.arrangement, (arrangement) {
                        final bloc = context.read<EarthingBloc>();
                        bloc.add(EarthingBlocEvent.updateInput(
                          bloc.state.input.copyWith(
                            arrangement: arrangement,
                            isClosedLoop: arrangement.isClosedLoop,
                          )
                        ));
                      }),
                      const SizedBox(height: 24),
                      _buildRodTypeSelector(context, input.rodType, (rodType) {
                        final bloc = context.read<EarthingBloc>();
                        bloc.add(EarthingBlocEvent.updateInput(
                          bloc.state.input.copyWith(rodType: rodType)
                        ));
                      }),
                      const SizedBox(height: 24),
                      _buildConductorSelectors(context, input, (material, shape) {
                        final bloc = context.read<EarthingBloc>();
                        bloc.add(EarthingBlocEvent.updateInput(
                          bloc.state.input.copyWith(
                            conductorMaterial: material,
                            conductorShape: shape,
                          )
                        ));
                      }),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Parameters Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Dimensions & Parameters',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Equation Display
                      _buildEquationDisplay(context, input.arrangement),

                      const SizedBox(height: 24),

                      _buildParametersContent(context, input, pushBlocUpdate, {
                        'rodLength': rodLengthCtrl,
                        'rodSpacing': rodSpacingCtrl,
                        'rodsPerSide': rodsPerSideCtrl,
                        'numberOfWells': numberOfWellsCtrl,
                        'plateSide': plateSideCtrl,
                        'meshArea': meshAreaCtrl,
                        'meshPerimeter': meshPerimeterCtrl,
                        'burialDepth': burialDepthCtrl,
                        'customRodDiameter': customRodDiameterCtrl,
                      }),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildArrangementSelector(BuildContext context, EarthingArrangement current, Function(EarthingArrangement) onChanged) {
    return Column(
      children: EarthingArrangement.values.map((arrangement) {
        final isSelected = arrangement == current;
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: GestureDetector(
            onTap: () => onChanged(arrangement),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primaryBlue.withValues(alpha: 0.1) : AppColors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? AppColors.primaryBlue : AppColors.outline,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getArrangementIcon(arrangement),
                    color: isSelected ? AppColors.primaryBlue : AppColors.darkGrey,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getArrangementName(arrangement),
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: isSelected ? AppColors.primaryBlue : AppColors.black,
                          ),
                        ),
                        Text(
                          _getArrangementDescription(arrangement),
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.darkGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Icon(Icons.check_circle, color: AppColors.primaryBlue),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildRodTypeSelector(BuildContext context, StandardRod current, Function(StandardRod) onChanged) {
    return Column(
      children: StandardRod.values.map((rod) {
        final isSelected = rod == current;
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: GestureDetector(
            onTap: () => onChanged(rod),
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primaryBlue.withValues(alpha: 0.1)
                    : AppColors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected ? AppColors.primaryBlue : AppColors.outline,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: isSelected ? AppColors.primaryBlue : AppColors.surface,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? AppColors.primaryBlue : AppColors.mediumGrey,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? Icon(Icons.check, size: 18, color: AppColors.white)
                        : null,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.primaryBlue.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Text(
                                '${(rod.dNominal * 1000).toStringAsFixed(1)}mm',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryBlue,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                rod.label,
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected ? AppColors.primaryBlue : AppColors.darkGrey,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Nominal: ${(rod.dNominal * 1000).toStringAsFixed(1)}mm | Table: ${(rod.dTable * 1000).toStringAsFixed(1)}mm',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.darkGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildConductorSelectors(BuildContext context, EarthingCalculationInput input, Function(ConductorMaterial, ConductorShape) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Material Selection
        Text(
          'Material',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.primaryBlue,
          ),
        ),
        const SizedBox(height: 12),
        ...ConductorMaterial.values.map((material) {
          final isSelected = material == input.conductorMaterial;
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: GestureDetector(
              onTap: () => onChanged(material, input.conductorShape),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primaryGreen.withValues(alpha: 0.1)
                      : AppColors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? AppColors.primaryGreen : AppColors.outline,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primaryGreen : AppColors.surface,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected ? AppColors.primaryGreen : AppColors.mediumGrey,
                        ),
                      ),
                      child: isSelected
                          ? Icon(Icons.check, size: 14, color: AppColors.white)
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      material.label,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? AppColors.primaryGreen : AppColors.darkGrey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),

        const SizedBox(height: 24),

        // Shape Selection
        Text(
          'Shape',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.primaryBlue,
          ),
        ),
        const SizedBox(height: 12),
        ...ConductorShape.values.map((shape) {
          final isSelected = shape == input.conductorShape;
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: GestureDetector(
              onTap: () => onChanged(input.conductorMaterial, shape),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primaryRed.withValues(alpha: 0.1)
                      : AppColors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? AppColors.primaryRed : AppColors.outline,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primaryRed : AppColors.surface,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected ? AppColors.primaryRed : AppColors.mediumGrey,
                        ),
                      ),
                      child: isSelected
                          ? Icon(Icons.check, size: 14, color: AppColors.white)
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      shape.label,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? AppColors.primaryRed : AppColors.darkGrey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildParametersContent(BuildContext context, EarthingCalculationInput input, VoidCallback pushBlocUpdate, Map<String, TextEditingController> controllers) {
    return Column(
      children: [
        if (input.arrangement != EarthingArrangement.meshGrid) ...[
          _buildParameterCard(
            'Rod Configuration',
            Icons.vertical_align_bottom,
            AppColors.primaryRed,
            [
              _buildParameterField('Rod Length', controllers['rodLength']!, 'm', pushBlocUpdate),
              _buildParameterField('Rod Spacing', controllers['rodSpacing']!, 'm', pushBlocUpdate),
              if (input.arrangement == EarthingArrangement.hollowSquareRod)
                _buildParameterField('Rods per Side', controllers['rodsPerSide']!, '', pushBlocUpdate),
              if (input.arrangement == EarthingArrangement.parallelRodLine)
                _buildParameterField('Number of Wells', controllers['numberOfWells']!, '', pushBlocUpdate),
              if (input.arrangement == EarthingArrangement.plate)
                _buildParameterField('Plate Side', controllers['plateSide']!, 'm', pushBlocUpdate),
            ],
          ),
        ] else ...[
          _buildParameterCard(
            'Mesh Configuration',
            Icons.grid_on,
            AppColors.primaryBlue,
            [
              _buildParameterField('Mesh Area', controllers['meshArea']!, 'm²', pushBlocUpdate),
              _buildParameterField('Mesh Perimeter', controllers['meshPerimeter']!, 'm', pushBlocUpdate),
            ],
          ),
        ],

        const SizedBox(height: 24),

        _buildParameterCard(
          'Installation Details',
          Icons.construction,
          AppColors.primaryRed,
          [
            _buildParameterField('Burial Depth', controllers['burialDepth']!, 'm', pushBlocUpdate),
            if (input.customRodDiameter != null)
              _buildParameterField('Custom Rod Diameter', controllers['customRodDiameter']!, 'mm', pushBlocUpdate),
            _buildSwitchField('Rod Encased', input.rodEncased, (value) {
              final bloc = context.read<EarthingBloc>();
              bloc.add(EarthingBlocEvent.updateInput(
                bloc.state.input.copyWith(
                  rodEncased: value,
                  // Provide default values when enabling encased rods
                  enhancementResistivity: value ? 100.0 : null,
                  infillDiameter: value ? 0.1 : null,
                )
              ));
            }),

            // Show encased rod parameters when enabled
            if (input.rodEncased) ...[
              const SizedBox(height: 16),
              _buildParameterField(
                'Enhancement Resistivity',
                TextEditingController(text: (input.enhancementResistivity ?? 100.0).toString()),
                'Ω·m',
                () {
                  // Handle enhancement resistivity update
                  final bloc = context.read<EarthingBloc>();
                  final controller = TextEditingController(text: (input.enhancementResistivity ?? 100.0).toString());
                  bloc.add(EarthingBlocEvent.updateInput(
                    bloc.state.input.copyWith(
                      enhancementResistivity: double.tryParse(controller.text) ?? 100.0,
                    )
                  ));
                }
              ),
              _buildParameterField(
                'Infill Diameter',
                TextEditingController(text: (input.infillDiameter ?? 0.1).toString()),
                'm',
                () {
                  // Handle infill diameter update
                  final bloc = context.read<EarthingBloc>();
                  final controller = TextEditingController(text: (input.infillDiameter ?? 0.1).toString());
                  bloc.add(EarthingBlocEvent.updateInput(
                    bloc.state.input.copyWith(
                      infillDiameter: double.tryParse(controller.text) ?? 0.1,
                    )
                  ));
                }
              ),
            ],
            // Closed loop is now automatic based on arrangement
            _buildInfoField('Closed Loop', input.arrangement.isClosedLoop ? 'Yes' : 'No',
              'Automatically determined by arrangement type'),
          ],
        ),
      ],
    );
  }

  Widget _buildParameterCard(String title, IconData icon, Color color, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 18),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    );
  }

  Widget _buildParameterField(String label, TextEditingController controller, String suffix, VoidCallback onChanged) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextField(
        controller: controller,
        onChanged: (_) => onChanged(),
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
          labelText: label,
          suffixText: suffix.isNotEmpty ? suffix : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
    );
  }

  Widget _buildSwitchField(String label, bool value, Function(bool) onChanged) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primaryBlue,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoField(String label, String value, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryBlue,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.darkGrey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEquationDisplay(BuildContext context, EarthingArrangement arrangement) {
    final eq = eqMap[arrangement]!;
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.functions, color: AppColors.primaryBlue, size: 20),
              const SizedBox(width: 12),
              Text(
                'Equation (${eq.$2})',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.primaryBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.outline),
            ),
            child: Center(
              child: Math.tex(
                eq.$1,
                textStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.primaryBlue,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Formula for ${arrangement.label} arrangement',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.darkGrey,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getArrangementIcon(EarthingArrangement arrangement) {
    switch (arrangement) {
      case EarthingArrangement.singleRod:
        return Icons.vertical_align_bottom;
      case EarthingArrangement.parallelRodLine:
        return Icons.view_column;
      case EarthingArrangement.triangleRod:
        return Icons.change_history;
      case EarthingArrangement.hollowSquareRod:
        return Icons.grid_4x4;
      case EarthingArrangement.buriedStraight:
        return Icons.horizontal_rule;
      case EarthingArrangement.buriedLShape:
        return Icons.turn_right;
      case EarthingArrangement.buriedStar:
        return Icons.star;
      case EarthingArrangement.buriedCruciform:
        return Icons.add;
      case EarthingArrangement.plate:
        return Icons.crop_square;
      case EarthingArrangement.meshGrid:
        return Icons.grid_on;
    }
  }

  String _getArrangementName(EarthingArrangement arrangement) {
    return arrangement.label;
  }

  String _getArrangementDescription(EarthingArrangement arrangement) {
    switch (arrangement) {
      case EarthingArrangement.singleRod:
        return 'Single vertical earthing rod';
      case EarthingArrangement.parallelRodLine:
        return 'Multiple parallel rods in line';
      case EarthingArrangement.triangleRod:
        return 'Three rods in triangular arrangement';
      case EarthingArrangement.hollowSquareRod:
        return 'Hollow square arrangement of rods';
      case EarthingArrangement.buriedStraight:
        return 'Straight buried conductor';
      case EarthingArrangement.buriedLShape:
        return 'L-shaped buried conductor';
      case EarthingArrangement.buriedStar:
        return 'Star-shaped buried conductor';
      case EarthingArrangement.buriedCruciform:
        return 'Cruciform buried conductor';
      case EarthingArrangement.plate:
        return 'Horizontal plate electrode';
      case EarthingArrangement.meshGrid:
        return 'Mesh grid system';
    }
  }
}
