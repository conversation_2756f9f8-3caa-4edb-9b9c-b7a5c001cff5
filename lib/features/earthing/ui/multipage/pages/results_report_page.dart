import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';

class ResultsReportPage extends HookWidget {
  const ResultsReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWide = constraints.maxWidth > 800;

        if (isWide) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Row(
              children: [
                // Left Panel - Results Summary
                Expanded(
                  flex: 4,
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      border: Border(
                        right: BorderSide(color: AppColors.outline, width: 1),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryRed.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.assessment,
                                  color: AppColors.primaryRed,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Results Summary',
                                      style: Theme.of(context)
                                          .textTheme
                                          .headlineSmall
                                          ?.copyWith(
                                            color: AppColors.primaryBlue,
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                    Text(
                                      'Key calculation results and compliance',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(color: AppColors.darkGrey),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 32),

                          // Results content
                          BlocBuilder<EarthingBloc, EarthingBlocState>(
                            builder: (context, state) {
                              final result = state.result;
                              final input = state.input;
                              return Column(
                                children: [
                                  // Compliance status
                                  Container(
                                    padding: const EdgeInsets.all(24),
                                    decoration: BoxDecoration(
                                      color: result.meetsTarget
                                          ? Colors.green.withValues(alpha: 0.1)
                                          : Colors.red.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: result.meetsTarget
                                            ? Colors.green
                                            : Colors.red,
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          result.meetsTarget
                                              ? Icons.check_circle
                                              : Icons.error,
                                          color: result.meetsTarget
                                              ? Colors.green
                                              : Colors.red,
                                          size: 32,
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                result.meetsTarget
                                                    ? 'COMPLIANT'
                                                    : 'NON-COMPLIANT',
                                                style: TextStyle(
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.bold,
                                                  color: result.meetsTarget
                                                      ? Colors.green
                                                      : Colors.red,
                                                ),
                                              ),
                                              Text(
                                                result.meetsTarget
                                                    ? 'System meets target resistance'
                                                    : 'System exceeds target resistance',
                                                style: TextStyle(
                                                  color: AppColors.darkGrey,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 24),

                                  // Key results
                                  _buildResultCard(
                                    'Total Resistance',
                                    '${result.totalResistance.toStringAsFixed(4)} Ω',
                                    Icons.functions,
                                    AppColors.primaryBlue,
                                    'Combined rod and cable resistance',
                                  ),

                                  const SizedBox(height: 16),

                                  _buildResultCard(
                                    'Rod Resistance',
                                    '${result.rodResistance.toStringAsFixed(4)} Ω',
                                    Icons.vertical_align_bottom,
                                    AppColors.primaryRed,
                                    'Earthing electrode resistance',
                                  ),

                                  const SizedBox(height: 16),

                                  _buildResultCard(
                                    'Cable Resistance',
                                    '${result.cableResistance.toStringAsFixed(4)} Ω',
                                    Icons.cable,
                                    AppColors.primaryBlue,
                                    'Conductor resistance',
                                  ),

                                  // Show calculated soil resistivity when using Wenner method
                                  if (input.wennerRReadings.isNotEmpty && result.soilResistivityUsed != null) ...[
                                    const SizedBox(height: 16),
                                    _buildResultCard(
                                      'Calculated Soil Resistivity',
                                      '${result.soilResistivityUsed!.toStringAsFixed(2)} Ω·m',
                                      Icons.terrain,
                                      AppColors.primaryGreen,
                                      'Calculated from Wenner measurements',
                                    ),
                                  ],

                                  if (result.requiredCsaMm2 != null) ...[
                                    const SizedBox(height: 16),
                                    _buildResultCard(
                                      'Required CSA',
                                      '${result.requiredCsaMm2!.toStringAsFixed(2)} mm²',
                                      Icons.straighten,
                                      AppColors.primaryRed,
                                      'Minimum cable cross-section',
                                    ),
                                  ],

                                  if (result.chosenCableSize != null) ...[
                                    const SizedBox(height: 16),
                                    _buildResultCard(
                                      'Selected Cable',
                                      '${result.chosenCableSize!.areaMm2} mm²',
                                      Icons.cable,
                                      AppColors.primaryBlue,
                                      'Chosen standard cable size',
                                    ),
                                  ],
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Right Panel - Detailed Results & Report
                Expanded(
                  flex: 6,
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(color: AppColors.surface),
                    child: Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.outline),
                      ),
                      child: BlocBuilder<EarthingBloc, EarthingBlocState>(
                        builder: (context, state) {
                          return SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Header
                                Row(
                                  children: [
                                    Container(
                                      width: 48,
                                      height: 48,
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryBlue.withValues(
                                          alpha: 0.1,
                                        ),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Icon(
                                        Icons.description,
                                        color: AppColors.primaryBlue,
                                        size: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Detailed Results & Report',
                                            style: Theme.of(context)
                                                .textTheme
                                                .headlineSmall
                                                ?.copyWith(
                                                  color: AppColors.primaryBlue,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                          ),
                                          Text(
                                            'Complete calculation details and professional report',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium
                                                ?.copyWith(
                                                  color: AppColors.darkGrey,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 24),

                                // Detailed results content
                                _buildDetailedResults(context, state),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          // Mobile layout - single column
          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Column(
              children: [
                // Results Summary Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: BlocBuilder<EarthingBloc, EarthingBlocState>(
                    builder: (context, state) {
                      final result = state.result;
                      return Column(
                        children: [
                          Text(
                            'Results Summary',
                            style: Theme.of(context).textTheme.titleLarge
                                ?.copyWith(
                                  color: AppColors.primaryBlue,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 24),
                          // Compliance status
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: result.meetsTarget
                                  ? Colors.green.withValues(alpha: 0.1)
                                  : Colors.red.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: result.meetsTarget
                                    ? Colors.green
                                    : Colors.red,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  result.meetsTarget
                                      ? Icons.check_circle
                                      : Icons.error,
                                  color: result.meetsTarget
                                      ? Colors.green
                                      : Colors.red,
                                  size: 32,
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        result.meetsTarget
                                            ? 'COMPLIANT'
                                            : 'NON-COMPLIANT',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: result.meetsTarget
                                              ? Colors.green
                                              : Colors.red,
                                        ),
                                      ),
                                      Text(
                                        result.meetsTarget
                                            ? 'System meets target resistance'
                                            : 'System exceeds target resistance',
                                        style: TextStyle(
                                          color: AppColors.darkGrey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 24),
                          _buildResultCard(
                            'Total Resistance',
                            '${result.totalResistance.toStringAsFixed(4)} Ω',
                            Icons.functions,
                            AppColors.primaryBlue,
                            'Combined rod and cable resistance',
                          ),
                        ],
                      );
                    },
                  ),
                ),

                const SizedBox(height: 24),

                // Detailed Results Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Detailed Results',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 24),
                      BlocBuilder<EarthingBloc, EarthingBlocState>(
                        builder: (context, state) {
                          return _buildDetailedResults(context, state);
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildResultCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String description,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.darkGrey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: AppColors.darkGrey),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedResults(BuildContext context, EarthingBlocState state) {
    final result = state.result;
    final input = state.input;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Calculation Details',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: AppColors.primaryBlue,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 24),

        // Input parameters section (Project info is shown in PROJECT INFORMATION section)
        _buildDetailSection('Input Parameters', [
          _buildDetailRow(
            'Target Resistance',
            '${input.targetTotalResistance} Ω',
          ),
          _buildDetailRow(
            'Soil Resistivity',
            result.soilResistivityUsed != null
              ? '${result.soilResistivityUsed!.toStringAsFixed(2)} Ω·m ${input.wennerRReadings.isNotEmpty ? "(Wenner Method)" : ""}'
              : '${input.soilResistivity} Ω·m'
          ),
          _buildDetailRow('Rod Length', '${input.rodLength} m'),
          _buildDetailRow('Rod Type', input.rodType.label),
          _buildDetailRow('Arrangement', input.arrangement.label),
          _buildDetailRow('Fault Current', '${input.faultCurrentA} A'),
          _buildDetailRow('Fault Duration', '${input.faultDurationS} s'),
          _buildDetailRow('Conductor Material', input.conductorMaterial.label),
          _buildDetailRow('Manual Cable Size', input.manualCableSize != null ? '${input.manualCableSize!.areaMm2} mm²' : 'Auto'),
          _buildDetailRow('Manual Cable Length', input.manualCableLength != null ? '${input.manualCableLength} m' : 'Auto'),
        ]),

        const SizedBox(height: 32),

        // Calculation results section
        _buildDetailSection('Calculation Results', [
          _buildDetailRow(
            'Rod Resistance',
            '${result.rodResistance.toStringAsFixed(4)} Ω',
          ),
          _buildDetailRow(
            'Cable Resistance',
            '${result.cableResistance.toStringAsFixed(4)} Ω',
          ),
          _buildDetailRow(
            'Total Resistance',
            '${result.totalResistance.toStringAsFixed(4)} Ω',
          ),
          if (result.requiredCsaMm2 != null)
            _buildDetailRow(
              'Required CSA',
              '${result.requiredCsaMm2!.toStringAsFixed(2)} mm²',
            ),
          if (result.chosenCableSize != null)
            _buildDetailRow(
              'Selected Cable',
              '${result.chosenCableSize!.areaMm2} mm²',
            ),
          if (result.conductorLength != null)
            _buildDetailRow(
              'Conductor Length',
              '${result.conductorLength!.toStringAsFixed(2)} m',
            ),
          _buildDetailRow('Meets Target', result.meetsTarget ? 'Yes' : 'No'),
        ]),

        const SizedBox(height: 32),

        // Standards compliance section
        _buildDetailSection('Standards & Compliance', [
          _buildDetailRow('Standard', 'BS 7430:2011'),
          _buildDetailRow(
            'Calculation Method',
            'Clause 9.5 - Electrode Resistance',
          ),
          _buildDetailRow('Cable Sizing', 'Clause 9.7 - Adiabatic Equation'),
          _buildDetailRow('Safety Factor', 'Applied as per standard'),
          _buildDetailRow(
            'Compliance Status',
            result.meetsTarget ? 'COMPLIANT' : 'NON-COMPLIANT',
          ),
        ]),

        const SizedBox(height: 32),

        // Additional information
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primaryBlue.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.primaryBlue,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Important Notes',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                '• All calculations performed in accordance with BS 7430:2011\n'
                '• Results are based on the input parameters provided\n'
                '• Professional installation and testing required\n'
                '• Regular maintenance and testing recommended\n'
                '• Consult qualified electrical engineer for final design',
                style: TextStyle(
                  color: AppColors.primaryBlue,
                  fontSize: 13,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.primaryBlue,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.outline),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                color: AppColors.darkGrey,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Flexible(
            flex: 1,
            child: Text(
              value,
              style: TextStyle(
                color: AppColors.black,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
