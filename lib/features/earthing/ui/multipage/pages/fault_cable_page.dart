import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';

class FaultCablePage extends HookWidget {
  const FaultCablePage({super.key});

  @override
  Widget build(BuildContext context) {
    final input = context.watch<EarthingBloc>().state.input;

    final faultCurrentCtrl = useTextEditingController(text: input.faultCurrentA.toString());
    final faultDurationCtrl = useTextEditingController(text: input.faultDurationS.toString());
    final initialTempCtrl = useTextEditingController(text: input.initialTempC.toString());
    final finalTempCtrl = useTextEditingController(text: input.finalTempC.toString());
    final manualCableLengthCtrl = useTextEditingController(text: input.manualCableLength?.toString() ?? '');

    final manualCableSize = useState<StandardCableSize?>(input.manualCableSize);
    final conductorMaterial = useState<ConductorMaterial>(input.conductorMaterial);

    void pushBlocUpdate() {
      final bloc = context.read<EarthingBloc>();
      final current = bloc.state.input;

      bloc.add(EarthingBlocEvent.updateInput(current.copyWith(
        faultCurrentA: double.tryParse(faultCurrentCtrl.text) ?? current.faultCurrentA,
        faultDurationS: double.tryParse(faultDurationCtrl.text) ?? current.faultDurationS,
        initialTempC: double.tryParse(initialTempCtrl.text) ?? current.initialTempC,
        finalTempC: double.tryParse(finalTempCtrl.text) ?? current.finalTempC,
        conductorMaterial: conductorMaterial.value,
        manualCableSize: manualCableSize.value,
        manualCableLength: manualCableLengthCtrl.text.isEmpty ? null : double.tryParse(manualCableLengthCtrl.text),
      )));
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final isWide = constraints.maxWidth > 800;

        if (isWide) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Row(
              children: [
                // Left Panel - Fault Parameters
                Expanded(
                  flex: 4,
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: AppColors.primaryRed.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.flash_on,
                            color: AppColors.primaryRed,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Fault Parameters',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  color: AppColors.primaryBlue,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.visible,
                                softWrap: true,
                              ),
                              Text(
                                'Configure fault current and duration',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: AppColors.darkGrey,
                                ),
                                overflow: TextOverflow.visible,
                                softWrap: true,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 32),

                    // System voltage selector
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: AppColors.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.outline),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'System Voltage',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildVoltageSelector(context, input.systemVoltage, (voltage) {
                            final bloc = context.read<EarthingBloc>();
                            bloc.add(EarthingBlocEvent.updateInput(
                              bloc.state.input.copyWith(systemVoltage: voltage)
                            ));
                          }),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Fault current input
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: AppColors.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.outline),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Fault Current (A)',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextField(
                            controller: faultCurrentCtrl,
                            onChanged: (_) => pushBlocUpdate(),
                            keyboardType: TextInputType.number,
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primaryRed,
                            ),
                            decoration: InputDecoration(
                              hintText: '26000',
                              suffixText: 'A',
                              suffixStyle: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: AppColors.darkGrey,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(color: AppColors.primaryRed),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(color: AppColors.primaryRed, width: 2),
                              ),
                              contentPadding: const EdgeInsets.all(16),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Fault duration input
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: AppColors.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.outline),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Fault Duration (s)',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextField(
                            controller: faultDurationCtrl,
                            onChanged: (_) => pushBlocUpdate(),
                            keyboardType: TextInputType.number,
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primaryBlue,
                            ),
                            decoration: InputDecoration(
                              hintText: '1.0',
                              suffixText: 's',
                              suffixStyle: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: AppColors.darkGrey,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(color: AppColors.primaryBlue),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(color: AppColors.primaryBlue, width: 2),
                              ),
                              contentPadding: const EdgeInsets.all(16),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Guidelines
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppColors.primaryBlue.withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.2)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.info_outline, color: AppColors.primaryBlue, size: 20),
                              const SizedBox(width: 12),
                              Text(
                                'Fault Duration Guidelines',
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  color: AppColors.primaryBlue,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          _buildFaultGuidelineRow('LV Systems', '0.4 - 1.0s', 'Low voltage'),
                          _buildFaultGuidelineRow('MV Systems', '0.5 - 3.0s', 'Medium voltage'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
                  ),
                ),

                // Right Panel - Cable & Temperature Parameters
                Expanded(
                  flex: 6,
                  child: Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: AppColors.primaryBlue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.cable,
                            color: AppColors.primaryBlue,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Cable & Temperature',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  color: AppColors.primaryBlue,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.visible,
                                softWrap: true,
                              ),
                              Text(
                                'Configure cable sizing and temperature parameters',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: AppColors.darkGrey,
                                ),
                                overflow: TextOverflow.visible,
                                softWrap: true,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 32),

                    // Temperature parameters
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.outline),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryRed.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(Icons.thermostat, color: AppColors.primaryRed, size: 18),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Temperature Parameters',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.primaryBlue,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),

                          _buildTemperatureField('Initial Temperature', initialTempCtrl, '°C', pushBlocUpdate),
                          _buildTemperatureField('Final Temperature', finalTempCtrl, '°C', pushBlocUpdate),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Cable selection preview
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.outline),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(Icons.cable, color: AppColors.primaryBlue, size: 18),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Cable Sizing Preview',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.primaryBlue,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),

                          _buildCableSizingPreview(context, input),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Temperature guidelines
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppColors.primaryRed.withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.primaryRed.withValues(alpha: 0.2)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.info_outline, color: AppColors.primaryRed, size: 20),
                              const SizedBox(width: 12),
                              Text(
                                'Temperature Guidelines',
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  color: AppColors.primaryRed,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          _buildTempGuidelineRow('Copper', '30°C → 450°C', 'Standard'),
                          _buildTempGuidelineRow('Aluminium', '30°C → 300°C', 'Below annealing'),
                        ],
                      ),
                    ),
                      ],
                    ),
                  ),
                ),
                ),
              ],
            ),
          );
        } else {
          // Mobile layout - single column
          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Column(
              children: [
                // Fault Parameters Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Fault Parameters',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.visible,
                        softWrap: true,
                      ),
                      const SizedBox(height: 24),
                      _buildVoltageSelector(context, input.systemVoltage, (voltage) {
                        final bloc = context.read<EarthingBloc>();
                        bloc.add(EarthingBlocEvent.updateInput(
                          bloc.state.input.copyWith(systemVoltage: voltage)
                        ));
                      }),
                      const SizedBox(height: 24),
                      _buildTemperatureField('Fault Current', faultCurrentCtrl, 'A', pushBlocUpdate),
                      _buildTemperatureField('Fault Duration', faultDurationCtrl, 's', pushBlocUpdate),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Temperature & Cable Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Cable & Temperature',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.visible,
                        softWrap: true,
                      ),
                      const SizedBox(height: 24),
                      _buildTemperatureField('Initial Temperature', initialTempCtrl, '°C', pushBlocUpdate),
                      _buildTemperatureField('Final Temperature', finalTempCtrl, '°C', pushBlocUpdate),
                      const SizedBox(height: 24),
                      _buildConductorMaterialSelector(context, conductorMaterial.value, (material) {
                        conductorMaterial.value = material;
                        pushBlocUpdate();
                      }),
                      const SizedBox(height: 24),
                      _buildManualCableFields(context, manualCableSize.value, manualCableLengthCtrl, (size) {
                        manualCableSize.value = size;
                        pushBlocUpdate();
                      }, pushBlocUpdate),
                      const SizedBox(height: 24),
                      _buildCableSizingPreview(context, input),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildVoltageSelector(BuildContext context, SystemVoltageType current, Function(SystemVoltageType) onChanged) {
    return Row(
      children: SystemVoltageType.values.map((voltage) {
        final isSelected = voltage == current;
        return Expanded(
          child: Padding(
            padding: EdgeInsets.only(right: voltage == SystemVoltageType.values.last ? 0 : 8),
            child: GestureDetector(
              onTap: () => onChanged(voltage),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primaryBlue : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? AppColors.primaryBlue : AppColors.outline,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      voltage == SystemVoltageType.lv ? Icons.home : Icons.business,
                      size: 20,
                      color: isSelected ? AppColors.white : AppColors.darkGrey,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      voltage.label,
                      style: TextStyle(
                        color: isSelected ? AppColors.white : AppColors.darkGrey,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildFaultGuidelineRow(String type, String range, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Flexible(
            flex: 3,
            child: Text(
              type,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColors.primaryBlue,
                fontSize: 13,
              ),
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
          ),
          const SizedBox(width: 8),
          Flexible(
            flex: 2,
            child: Text(
              range,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryRed,
                fontSize: 13,
              ),
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
          ),
          const SizedBox(width: 8),
          Flexible(
            flex: 3,
            child: Text(
              description,
              style: TextStyle(
                color: AppColors.darkGrey,
                fontSize: 12,
              ),
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemperatureField(String label, TextEditingController controller, String suffix, VoidCallback onChanged) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextField(
        controller: controller,
        onChanged: (_) => onChanged(),
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
          labelText: label,
          suffixText: suffix,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
    );
  }

  Widget _buildCableSizingPreview(BuildContext context, input) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Based on current parameters, the system will automatically calculate:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.darkGrey,
          ),
        ),
        const SizedBox(height: 16),
        _buildPreviewRow('Required CSA', 'Calculated from fault parameters'),
        _buildPreviewRow('Cable Selection', 'Automatic from standard sizes'),
        _buildPreviewRow('K Constant', 'Based on material and temperature'),
        _buildPreviewRow('Cable Resistance', 'Calculated for selected cable'),
      ],
    );
  }

  Widget _buildPreviewRow(String label, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryBlue,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.darkGrey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTempGuidelineRow(String material, String range, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Flexible(
            flex: 2,
            child: Text(
              material,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColors.primaryRed,
                fontSize: 13,
              ),
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
          ),
          const SizedBox(width: 8),
          Flexible(
            flex: 3,
            child: Text(
              range,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
                fontSize: 13,
              ),
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
          ),
          const SizedBox(width: 8),
          Flexible(
            flex: 3,
            child: Text(
              description,
              style: TextStyle(
                color: AppColors.darkGrey,
                fontSize: 12,
              ),
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConductorMaterialSelector(
    BuildContext context,
    ConductorMaterial currentMaterial,
    Function(ConductorMaterial) onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Conductor Material',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(height: 12),
          DropdownButtonFormField<ConductorMaterial>(
            value: currentMaterial,
            onChanged: (value) {
              if (value != null) onChanged(value);
            },
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            items: ConductorMaterial.values.map((material) {
              return DropdownMenuItem(
                value: material,
                child: Text(material.label),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildManualCableFields(
    BuildContext context,
    StandardCableSize? currentSize,
    TextEditingController lengthController,
    Function(StandardCableSize?) onSizeChanged,
    VoidCallback onLengthChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Manual Cable Override',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Override automatic cable sizing with manual values',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.darkGrey,
            ),
          ),
          const SizedBox(height: 16),

          // Manual Cable Size
          Text(
            'Cable Size',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<StandardCableSize?>(
            value: currentSize,
            onChanged: onSizeChanged,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            items: [
              const DropdownMenuItem(
                value: null,
                child: Text('Auto (Calculate from fault current)'),
              ),
              ...StandardCableSize.values.map((size) {
                return DropdownMenuItem(
                  value: size,
                  child: Text('${size.areaMm2} mm²'),
                );
              }),
            ],
          ),

          const SizedBox(height: 16),

          // Manual Cable Length
          Text(
            'Cable Length',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: lengthController,
            onChanged: (_) => onLengthChanged(),
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              suffixText: 'm',
              hintText: 'Auto (Calculate from arrangement)',
            ),
          ),
        ],
      ),
    );
  }
}
