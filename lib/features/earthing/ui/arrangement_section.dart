import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/widgets/section_card.dart';
import 'package:pis_core/core/widgets/field_tile.dart';
import 'package:pis_core/core/widgets/num_field_tile.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';
import 'package:pis_core/core/widgets/centered_dropdown_form_field.dart';
import 'package:flutter_math_fork/flutter_math.dart';
import 'package:pis_core/features/earthing/ui/equations.dart';

class ArrangementSection extends HookWidget {
  const ArrangementSection({super.key});

  @override
  Widget build(BuildContext context) {
    final input = context.watch<EarthingBloc>().state.input;
    final selectedArrangement = input.arrangement;
    final selectedShape = input.conductorShape;

    // Extra inputs for mesh and plate arrangements – controllers sync with Bloc
    final meshAreaCtrl = useTextEditingController(text: input.meshArea.toString());
    final meshPerimeterCtrl = useTextEditingController(text: input.meshPerimeter.toString());
    final plateSideCtrl = useTextEditingController(text: input.plateSide.toString());
    // Hollow-square rod extra — rods per side
    final rodsPerSideCtrl = useTextEditingController(text: input.rodsPerSide.toString());

    // Focus nodes to avoid overwriting while editing
    final meshAreaFocus = useFocusNode();
    final meshPerimeterFocus = useFocusNode();
    final plateSideFocus = useFocusNode();
    final rodsPerSideFocus = useFocusNode();

    // Keep controllers in sync when Bloc state changes
    useEffect(() {
      if (!meshAreaFocus.hasFocus) {
        meshAreaCtrl.text = input.meshArea.toString();
      }
      if (!meshPerimeterFocus.hasFocus) {
        meshPerimeterCtrl.text = input.meshPerimeter.toString();
      }
      if (!plateSideFocus.hasFocus) {
        plateSideCtrl.text = input.plateSide.toString();
      }
      if (!rodsPerSideFocus.hasFocus) {
        rodsPerSideCtrl.text = input.rodsPerSide.toString();
      }
      return null;
    }, [input.meshArea, input.meshPerimeter, input.plateSide, input.rodsPerSide]);

    void push({EarthingArrangement? arr, ConductorShape? sh}) {
      final bloc = context.read<EarthingBloc>();
      bloc.add(EarthingBlocEvent.updateInput(bloc.state.input.copyWith(
        arrangement: arr ?? selectedArrangement,
        conductorShape: sh ?? selectedShape,
        isClosedLoop: (arr ?? selectedArrangement).isClosedLoop,
        rodsPerSide: int.tryParse(rodsPerSideCtrl.text) ?? input.rodsPerSide,
        meshArea: double.tryParse(meshAreaCtrl.text) ?? 0,
        meshPerimeter: double.tryParse(meshPerimeterCtrl.text) ?? 0,
        plateSide: double.tryParse(plateSideCtrl.text) ?? 0.0,
      )));
    }

    return SectionCard(
      title: 'Arrangement',
      description: 'Electrode layout and conductor shape.',
      child: Column(
        children: [
          FieldTile(
            label: 'Arrangement',
            child: CenteredDropdownFormField<EarthingArrangement>(
              value: selectedArrangement,
              onChanged: (v) {
                if (v != null) push(arr: v);
              },
              items: EarthingArrangement.values
                  .map((e) => DropdownMenuItem(value: e, child: Text(e.label, textAlign: TextAlign.center)))
                  .toList(),
            ),
          ),
          // Show the equation for the selected arrangement
          Padding(
            padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
            child: Builder(
              builder: (context) {
                final eq = eqMap[selectedArrangement]!;
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Equation (${eq.$2})', style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: 4),
                    Math.tex(eq.$1, textStyle: Theme.of(context).textTheme.bodyLarge),
                  ],
                );
              },
            ),
          ),
          if (selectedArrangement == EarthingArrangement.meshGrid) ...[
            const Divider(),
            NumFieldTile(
              label: 'Mesh Area',
              controller: meshAreaCtrl,
              focusNode: meshAreaFocus,
              suffix: 'm²',
              onChanged: () => push(),
            ),
            NumFieldTile(
              label: 'Mesh Perimeter',
              controller: meshPerimeterCtrl,
              focusNode: meshPerimeterFocus,
              suffix: 'm',
              onChanged: () => push(),
            ),
          ] else if (selectedArrangement == EarthingArrangement.plate) ...[
            const Divider(),
            NumFieldTile(
              label: 'Plate Side',
              controller: plateSideCtrl,
              focusNode: plateSideFocus,
              suffix: 'm',
              onChanged: () => push(),
            ),
          ] else if (selectedArrangement == EarthingArrangement.hollowSquareRod) ...[
            const Divider(),
            NumFieldTile(
              label: 'Rods per Side',
              controller: rodsPerSideCtrl,
              focusNode: rodsPerSideFocus,
              onChanged: () => push(),
            ),
          ],

          FieldTile(
            label: 'Conductor Shape',
            child: CenteredDropdownFormField<ConductorShape>(
              value: selectedShape,
              onChanged: (v) {
                if (v != null) push(sh: v);
              },
              items: ConductorShape.values
                  .map((e) => DropdownMenuItem(value: e, child: Text(e.label, textAlign: TextAlign.center)))
                  .toList(),
            ),
          ),
          FieldTile(
            label: 'Closed Loop',
            info: 'Derived automatically from arrangement.\nClosed loop equation (perimeter): L = N × S\nOpen loop (line) length: L = (N - 1) × S',
            equation: 'isClosedLoop = arrangement.isClosedLoop',
            child: Text(selectedArrangement.isClosedLoop ? 'Yes' : 'No'),
          ),
        ],
      ),
    );
  }
} 