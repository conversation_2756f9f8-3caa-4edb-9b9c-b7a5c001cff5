import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/widgets/section_card.dart';
import 'package:pis_core/core/widgets/num_field_tile.dart';
import 'package:pis_core/core/widgets/field_tile.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/core/widgets/centered_dropdown_form_field.dart';

enum RhoMode { direct, wenner }

// Constant Wenner probe spacings that match the sheet example
const List<double> _wennerSpacings = [1, 2, 3, 4];

class SoilSection extends HookWidget {
  const SoilSection({super.key});

  @override
  Widget build(BuildContext context) {
    final input = context.watch<EarthingBloc>().state.input;
    final initialMode = input.wennerRReadings.isNotEmpty ? RhoMode.wenner : RhoMode.direct;
    final rhoMode = useState<RhoMode>(initialMode);

    final soilCtrl = useTextEditingController(text: input.soilResistivity.toString());
    final targetCtrl = useTextEditingController(text: input.targetTotalResistance.toString());

    final soilFocus = useFocusNode();
    final targetFocus = useFocusNode();

    // zones persist – create based on existing wenner readings if any
    final zones = useState<List<_ZoneControllers>>(() {
      if (input.wennerRReadings.isEmpty) return [_ZoneControllers()];
      final z = <_ZoneControllers>[];
      for (var _ in input.wennerRReadings) {
        z.add(_ZoneControllers());
      }
      return z;
    }());

    useEffect(() {
      if (!soilFocus.hasFocus) {
        soilCtrl.text = input.soilResistivity.toString();
      }
      if (!targetFocus.hasFocus) {
        targetCtrl.text = input.targetTotalResistance.toString();
      }
      return null;
    }, [input.soilResistivity, input.targetTotalResistance]);

    void pushBlocUpdate() {
      final bloc = context.read<EarthingBloc>();
      final current = bloc.state.input;
      final soilVal = double.tryParse(soilCtrl.text) ?? 0;

      // For Wenner mode we rely on the averaged ρ value; we *do not* forward
      // the R/X tables so the bloc uses the provided soilResistivity instead
      // of overriding it.
      final useWenner = rhoMode.value == RhoMode.wenner;

      bloc.add(EarthingBlocEvent.updateInput(current.copyWith(
        soilResistivity: soilVal,
        targetTotalResistance: double.tryParse(targetCtrl.text) ?? 1,
        wennerRReadings: useWenner ? [[soilVal]] : <List<double>>[],
        wennerXReadings: <List<double>>[],
      )));
    }

    void recomputeFromTables() {
      final zoneAverages = <double>[];
      for (final z in zones.value) {
        final rhos = z.rhoCtrls.map((c) => double.tryParse(c.text) ?? 0).toList();
        if (rhos.any((e) => e == 0)) continue;
        final avgRho = rhos.reduce((a, b) => a + b) / rhos.length;
        zoneAverages.add(avgRho);
      }
      if (zoneAverages.isNotEmpty) {
        final totalAvg = zoneAverages.reduce((a, b) => a + b) / zoneAverages.length;
        soilCtrl.text = totalAvg.toStringAsFixed(3);
      } else {
        // No valid rho values yet – clear so UI/result shows '--'
        soilCtrl.text = '';
      }
      pushBlocUpdate();
    }

    Widget buildWennerTable(_ZoneControllers ctrls, int zoneIdx) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text('Zone ${zoneIdx + 1}', style: Theme.of(context).textTheme.labelLarge),
              const Spacer(),
              if (zones.value.length > 1)
                IconButton(
                  icon: const Icon(Icons.delete),
                  color: Theme.of(context).colorScheme.error,
                  tooltip: 'Remove this zone',
                  onPressed: () async {
                    final confirm = await showDialog<bool>(
                      context: context,
                      builder: (ctx) => AlertDialog(
                        title: const Text('Remove Zone'),
                        content: Text('Are you sure you want to remove Zone ${zoneIdx + 1}?'),
                        actions: [
                          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('Cancel')),
                          TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('Remove')),
                        ],
                      ),
                    );
                    if (confirm == true) {
                      zones.value.removeAt(zoneIdx);
                      zones.value = [...zones.value];
                      recomputeFromTables();
                    }
                  },
                ),
            ],
          ),
          const SizedBox(height: 6),
          Table(
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            columnWidths: const {
              0: IntrinsicColumnWidth(),
              1: FlexColumnWidth(),
              2: FlexColumnWidth(),
              3: FlexColumnWidth(),
              4: FlexColumnWidth(),
            },
            children: [
              // --- Spacing row (X) --------------------------------------------------
              TableRow(children: [
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  child: Text('X (m)'),
                ),
                for (int i = 0; i < 4; i++)
                  Center(child: Text(_wennerSpacings[i].toStringAsFixed(0))),
              ]),
              // --- Resistance input row (R) ----------------------------------------
              TableRow(children: [
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  child: Text('R (Ω)'),
                ),
                for (int i = 0; i < 4; i++)
                  Padding(
                    padding: const EdgeInsets.all(2),
                    child: SizedBox(
                      width: 80,
                      child: TextField(
                        textAlign: TextAlign.center,
                        controller: ctrls.rCtrls[i],
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          hintText: 'R${i + 1}',
                          isDense: true,
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 6),
                        ),
                      ),
                    ),
                  ),
              ]),
              // --- Input rho row ----------------------------------------------------
              TableRow(children: [
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  child: Text('ρ (Ω·m)'),
                ),
                for (int i = 0; i < 4; i++)
                  Padding(
                    padding: const EdgeInsets.all(2),
                    child: SizedBox(
                      width: 80,
                      child: TextField(
                        textAlign: TextAlign.center,
                        controller: ctrls.rhoCtrls[i],
                        keyboardType: TextInputType.number,
                        onChanged: (_) => recomputeFromTables(),
                        decoration: InputDecoration(
                          hintText: 'ρ${i + 1}',
                          isDense: true,
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 6),
                        ),
                      ),
                    ),
                  ),
              ]),
            ],
          ),
          const Divider(),
        ],
      );
    }

    return SectionCard(
      title: 'Soil & Target',
      description: 'Enter target earth resistance and compute soil resistivity via Wenner method or direct entry.',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Target resistance always first
          NumFieldTile(
            label: 'Target Resistance',
            controller: targetCtrl,
            suffix: 'Ω',
            info: 'Desired overall earth resistance for the installation.',
            onChanged: pushBlocUpdate,
            focusNode: targetFocus,
          ),
          const SizedBox(height: 8),
          // Mode selector
          FieldTile(
            label: 'Entry Mode',
            info: 'Choose how to provide soil resistivity: direct value or Wenner four-pin measurement tables.',
            child: CenteredDropdownFormField<RhoMode>(
              value: rhoMode.value,
              onChanged: (v) {
                if (v == null) return;
                rhoMode.value = v;
                if (v == RhoMode.wenner) {
                  // Recompute (or clear) soil resistivity based on rho tables
                  recomputeFromTables();
                } else {
                  pushBlocUpdate();
                }
              },
              items: const [
                DropdownMenuItem(value: RhoMode.direct, child: Text('Direct', textAlign: TextAlign.center)),
                DropdownMenuItem(value: RhoMode.wenner, child: Text('Wenner Calc', textAlign: TextAlign.center)),
              ],
            ),
          ),
          if (rhoMode.value == RhoMode.direct) ...[
            const Divider(),
            NumFieldTile(
              label: 'Soil Resistivity',
              controller: soilCtrl,
              suffix: 'Ω·m',
              onChanged: pushBlocUpdate,
              focusNode: soilFocus,
            ),
          ] else ...[
            const Divider(),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: zones.value.length,
              itemBuilder: (ctx, i) => buildWennerTable(zones.value[i], i),
            ),
          ],
          Align(
            alignment: Alignment.centerLeft,
            child: TextButton.icon(
              onPressed: () {
                zones.value = [...zones.value, _ZoneControllers()];
              },
              icon: const Icon(Icons.add),
              label: const Text('Add Zone'),
            ),
          ),
        ],
      ),
    );
  }
}

class _ZoneControllers {
  _ZoneControllers()
      : rCtrls = List.generate(4, (_) => TextEditingController()),
        rhoCtrls = List.generate(4, (_) => TextEditingController());

  final List<TextEditingController> rCtrls;
  final List<TextEditingController> rhoCtrls;
} 