import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';

/// LaTeX source + clause for each arrangement.
/// The LaTeX is written to render correctly with flutter_math(_fork) / KaTeX.
const Map<EarthingArrangement, (String latex, String clause)> eqMap = {
  // ────────────── Vertical rods ────────────────────────────────────────────
  EarthingArrangement.singleRod: (
    r'R = \frac{\rho}{2\pi L}\!\left[\ln\!\left(\frac{8L}{d}\right)-1\right]',
    'BS 7430 § 9.5.3'
  ),

  EarthingArrangement.parallelRodLine: (
    r'R = \frac{1}{n}\,\frac{\rho}{2\pi L}'
    r'\!\left[\ln\!\left(\frac{8L}{d}\right)-1 + \lambda\,\frac{L}{S}\right]',
    '§ 9.5.4'
  ),

  EarthingArrangement.triangleRod: (
    r'R = \frac{\rho}{3\pi L}'
    r'\!\left[\ln\!\left(\frac{8L}{d}\right)-1 + 2\ln\!\left(\frac{L}{a}\right)\right]',
    '§ *******'
  ),

  EarthingArrangement.hollowSquareRod: (
    r'R = R_{\text{single}}\!\left(1 + \frac{\lambda\,\alpha}{N}\right),\,'
    r'\alpha = \frac{\rho}{2\pi R_{\text{single}}\,s}',
    '§ *******'
  ),

  // ────────────── Buried conductors (strip / round) ────────────────────────
  EarthingArrangement.buriedStraight: (
    r'R = \frac{\rho}{2\pi L}'
    r'\,\ln\!\left(\frac{L^{2}}{K\,h\,d}\right),\;'
    r'K = \begin{cases}1.36 & \text{strip}\\ 1.83 & \text{round}\end{cases}',
    '§ 9.5.5'
  ),

  EarthingArrangement.buriedLShape: (
    r'R = \frac{\rho}{2\pi L}'
    r'\,\ln\!\left(\frac{L^{2}}{K\,h\,d}\right),\;'
    r'K = \begin{cases}1.21 & \text{strip}\\ 0.813 & \text{round}\end{cases}',
    '§ *******'
  ),

  EarthingArrangement.buriedStar: (
    r'R = \frac{\rho}{2\pi L}'
    r'\,\ln\!\left(\frac{L^{2}}{K\,h\,d}\right),\;'
    r'K = \begin{cases}0.734 & \text{strip}\\ 0.499 & \text{round}\end{cases}',
    '§ *******'
  ),

  EarthingArrangement.buriedCruciform: (
    r'R = \frac{\rho}{2\pi L}'
    r'\,\ln\!\left(\frac{L^{2}}{K\,h\,d}\right),\;'
    r'K = \begin{cases}0.219 & \text{strip}\\ 0.133 & \text{round}\end{cases}',
    '§ *******'
  ),

  // ────────────── Mesh & plate ─────────────────────────────────────────────
  EarthingArrangement.meshGrid: (
    r'R = \frac{\rho}{\pi P}'
    r'\!\left[\ln\!\left(\frac{16A}{P}\right)-1\right]',
    '§ 9.5.6'
  ),

  EarthingArrangement.plate: (
    r'R = \frac{\rho}{4a}',
    '§ 9.5.2'
  ),
}; 