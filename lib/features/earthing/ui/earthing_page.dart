import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';

import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/ui/application_selection/earthing_application_selection_page.dart';

class EarthingPage extends HookWidget {
  const EarthingPage({super.key});

  Future<bool> _showExitDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Earthing Calculator?'),
        content: const Text('Any unsaved calculations will be lost. Are you sure you want to leave?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Leave'),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => EarthingBloc(),
      child: Builder(
        builder: (context) {
          final baseTheme = Theme.of(context);
          final enlargedTextTheme = baseTheme.textTheme.apply(fontSizeFactor: 1.35);
          final enlarged = baseTheme.copyWith(
            textTheme: enlargedTextTheme,
            listTileTheme: baseTheme.listTileTheme.copyWith(
              titleTextStyle: enlargedTextTheme.bodyLarge,
              subtitleTextStyle: enlargedTextTheme.bodyMedium,
            ),
          );
          return Theme(
            data: enlarged,
            child: PopScope(
              canPop: false,
              onPopInvokedWithResult: (didPop, result) async {
                if (didPop) return;
                final shouldPop = await _showExitDialog(context);
                if (shouldPop && context.mounted) {
                  context.go('/modules');
                }
              },
              child: const EarthingApplicationSelectionPage(),
            ),
          );
        },
      ),
    );
  }
}