import 'dart:io';
import 'package:flutter/material.dart';
import 'package:pis_core/core/services/report_to_pdf_service.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/core/widgets/report_preview/common_report_success_dialog.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';
import 'package:pis_core/features/earthing/ui/soil_report_preview/widgets/soil_report_header_section.dart';
import 'package:pis_core/features/earthing/ui/soil_report_preview/widgets/soil_project_info_section.dart';
import 'package:pis_core/features/earthing/ui/soil_report_preview/widgets/soil_executive_summary_section.dart';
import 'package:pis_core/features/earthing/ui/soil_report_preview/widgets/soil_wenner_data_section.dart';
import 'package:pis_core/features/earthing/ui/soil_report_preview/widgets/soil_technical_notes_section.dart';
import 'dart:developer' as developer;

class SoilReportPreviewPage extends StatefulWidget {
  final EarthingCalculationInput input;

  const SoilReportPreviewPage({
    super.key,
    required this.input,
  });

  @override
  State<SoilReportPreviewPage> createState() => _SoilReportPreviewPageState();
}

class _SoilReportPreviewPageState extends State<SoilReportPreviewPage> {
  bool _isGeneratingPdf = false;
  final GlobalKey _repaintBoundaryKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text('Soil Resistivity Report Preview'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        elevation: 0,
        actions: [
          // Check if we have Wenner data
          if (widget.input.wennerRReadings.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: ElevatedButton.icon(
                onPressed: _isGeneratingPdf ? null : () => _generatePdf(widget.input),
                icon: _isGeneratingPdf
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.picture_as_pdf),
                label: Text(_isGeneratingPdf ? 'Generating...' : 'Generate PDF'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryRed,
                  foregroundColor: AppColors.white,
                  elevation: 2,
                ),
              ),
            ),
        ],
      ),
      body: widget.input.wennerRReadings.isEmpty
          ? const Center(
              child: Text('No Wenner soil resistivity data available'),
            )
          : _buildReportPreview(widget.input),
    );
  }

  Widget _buildReportPreview(EarthingCalculationInput input) {
    return Container(
      color: Colors.grey[100],
      child: Center(
        child: Container(
          width: 800, // A4-like width
          margin: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: RepaintBoundary(
              key: _repaintBoundaryKey,
              child: Container(
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Report Header
                    const SoilReportHeaderSection(),

                    const SizedBox(height: 30),

                    // Project Information
                    SoilProjectInfoSection(input: input),

                    const SizedBox(height: 30),

                    // Executive Summary
                    SoilExecutiveSummarySection(input: input),

                    const SizedBox(height: 30),

                    // Wenner Data
                    SoilWennerDataSection(input: input),

                    const SizedBox(height: 30),

                    // Technical Notes
                    SoilTechnicalNotesSection(input: input),

                    const SizedBox(height: 50),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _generatePdf(EarthingCalculationInput input) async {
    if (_isGeneratingPdf) return;

    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      // Generate PDF from the exact preview widget
      final File reportFile = await ReportToPdfService.captureWidgetAsPdf(
        repaintBoundaryKey: _repaintBoundaryKey,
        filename: ReportToPdfService.generateFilename('Soil_Resistivity'),
      );

      if (mounted) {
        CommonReportSuccessDialog.show(context, reportFile, 'Soil Resistivity Analysis Report');
      }
    } catch (e, stackTrace) {
      developer.log(
        'Error generating soil resistivity PDF report',
        error: e,
        stackTrace: stackTrace,
        name: 'SoilReportPreview',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to generate PDF report. Please try again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      setState(() {
        _isGeneratingPdf = false;
      });
    }
  }
}
