import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';

class SoilTechnicalNotesSection extends StatelessWidget {
  final EarthingCalculationInput input;

  const SoilTechnicalNotesSection({
    super.key,
    required this.input,
  });

  @override
  Widget build(BuildContext context) {
    final averageRho = _calculateAverageResistivity();
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.notes, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'TECHNICAL NOTES & ANALYSIS',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Technical Notes Content
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryBlue, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Methodology
                _buildNoteSection(
                  context,
                  'MEASUREMENT METHODOLOGY',
                  'This soil resistivity analysis follows BS 7430:2011 Clause 10.2.2 using the Wenner four-pin method.',
                  Icons.science,
                  AppColors.primaryBlue,
                ),
                
                const SizedBox(height: 20),
                
                // Wenner Method Details
                _buildNoteSection(
                  context,
                  'WENNER METHOD DETAILS',
                  '• Formula: ρ = 2πRX\n'
                  '• Standard spacing sequence: 1, 2, 3, 4 meters\n'
                  '• Four-pin electrode configuration\n'
                  '• Current injection through outer pins\n'
                  '• Voltage measurement across inner pins',
                  Icons.electrical_services,
                  AppColors.primaryRed,
                ),
                
                const SizedBox(height: 20),
                
                // Test Results
                _buildNoteSection(
                  context,
                  'TEST RESULTS SUMMARY',
                  '• Number of test zones: ${input.wennerRReadings.length}\n'
                  '• Total measurements: ${input.wennerRReadings.fold(0, (sum, zone) => sum + zone.length)}\n'
                  '• Average soil resistivity: ${averageRho.toStringAsFixed(2)} Ω·m\n'
                  '• Soil classification: ${_classifySoil(averageRho)}',
                  Icons.assessment,
                  Colors.orange,
                ),
                
                const SizedBox(height: 20),
                
                // Soil Analysis
                _buildSoilAnalysis(context, averageRho),
                
                const SizedBox(height: 20),
                
                // Design Recommendations
                _buildDesignRecommendations(context, averageRho),
                
                const SizedBox(height: 20),
                
                // Standards and References
                _buildStandardsReferences(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteSection(
    BuildContext context,
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 18),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.darkGrey,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSoilAnalysis(BuildContext context, double averageRho) {
    final Color analysisColor = _getSoilClassificationColor(averageRho);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: analysisColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: analysisColor, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.terrain,
                color: analysisColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'SOIL ANALYSIS',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: analysisColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _getSoilAnalysisText(averageRho),
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: analysisColor,
              fontWeight: FontWeight.w600,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getDetailedAnalysis(averageRho),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.darkGrey,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesignRecommendations(BuildContext context, double averageRho) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.engineering, color: Colors.green, size: 18),
              const SizedBox(width: 8),
              Text(
                'EARTHING DESIGN RECOMMENDATIONS',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ..._getDesignRecommendations(averageRho).map((recommendation) => 
            _buildRecommendationItem(context, recommendation)
          ),
        ],
      ),
    );
  }

  Widget _buildStandardsReferences(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.library_books, color: Colors.grey[600], size: 18),
              const SizedBox(width: 8),
              Text(
                'STANDARDS & REFERENCES',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.grey[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildReferenceItem(context, 'BS 7430:2011', 'Code of practice for protective earthing of electrical installations'),
          _buildReferenceItem(context, 'Clause 10.2.2', 'Wenner four-pin method for soil resistivity measurement'),
          _buildReferenceItem(context, 'IEEE Std 81', 'Guide for measuring earth resistivity, ground impedance, and earth surface potentials'),
          _buildReferenceItem(context, 'IEC 62561', 'Lightning protection system components'),
        ],
      ),
    );
  }

  Widget _buildRecommendationItem(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.green,
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.darkGrey,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReferenceItem(BuildContext context, String standard, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[700],
                  height: 1.3,
                ),
                children: [
                  TextSpan(
                    text: '$standard: ',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  TextSpan(text: description),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _calculateAverageResistivity() {
    double totalRho = 0;
    int totalMeasurements = 0;
    
    for (int i = 0; i < input.wennerRReadings.length; i++) {
      if (i < input.wennerXReadings.length) {
        final rReadings = input.wennerRReadings[i];
        final xReadings = input.wennerXReadings[i];
        for (int j = 0; j < rReadings.length && j < xReadings.length; j++) {
          totalRho += 2 * 3.14159 * rReadings[j] * xReadings[j];
          totalMeasurements++;
        }
      }
    }
    
    return totalMeasurements > 0 ? totalRho / totalMeasurements : input.soilResistivity;
  }

  String _classifySoil(double resistivity) {
    if (resistivity < 10) return 'Very Low Resistivity (Wet/Saline)';
    if (resistivity < 100) return 'Low Resistivity (Clay/Loam)';
    if (resistivity < 1000) return 'Medium Resistivity (Sand/Gravel)';
    if (resistivity < 10000) return 'High Resistivity (Rock/Dry Sand)';
    return 'Very High Resistivity (Bedrock)';
  }

  Color _getSoilClassificationColor(double resistivity) {
    if (resistivity < 10) return Colors.blue;
    if (resistivity < 100) return Colors.green;
    if (resistivity < 1000) return Colors.orange;
    if (resistivity < 10000) return Colors.red;
    return Colors.purple;
  }

  String _getSoilAnalysisText(double resistivity) {
    if (resistivity < 10) {
      return 'Excellent soil conditions for earthing systems detected.';
    } else if (resistivity < 100) {
      return 'Good soil conditions for earthing systems.';
    } else if (resistivity < 1000) {
      return 'Moderate soil conditions requiring careful earthing design.';
    } else if (resistivity < 10000) {
      return 'Challenging soil conditions for earthing systems.';
    } else {
      return 'Very difficult soil conditions requiring special measures.';
    }
  }

  String _getDetailedAnalysis(double resistivity) {
    if (resistivity < 10) {
      return 'The measured soil resistivity indicates wet or saline soil conditions with excellent conductivity. Standard earthing designs should achieve very low resistance values. Consider corrosion protection measures due to high moisture content.';
    } else if (resistivity < 100) {
      return 'The soil shows good conductivity typical of clay or loam soils. Standard earthing systems should perform well. Monitor for seasonal variations in soil moisture.';
    } else if (resistivity < 1000) {
      return 'Medium resistivity soil typical of sand or gravel. Earthing systems may require larger electrode areas or deeper installations to achieve target resistance values.';
    } else if (resistivity < 10000) {
      return 'High resistivity soil typical of rock or dry sand. Achieving low earthing resistance will be challenging and may require soil treatment or enhanced earthing materials.';
    } else {
      return 'Very high resistivity soil typical of bedrock or extremely dry conditions. Conventional earthing methods will be ineffective. Chemical soil treatment or alternative earthing methods strongly recommended.';
    }
  }

  List<String> _getDesignRecommendations(double resistivity) {
    if (resistivity < 10) {
      return [
        'Standard earthing electrode designs should be sufficient',
        'Consider corrosion protection due to high moisture/salinity',
        'Regular maintenance and inspection recommended',
        'Monitor for seasonal variations in soil conditions',
      ];
    } else if (resistivity < 100) {
      return [
        'Standard earthing designs should work effectively',
        'Consider seasonal soil moisture variations',
        'Regular testing recommended to verify performance',
        'Standard electrode materials suitable',
      ];
    } else if (resistivity < 1000) {
      return [
        'Larger earthing systems may be required',
        'Consider deeper electrode installations',
        'Multiple parallel electrodes recommended',
        'Soil treatment may be beneficial for critical applications',
      ];
    } else if (resistivity < 10000) {
      return [
        'Soil treatment highly recommended',
        'Consider chemical enhancement materials',
        'Deeper electrodes or enhanced earthing rods required',
        'Multiple earthing points with interconnection',
        'Professional earthing design consultation recommended',
      ];
    } else {
      return [
        'Chemical soil treatment essential',
        'Consider alternative earthing methods',
        'Deep bore earthing systems may be required',
        'Conductive concrete or bentonite enhancement',
        'Specialist earthing contractor consultation required',
      ];
    }
  }
}
