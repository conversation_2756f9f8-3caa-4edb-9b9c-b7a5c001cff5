import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';

class SoilWennerDataSection extends StatelessWidget {
  final EarthingCalculationInput input;

  const SoilWennerDataSection({
    super.key,
    required this.input,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.table_chart,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'WENNER METHOD MEASUREMENTS',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Wenner Data Tables
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryBlue, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Method Description
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: AppColors.primaryBlue,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'WENNER FOUR-PIN METHOD',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Formula: ρ = 2πRX\nWhere: ρ = soil resistivity (Ω·m), R = measured resistance (Ω), X = electrode spacing (m)',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.darkGrey,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Zone Data Tables
                ...List.generate(input.wennerRReadings.length, (zoneIndex) {
                  return _buildZoneTable(context, zoneIndex);
                }),
                
                const SizedBox(height: 20),
                
                // Summary Statistics
                _buildSummaryStatistics(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildZoneTable(BuildContext context, int zoneIndex) {
    final zoneAlias = zoneIndex < input.wennerZoneAliases.length 
        ? input.wennerZoneAliases[zoneIndex] 
        : 'Zone ${zoneIndex + 1}';
    
    final rReadings = input.wennerRReadings[zoneIndex];
    final xReadings = zoneIndex < input.wennerXReadings.length 
        ? input.wennerXReadings[zoneIndex] 
        : <double>[];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Zone Header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.primaryRed.withValues(alpha: 0.1),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
            border: Border.all(color: AppColors.primaryRed.withValues(alpha: 0.3)),
          ),
          child: Text(
            zoneAlias,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: AppColors.primaryRed,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        // Data Table
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!, width: 1),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
          ),
          child: Table(
            border: TableBorder.all(color: Colors.grey[300]!, width: 1),
            columnWidths: const {
              0: FlexColumnWidth(1),
              1: FlexColumnWidth(1),
              2: FlexColumnWidth(1),
              3: FlexColumnWidth(1.5),
            },
            children: [
              // Header Row
              TableRow(
                decoration: BoxDecoration(color: Colors.grey[100]),
                children: [
                  _buildTableCell('Measurement', isHeader: true),
                  _buildTableCell('X (m)', isHeader: true),
                  _buildTableCell('R (Ω)', isHeader: true),
                  _buildTableCell('ρ (Ω·m)', isHeader: true),
                ],
              ),
              // Data Rows
              ...List.generate(rReadings.length, (measurementIndex) {
                final x = measurementIndex < xReadings.length ? xReadings[measurementIndex] : 0.0;
                final r = rReadings[measurementIndex];
                final rho = 2 * 3.14159 * r * x;
                
                return TableRow(
                  decoration: BoxDecoration(
                    color: measurementIndex % 2 == 0 ? Colors.white : Colors.grey[50],
                  ),
                  children: [
                    _buildTableCell('${measurementIndex + 1}'),
                    _buildTableCell(x.toStringAsFixed(1)),
                    _buildTableCell(r.toStringAsFixed(2)),
                    _buildTableCell(rho.toStringAsFixed(2)),
                  ],
                );
              }),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildTableCell(String text, {bool isHeader = false}) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        text,
        style: TextStyle(
          fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
          color: isHeader ? AppColors.darkGrey : AppColors.darkGrey,
          fontSize: 14,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildSummaryStatistics(BuildContext context) {
    final allResistivities = <double>[];
    
    for (int i = 0; i < input.wennerRReadings.length; i++) {
      if (i < input.wennerXReadings.length) {
        final rReadings = input.wennerRReadings[i];
        final xReadings = input.wennerXReadings[i];
        for (int j = 0; j < rReadings.length && j < xReadings.length; j++) {
          final rho = 2 * 3.14159 * rReadings[j] * xReadings[j];
          allResistivities.add(rho);
        }
      }
    }
    
    if (allResistivities.isEmpty) return const SizedBox();
    
    final average = allResistivities.reduce((a, b) => a + b) / allResistivities.length;
    final min = allResistivities.reduce((a, b) => a < b ? a : b);
    final max = allResistivities.reduce((a, b) => a > b ? a : b);
    final range = max - min;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'STATISTICAL SUMMARY',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: AppColors.darkGrey,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(context, 'Average', '${average.toStringAsFixed(2)} Ω·m'),
              ),
              Expanded(
                child: _buildStatItem(context, 'Minimum', '${min.toStringAsFixed(2)} Ω·m'),
              ),
              Expanded(
                child: _buildStatItem(context, 'Maximum', '${max.toStringAsFixed(2)} Ω·m'),
              ),
              Expanded(
                child: _buildStatItem(context, 'Range', '${range.toStringAsFixed(2)} Ω·m'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.darkGrey,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
