import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';

class SoilExecutiveSummarySection extends StatelessWidget {
  final EarthingCalculationInput input;

  const SoilExecutiveSummarySection({
    super.key,
    required this.input,
  });

  @override
  Widget build(BuildContext context) {
    final averageRho = _calculateAverageResistivity();
    final minRho = _getMinResistivity();
    final maxRho = _getMaxResistivity();
    final soilClass = _classifySoil(averageRho);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryRed,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.summarize,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'EXECUTIVE SUMMARY',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Soil Analysis Summary
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryRed, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Resistivity Summary Card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _getSoilClassificationColor(averageRho).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _getSoilClassificationColor(averageRho), width: 2),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.terrain,
                        color: _getSoilClassificationColor(averageRho),
                        size: 32,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Average Resistivity: ${averageRho.toStringAsFixed(2)} Ω·m',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                color: _getSoilClassificationColor(averageRho),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              soilClass,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: _getSoilClassificationColor(averageRho),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Summary Table
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!, width: 1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      _buildSummaryRow(
                        context,
                        'Average Resistivity',
                        '${averageRho.toStringAsFixed(2)} Ω·m',
                        isHighlighted: true,
                      ),
                      _buildSummaryRow(
                        context,
                        'Minimum Resistivity',
                        '${minRho.toStringAsFixed(2)} Ω·m',
                        isHighlighted: true,
                      ),
                      _buildSummaryRow(
                        context,
                        'Maximum Resistivity',
                        '${maxRho.toStringAsFixed(2)} Ω·m',
                        isHighlighted: true,
                      ),
                      _buildSummaryRow(
                        context,
                        'Resistivity Range',
                        '${(maxRho - minRho).toStringAsFixed(2)} Ω·m',
                      ),
                      _buildSummaryRow(
                        context,
                        'Soil Classification',
                        soilClass,
                        statusColor: _getSoilClassificationColor(averageRho),
                      ),
                      _buildSummaryRow(
                        context,
                        'Test Method',
                        'Wenner Four-Pin Method',
                        isLast: true,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Recommendations Box
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.lightbulb_outline,
                            color: AppColors.primaryBlue,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'RECOMMENDATIONS',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _getRecommendations(averageRho),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.darkGrey,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String parameter,
    String value, {
    bool isHighlighted = false,
    bool isLast = false,
    Color? statusColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isHighlighted 
            ? AppColors.primaryRed.withValues(alpha: 0.05)
            : Colors.transparent,
        border: isLast 
            ? null 
            : Border(
                bottom: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                parameter,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGrey,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                padding: statusColor != null 
                    ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
                    : EdgeInsets.zero,
                decoration: statusColor != null
                    ? BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: statusColor, width: 1),
                      )
                    : null,
                child: Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: statusColor ?? (isHighlighted ? AppColors.primaryRed : AppColors.darkGrey),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _calculateAverageResistivity() {
    double totalRho = 0;
    int totalMeasurements = 0;
    
    for (int i = 0; i < input.wennerRReadings.length; i++) {
      if (i < input.wennerXReadings.length) {
        final rReadings = input.wennerRReadings[i];
        final xReadings = input.wennerXReadings[i];
        for (int j = 0; j < rReadings.length && j < xReadings.length; j++) {
          totalRho += 2 * 3.14159 * rReadings[j] * xReadings[j]; // Wenner formula
          totalMeasurements++;
        }
      }
    }
    
    return totalMeasurements > 0 ? totalRho / totalMeasurements : input.soilResistivity;
  }

  double _getMinResistivity() {
    double minRho = double.infinity;
    for (int i = 0; i < input.wennerRReadings.length; i++) {
      if (i < input.wennerXReadings.length) {
        final rReadings = input.wennerRReadings[i];
        final xReadings = input.wennerXReadings[i];
        for (int j = 0; j < rReadings.length && j < xReadings.length; j++) {
          final rho = 2 * 3.14159 * rReadings[j] * xReadings[j];
          if (rho < minRho) minRho = rho;
        }
      }
    }
    return minRho == double.infinity ? input.soilResistivity : minRho;
  }

  double _getMaxResistivity() {
    double maxRho = 0;
    for (int i = 0; i < input.wennerRReadings.length; i++) {
      if (i < input.wennerXReadings.length) {
        final rReadings = input.wennerRReadings[i];
        final xReadings = input.wennerXReadings[i];
        for (int j = 0; j < rReadings.length && j < xReadings.length; j++) {
          final rho = 2 * 3.14159 * rReadings[j] * xReadings[j];
          if (rho > maxRho) maxRho = rho;
        }
      }
    }
    return maxRho > 0 ? maxRho : input.soilResistivity;
  }

  String _classifySoil(double resistivity) {
    if (resistivity < 10) return 'Very Low Resistivity (Wet/Saline)';
    if (resistivity < 100) return 'Low Resistivity (Clay/Loam)';
    if (resistivity < 1000) return 'Medium Resistivity (Sand/Gravel)';
    if (resistivity < 10000) return 'High Resistivity (Rock/Dry Sand)';
    return 'Very High Resistivity (Bedrock)';
  }

  Color _getSoilClassificationColor(double resistivity) {
    if (resistivity < 10) return Colors.blue;
    if (resistivity < 100) return Colors.green;
    if (resistivity < 1000) return Colors.orange;
    if (resistivity < 10000) return Colors.red;
    return Colors.purple;
  }

  String _getRecommendations(double resistivity) {
    if (resistivity < 10) {
      return 'Very low resistivity soil. Excellent for earthing systems. Standard earthing designs should be sufficient. Consider corrosion protection due to high moisture/salinity.';
    } else if (resistivity < 100) {
      return 'Low resistivity soil. Good for earthing systems. Standard earthing designs should work well. Monitor for seasonal variations.';
    } else if (resistivity < 1000) {
      return 'Medium resistivity soil. Moderate earthing performance. May require larger earthing systems or soil treatment for low resistance requirements.';
    } else if (resistivity < 10000) {
      return 'High resistivity soil. Challenging for earthing systems. Consider soil treatment, deeper electrodes, or enhanced earthing materials.';
    } else {
      return 'Very high resistivity soil. Difficult earthing conditions. Soil treatment highly recommended. Consider chemical enhancement or alternative earthing methods.';
    }
  }
}
