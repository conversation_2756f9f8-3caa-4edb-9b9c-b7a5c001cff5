import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/core/widgets/report_preview/common_section_container.dart';
import 'package:pis_core/core/widgets/report_preview/common_info_row.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';

class SoilProjectInfoSection extends StatelessWidget {
  final EarthingCalculationInput input;

  const SoilProjectInfoSection({
    super.key,
    required this.input,
  });

  @override
  Widget build(BuildContext context) {
    return CommonSectionContainer(
      title: 'PROJECT INFORMATION',
      icon: Icons.info_outline,
      headerColor: AppColors.primaryBlue,
      borderColor: AppColors.primaryBlue,
      children: [
        CommonInfoRow(
          parameter: 'Project Name',
          value: input.projectName.isNotEmpty ? input.projectName : 'Not specified',
          isHighlighted: true,
        ),
        CommonInfoRow(
          parameter: 'Main Constructor',
          value: input.mainConstructor.isNotEmpty ? input.mainConstructor : 'Not specified',
          isHighlighted: true,
        ),
        CommonInfoRow(
          parameter: 'Test Method',
          value: 'Wenner Four-Pin Method',
          isHighlighted: true,
        ),
        CommonInfoRow(
          parameter: 'Number of Test Zones',
          value: '${input.wennerRReadings.length}',
          isHighlighted: true,
        ),
        CommonInfoRow(
          parameter: 'Total Measurements',
          value: '${input.wennerRReadings.fold(0, (sum, zone) => sum + zone.length)}',
          isHighlighted: true,
        ),
        CommonInfoRow(
          parameter: 'Report Date',
          value: DateTime.now().toString().split(' ')[0],
          isHighlighted: true,
        ),
        CommonInfoRow(
          parameter: 'Standard',
          value: 'BS 7430:2011 Clause 10.2.2',
          isLast: true,
        ),
      ],
    );
  }
}
