import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/ui/multipage/earthing_multipage_form.dart';

enum EarthingApplicationType {
  infra('Infrastructure', Icons.construction, 'General infrastructure projects'),
  residential('Residential', Icons.home, 'Houses and residential buildings'),
  dataCenter('Data Center', Icons.dns, 'Data centers and server facilities'),
  hospital('Hospital', Icons.local_hospital, 'Medical facilities and hospitals'),
  industrial('Industrial', Icons.factory, 'Manufacturing and industrial plants'),
  agriculture('Agriculture', Icons.agriculture, 'Farms and agricultural facilities'),
  airport('Airport', Icons.flight, 'Airports and aviation facilities'),
  railway('Railway', Icons.train, 'Railway systems and stations'),
  fuelStations('Fuel Stations', Icons.local_gas_station, 'Gas stations and fuel depots'),
  highRise('High Rise Buildings', Icons.apartment, 'Skyscrapers and tall buildings'),
  telecommunications('Telecommunications', Icons.cell_tower, 'Telecom towers and facilities'),
  substations('Substations (HV,MV..)', Icons.electrical_services, 'Electrical substations'),
  solarStations('Solar Stations', Icons.solar_power, 'Solar power plants and installations');

  const EarthingApplicationType(this.label, this.icon, this.description);
  final String label;
  final IconData icon;
  final String description;
}

class EarthingApplicationSelectionPage extends StatefulWidget {
  const EarthingApplicationSelectionPage({super.key});

  @override
  State<EarthingApplicationSelectionPage> createState() => _EarthingApplicationSelectionPageState();
}

class _EarthingApplicationSelectionPageState extends State<EarthingApplicationSelectionPage>
    with TickerProviderStateMixin {
  EarthingApplicationType? _hoveredType;
  EarthingApplicationType? _selectedType;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text('Select Application Type'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              // If we can't pop, go to modules
              context.go('/modules');
            }
          },
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Header Section with PIS Logo
              _buildHeaderSection(),

              const SizedBox(height: 20),

              // Application Selection Grid
              _buildApplicationGrid(),

              const SizedBox(height: 30),

              // Continue Button (now part of scrollable content)
              _buildContinueButton(),

              const SizedBox(height: 20), // Bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primaryBlue, AppColors.primaryBlue.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Title
          Text(
            'Earthing System Design',
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 28,
              shadows: [
                Shadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  offset: const Offset(1, 1),
                  blurRadius: 2,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select the application type for your earthing system design',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 20),

          // PIS Logo - Bigger and integrated into column
          Container(
            width: 500,
            height: 100,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.15),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.asset(
                'assets/logo/PIS logo full.png',
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.engineering,
                        color: AppColors.primaryBlue,
                        size: 40,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'PIS',
                        style: TextStyle(
                          color: AppColors.primaryBlue,
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicationGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Responsive grid based on screen width
        int crossAxisCount;
        if (constraints.maxWidth > 1200) {
          crossAxisCount = 5;
        } else if (constraints.maxWidth > 900) {
          crossAxisCount = 4;
        } else if (constraints.maxWidth > 600) {
          crossAxisCount = 3;
        } else {
          crossAxisCount = 2;
        }

        // Calculate grid height to avoid using Expanded inside ScrollView
        final itemHeight = (constraints.maxWidth / crossAxisCount - 16) / 1.1;
        final rowCount = (EarthingApplicationType.values.length / crossAxisCount).ceil();
        final gridHeight = (rowCount * itemHeight) + ((rowCount - 1) * 16);

        return SizedBox(
          height: gridHeight,
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(), // Disable grid scrolling
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              childAspectRatio: 1.1,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: EarthingApplicationType.values.length,
            itemBuilder: (context, index) {
              final applicationType = EarthingApplicationType.values[index];
              return _buildApplicationCard(applicationType);
            },
          ),
        );
      },
    );
  }

  Widget _buildApplicationCard(EarthingApplicationType type) {
    final isHovered = _hoveredType == type;
    final isSelected = _selectedType == type;
    
    return MouseRegion(
      onEnter: (_) => setState(() => _hoveredType = type),
      onExit: (_) => setState(() => _hoveredType = null),
      child: GestureDetector(
        onTap: () => setState(() => _selectedType = type),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          transform: Matrix4.identity()
            ..scale(isHovered ? 1.05 : 1.0),
          decoration: BoxDecoration(
            color: isSelected 
                ? AppColors.primaryBlue.withValues(alpha: 0.1)
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected 
                  ? AppColors.primaryBlue
                  : isHovered 
                      ? AppColors.primaryBlue.withValues(alpha: 0.5)
                      : Colors.grey[300]!,
              width: isSelected ? 3 : isHovered ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: isHovered ? 0.15 : 0.08),
                blurRadius: isHovered ? 12 : 6,
                offset: Offset(0, isHovered ? 6 : 3),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppColors.primaryBlue
                        : isHovered 
                            ? AppColors.primaryBlue.withValues(alpha: 0.1)
                            : Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    type.icon,
                    size: 32,
                    color: isSelected 
                        ? Colors.white
                        : isHovered 
                            ? AppColors.primaryBlue
                            : Colors.grey[600],
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // Label
                Text(
                  type.label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isSelected 
                        ? AppColors.primaryBlue
                        : Colors.grey[800],
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: 4),
                
                // Description
                Text(
                  type.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContinueButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _selectedType != null ? _continueToCalculations : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryBlue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 4,
          shadowColor: AppColors.primaryBlue.withValues(alpha: 0.3),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.arrow_forward, size: 20),
            const SizedBox(width: 12),
            Text(
              _selectedType != null
                  ? 'Continue with ${_selectedType!.label}'
                  : 'Select an Application Type',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _continueToCalculations() {
    if (_selectedType == null) return;

    // Navigate to earthing calculations
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (_) => EarthingBloc(),
          child: Builder(
            builder: (context) {
              final baseTheme = Theme.of(context);
              final enlargedTextTheme = baseTheme.textTheme.apply(fontSizeFactor: 1.35);
              final enlarged = baseTheme.copyWith(
                textTheme: enlargedTextTheme,
                listTileTheme: baseTheme.listTileTheme.copyWith(
                  titleTextStyle: enlargedTextTheme.bodyLarge,
                  subtitleTextStyle: enlargedTextTheme.bodyMedium,
                ),
              );
              return Theme(
                data: enlarged,
                child: EarthingMultipageForm(
                  onExit: () async {
                    // Go back to application selection
                    Navigator.of(context).pop();
                  },
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
