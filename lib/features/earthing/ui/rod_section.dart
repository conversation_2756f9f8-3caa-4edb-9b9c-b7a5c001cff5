import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/widgets/section_card.dart';
import 'package:pis_core/core/widgets/num_field_tile.dart';
import 'package:pis_core/core/widgets/field_tile.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';
import 'package:pis_core/core/widgets/centered_dropdown_form_field.dart';

class RodSection extends HookWidget {
  const RodSection({super.key});

  @override
  Widget build(BuildContext context) {
    final input = context.watch<EarthingBloc>().state.input;

    // Controllers seeded from bloc values
    final rodLenCtrl = useTextEditingController(text: input.rodLength.toString());
    final rodSpacingCtrl = useTextEditingController(text: input.rodSpacing.toString());
    final wellsCtrl = useTextEditingController(text: input.numberOfWells.toString());

    // Focus nodes for numeric fields
    final rodLenFocus = useFocusNode();
    final rodSpacingFocus = useFocusNode();
    final wellsFocus = useFocusNode();

    final rodTypeSel = input.rodType;

    // Encased-rod extras ---------------------------------------------------
    final isEncased = useState<bool>(input.rodEncased);
    final rhoCCtrl = useTextEditingController(text: (input.enhancementResistivity ?? '').toString());
    final infillDCtrl = useTextEditingController(text: (input.infillDiameter ?? '').toString());

    // Keep controllers in sync when bloc state changes
    useEffect(() {
      if (!rodLenFocus.hasFocus) {
        rodLenCtrl.text = input.rodLength.toString();
      }
      if (!rodSpacingFocus.hasFocus) {
        rodSpacingCtrl.text = input.rodSpacing.toString();
      }
      if (!wellsFocus.hasFocus) {
        wellsCtrl.text = input.numberOfWells.toString();
      }
      rhoCCtrl.text = (input.enhancementResistivity ?? '').toString();
      infillDCtrl.text = (input.infillDiameter ?? '').toString();
      return null;
    }, [input]);

    void push({StandardRod? type}) {
      final bloc = context.read<EarthingBloc>();
      final inp = bloc.state.input.copyWith(
        rodLength: double.tryParse(rodLenCtrl.text) ?? input.rodLength,
        rodSpacing: double.tryParse(rodSpacingCtrl.text) ?? input.rodSpacing,
        numberOfWells: int.tryParse(wellsCtrl.text) ?? input.numberOfWells,
        rodType: type ?? rodTypeSel,
        rodEncased: isEncased.value,
        enhancementResistivity: isEncased.value ? double.tryParse(rhoCCtrl.text) : null,
        infillDiameter: isEncased.value ? double.tryParse(infillDCtrl.text) : null,
      );
      bloc.add(EarthingBlocEvent.updateInput(inp));
    }

    return SectionCard(
      title: 'Rod Details',
      description: 'Select standard rod type and spacing parameters.',
      child: Column(
        children: [
          FieldTile(
            label: 'Rod Type',
            info: 'Select standard rod diameter size.',
            child: CenteredDropdownFormField<StandardRod>(
              value: rodTypeSel,
              onChanged: (v) {
                if (v != null) push(type: v);
              },
              items: StandardRod.values
                  .map((e) => DropdownMenuItem(value: e, child: Text(e.label, textAlign: TextAlign.center)))
                  .toList(),
            ),
          ),
          NumFieldTile(label: 'Rod Length', controller: rodLenCtrl, focusNode: rodLenFocus, suffix: 'm', onChanged: () => push()),
          NumFieldTile(label: 'Rod Spacing', controller: rodSpacingCtrl, focusNode: rodSpacingFocus, suffix: 'm', onChanged: () => push()),
          NumFieldTile(label: 'Number of Wells', controller: wellsCtrl, focusNode: wellsFocus, onChanged: () => push()),
          SwitchListTile(
            title: const Text('Rods Encased'),
            subtitle: const Text('Enable Clause 9.5.7 encased-rod model'),
            value: isEncased.value,
            onChanged: (v) {
              isEncased.value = v;
              push();
            },
          ),
          if (isEncased.value) ...[
            NumFieldTile(
              label: 'Enhancement ρc',
              controller: rhoCCtrl,
              focusNode: null,
              suffix: 'Ω·m',
              onChanged: push,
            ),
            NumFieldTile(
              label: 'Infill Diameter',
              controller: infillDCtrl,
              focusNode: null,
              suffix: 'm',
              onChanged: push,
            ),
          ],
        ],
      ),
    );
  }
} 