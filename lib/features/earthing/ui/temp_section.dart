import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/widgets/section_card.dart';
import 'package:pis_core/core/widgets/num_field_tile.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';

class TemperatureSection extends HookWidget {
  const TemperatureSection({super.key});

  @override
  Widget build(BuildContext context) {
    final input = context.watch<EarthingBloc>().state.input;
    final initCtrl = useTextEditingController(text: input.initialTempC.toString());
    final finalCtrl = useTextEditingController(text: input.finalTempC.toString());

    final initFocus = useFocusNode();
    final finalFocus = useFocusNode();

    useEffect(() {
      if (!initFocus.hasFocus) {
        initCtrl.text = input.initialTempC.toString();
      }
      if (!finalFocus.hasFocus) {
        finalCtrl.text = input.finalTempC.toString();
      }
      return null;
    }, [input.initialTempC, input.finalTempC]);

    void push() {
      final bloc = context.read<EarthingBloc>();
      bloc.add(EarthingBlocEvent.updateInput(bloc.state.input.copyWith(
        initialTempC: double.tryParse(initCtrl.text) ?? input.initialTempC,
        finalTempC: double.tryParse(finalCtrl.text) ?? input.finalTempC,
      )));
    }

    return SectionCard(
      title: 'Temperature',
      description: 'Initial and final conductor temperatures for adiabatic equation.',
      child: Column(
        children: [
          NumFieldTile(label: 'Initial Temp', controller: initCtrl, focusNode: initFocus, suffix: '°C', onChanged: push),
          NumFieldTile(label: 'Final Temp', controller: finalCtrl, focusNode: finalFocus, suffix: '°C', onChanged: push),
        ],
      ),
    );
  }
} 