import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/widgets/section_card.dart';
import 'package:pis_core/core/widgets/num_field_tile.dart';
import 'package:pis_core/core/widgets/field_tile.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';
import 'package:pis_core/core/widgets/centered_dropdown_form_field.dart';

class FaultSection extends HookWidget {
  const FaultSection({super.key});

  @override
  Widget build(BuildContext context) {
    final input = context.watch<EarthingBloc>().state.input;

    final voltage = useState<SystemVoltageType>(input.systemVoltage);
    final material = useState<ConductorMaterial>(input.conductorMaterial);

    final faultCurrentCtrl = useTextEditingController(text: input.faultCurrentA.toString());
    final faultDurationCtrl = useTextEditingController(text: (input.faultDurationS).toString());
    final burialCtrl = useTextEditingController(text: input.burialDepth.toString());

    final faultCurrentFocus = useFocusNode();
    final faultDurationFocus = useFocusNode();
    final burialFocus = useFocusNode();

    // Manual cable overrides ---------------------------------------------
    final manualSize = useState<StandardCableSize?>(input.manualCableSize);
    final manualLenCtrl = useTextEditingController(text: input.manualCableLength?.toString() ?? '');
    final manualLenFocus = useFocusNode();

    // keep controllers in sync with bloc state
    useEffect(() {
      if (!faultCurrentFocus.hasFocus) {
        faultCurrentCtrl.text = input.faultCurrentA.toString();
      }
      if (!faultDurationFocus.hasFocus) {
        faultDurationCtrl.text = (input.faultDurationS).toString();
      }
      if (!burialFocus.hasFocus) {
        burialCtrl.text = input.burialDepth.toString();
      }
      if (!manualLenFocus.hasFocus) {
        manualLenCtrl.text = input.manualCableLength?.toString() ?? '';
      }
      return null;
    }, [input]);

    void push() {
      final bloc = context.read<EarthingBloc>();
      bloc.add(EarthingBlocEvent.updateInput(bloc.state.input.copyWith(
        systemVoltage: voltage.value,
        faultCurrentA: double.tryParse(faultCurrentCtrl.text) ?? input.faultCurrentA,
        faultDurationS: double.tryParse(faultDurationCtrl.text) ?? input.faultDurationS,
        conductorMaterial: material.value,
        burialDepth: double.tryParse(burialCtrl.text) ?? input.burialDepth,
        manualCableSize: manualSize.value,
        manualCableLength: manualLenCtrl.text.isEmpty ? null : double.tryParse(manualLenCtrl.text),
      )));
    }

    return SectionCard(
      title: 'Fault & Cable',
      description: 'Short-circuit parameters and conductor options.',
      child: Column(
        children: [
          FieldTile(
            label: 'System Voltage',
            info: 'LV ≤ 1 kV, MV 11 kV as per design data.',
            child: CenteredDropdownFormField<SystemVoltageType>(
              value: voltage.value,
              onChanged: (v) {
                if (v != null) voltage.value = v;
                push();
              },
              items: SystemVoltageType.values
                  .map((e) => DropdownMenuItem(value: e, child: Text(e.label.toUpperCase(), textAlign: TextAlign.center)))
                  .toList(),
            ),
          ),
          NumFieldTile(label: 'Fault Current', controller: faultCurrentCtrl, focusNode: faultCurrentFocus, suffix: 'A', onChanged: push),
          NumFieldTile(label: 'Fault Duration', controller: faultDurationCtrl, focusNode: faultDurationFocus, suffix: 's', onChanged: push),
          FieldTile(
            label: 'Conductor Material',
            info: 'Select cable material for adiabatic sizing.',
            child: CenteredDropdownFormField<ConductorMaterial>(
              value: material.value,
              onChanged: (v) {
                if (v != null) material.value = v;
                push();
              },
              items: ConductorMaterial.values
                  .map((e) => DropdownMenuItem(value: e, child: Text(e.label, textAlign: TextAlign.center)))
                  .toList(),
            ),
          ),
          NumFieldTile(label: 'Burial Depth', controller: burialCtrl, focusNode: burialFocus, suffix: 'm', onChanged: push),
          const Divider(),
          FieldTile(
            label: 'Manual Cable Size',
            info: 'Override auto-sizing and pick a fixed CSA.',
            child: CenteredDropdownFormField<StandardCableSize?>(
              value: manualSize.value,
              onChanged: (v) {
                manualSize.value = v;
                push();
              },
              items: [
                const DropdownMenuItem(value: null, child: Text('Auto', textAlign: TextAlign.center)),
                ...StandardCableSize.values.map((e) => DropdownMenuItem(
                      value: e,
                      child: Text('S${e.areaMm2}', textAlign: TextAlign.center),
                    )),
              ],
            ),
          ),
          NumFieldTile(
            label: 'Manual Cable Length',
            controller: manualLenCtrl,
            focusNode: manualLenFocus,
            suffix: 'm',
            onChanged: push,
          ),
        ],
      ),
    );
  }
} 