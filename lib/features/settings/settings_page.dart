import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/core/theme/theme_helper.dart';
import 'package:pis_core/core/theme/theme_settings_bloc.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/modules'),
        ),
      ),
      body: BlocBuilder<ThemeSettingsBloc, ThemeSettingsState>(
        builder: (context, state) {
          return SingleChildScrollView(
            padding: context.responsiveAllPadding(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Theme Settings Section
                Card(
                  child: Padding(
                    padding: context.responsiveAllPadding(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Theme Settings',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: AppColors.primaryBlue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: context.scaledFontSize(16)),
                        
                        // Font Size
                        Text(
                          'Font Size',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: context.scaledFontSize(8)),
                        Row(
                          children: [
                            Text(
                              'Small',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            Expanded(
                              child: Slider(
                                value: state.fontSize,
                                min: 10.0,
                                max: 20.0,
                                divisions: 20,
                                label: '${state.fontSize.round()}px',
                                onChanged: (value) {
                                  context.read<ThemeSettingsBloc>().add(
                                    ThemeSettingsEvent.changeFontSize(value),
                                  );
                                },
                              ),
                            ),
                            Text(
                              'Large',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                        
                        SizedBox(height: context.scaledFontSize(16)),
                        
                        // Font Size Preview
                        Container(
                          padding: context.responsiveAllPadding(16),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Preview Text',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              SizedBox(height: context.scaledFontSize(8)),
                              Text(
                                'This is how text will appear with the current font size setting. The changes apply immediately.',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                SizedBox(height: context.scaledFontSize(24)),
                
                // Reset Settings
                Card(
                  child: Padding(
                    padding: context.responsiveAllPadding(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Reset Settings',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: AppColors.primaryRed,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: context.scaledFontSize(16)),
                        Text(
                          'Reset all theme settings to their default values.',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        SizedBox(height: context.scaledFontSize(16)),
                        ElevatedButton.icon(
                          onPressed: () {
                            context.read<ThemeSettingsBloc>().add(
                              const ThemeSettingsEvent.reset(),
                            );
                          },
                          icon: const Icon(Icons.restore),
                          label: const Text('Reset to Defaults'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryRed,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                SizedBox(height: context.scaledFontSize(24)),
                
                // Current Settings Info
                Card(
                  child: Padding(
                    padding: context.responsiveAllPadding(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Settings',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: context.scaledFontSize(12)),
                        Text(
                          'Font Size: ${state.fontSize.toStringAsFixed(1)}px',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          'Font Scale: ${(state.fontSize / 14.0).toStringAsFixed(2)}x',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
