// lightning_bloc.dart
// ---------------------------------------------------------------------------
// BLoC for managing lightning protection system risk assessment calculations.
// Follows the same pattern as the earthing BLoC for consistency.
// ---------------------------------------------------------------------------

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/features/lightning/calculation/lightning_formulas.dart';
import 'lightning_event.dart';
import 'lightning_state.dart';

class LightningBloc extends Bloc<LightningEvent, LightningState> {
  LightningBloc() : super(const LightningState.initial()) {
    on<LightningCalculateEvent>(_onCalculate);
    on<LightningResetEvent>(_onReset);
    on<LightningInputChangedEvent>(_onInputChanged);
  }

  void _onCalculate(
    LightningCalculateEvent event,
    Emitter<LightningState> emit,
  ) async {
    emit(const LightningState.loading());
    
    try {
      // Validate input
      if (!validateLightningInput(event.input)) {
        emit(const LightningState.error('Invalid input parameters'));
        return;
      }

      // Perform risk assessment calculation
      final result = performLightningRiskAssessment(input: event.input);
      
      emit(LightningState.success(
        input: event.input,
        result: result,
      ));
    } catch (e) {
      emit(LightningState.error('Calculation failed: ${e.toString()}'));
    }
  }

  void _onReset(
    LightningResetEvent event,
    Emitter<LightningState> emit,
  ) {
    emit(const LightningState.initial());
  }

  void _onInputChanged(
    LightningInputChangedEvent event,
    Emitter<LightningState> emit,
  ) {
    // Optionally trigger automatic recalculation on input change
    // For now, just store the updated input without calculating
    if (state is LightningSuccessState) {
      // Could trigger recalculation here if desired
      add(LightningCalculateEvent(event.input));
    }
    // Do nothing for other states
  }
} 