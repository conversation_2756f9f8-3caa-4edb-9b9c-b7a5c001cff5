// lightning_event.dart
// ---------------------------------------------------------------------------
// Events for the Lightning BLoC
// ---------------------------------------------------------------------------

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';

part 'lightning_event.freezed.dart';

@freezed
abstract class LightningEvent with _$LightningEvent {
  /// Trigger calculation with given input
  const factory LightningEvent.calculate(LightningCalculationInput input) = LightningCalculateEvent;
  
  /// Reset to initial state
  const factory LightningEvent.reset() = LightningResetEvent;
  
  /// Input parameters changed (optionally trigger recalculation)
  const factory LightningEvent.inputChanged(LightningCalculationInput input) = LightningInputChangedEvent;
} 