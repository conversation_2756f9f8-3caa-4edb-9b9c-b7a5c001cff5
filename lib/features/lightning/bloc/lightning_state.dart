// lightning_state.dart
// ---------------------------------------------------------------------------
// States for the Lightning BLoC
// ---------------------------------------------------------------------------

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/features/lightning/calculation/lightning_result.dart';

part 'lightning_state.freezed.dart';

@freezed
abstract class LightningState with _$LightningState {
  /// Initial state before any calculation
  const factory LightningState.initial() = LightningInitialState;
  
  /// Calculation in progress
  const factory LightningState.loading() = LightningLoadingState;
  
  /// Calculation completed successfully
  const factory LightningState.success({
    required LightningCalculationInput input,
    required LightningCalculationResult result,
  }) = LightningSuccessState;
  
  /// Calculation failed with error
  const factory LightningState.error(String message) = LightningErrorState;
} 