# Lightning Protection System (LPS) Risk Assessment

This module implements lightning protection system risk assessment calculations based on NFPA 780 standards.

## Features

- **Risk Assessment Calculation**: Complete NFPA 780 compliant risk assessment
- **Building Analysis**: Collective area calculation for rectangular structures  
- **Coefficient Management**: Location, construction, contents, occupancy, and consequence factors
- **LPS Recommendation**: Automated determination of lightning protection system requirements
- **Protection Level**: Risk-based protection level classification (1-4)

## Implementation

### Core Components

- `lightning_formulas.dart` - All NFPA 780 calculation formulas
- `lightning_input.dart` - Input parameter model with defaults
- `lightning_result.dart` - Comprehensive result model
- `lightning_bloc.dart` - BLoC for state management

### Constants

- `nfpa780_constants.dart` - NFPA 780 lookup tables and coefficients
- Material combinations for construction coefficient (C2)
- Risk level descriptions and abbreviations
- Flash density by geographic location

## Usage

```dart
const input = LightningCalculationInput(
  lengthM: 41.0,
  widthM: 8.0, 
  heightM: 11.0,
  location: 'Egypt',
  flashDensityNg: 1.0,
  locationFactor: LocationFactorCoeff.isolated,
  structureMaterial: StructureMaterial.metal,
  roofMaterial: RoofMaterial.nonmetallic,
  contentsCoeff: StructureContentsCoeff.lowValue,
  occupancyCoeff: StructureOccupancyCoeff.normallyOccupied,
  consequenceCoeff: LightningConsequenceCoeff.continuityNotRequired,
);

final result = performLightningRiskAssessment(input: input);
```

## Calculations

### Collective Area (Rectangular Structure)
```
Ac = L×W + 6H(L + W) + 9πH²
```

### Expected Strike Frequency  
```
Ne = Ng × Ac × C1 × 10⁻⁶
```

### Tolerable Strike Frequency
```
Nt = (1.5 × 10⁻³) / (C2 × C3 × C4 × C5)
```

### Risk Level
```
Risk % = (1 - Nt/Ne) × 100%
```
Excel formula: `1-(C127/C72)` where C127=Nt, C72=Ne

## Coefficients

- **C1 (Location Factor)**: 0.25, 0.5, 1.0, 2.0 based on surrounding structures
- **C2 (Construction)**: 0.5-3.0 based on structure and roof materials  
- **C3 (Contents)**: 0.5-4.0 based on building contents value and combustibility
- **C4 (Occupancy)**: 0.5, 1.0, 3.0 based on occupancy difficulty
- **C5 (Consequence)**: 1.0, 5.0, 10.0 based on environmental impact

## Protection Levels

- **Level 1**: 0-25% risk - Very Low Risk (LPS not typically required)
- **Level 2**: 25-40% risk - Low Risk (LPS may be considered)
- **Level 3**: 40-55% risk - Moderate Risk (LPS recommended)
- **Level 4**: 55%+ risk - High Risk (LPS should be installed)

## Testing

Comprehensive test suite includes:
- Manual calculation verification against reference sheets
- All coefficient lookup combinations
- Edge cases and boundary conditions  
- BLoC state management testing
- Input validation testing

## References

- NFPA 780: Standard for the Installation of Lightning Protection Systems
- Risk assessment calculation sheets (provided images)
- Protection level classification standards 