import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/widgets/section_card.dart';
import 'package:pis_core/core/widgets/num_field_tile.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';

class BuildingSection extends HookWidget {
  const BuildingSection({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<LightningBloc>().state;
    final input = switch (state) {
      LightningSuccessState state => state.input,
      _ => null,
    };

    final lengthCtrl = useTextEditingController(
      text: (input?.lengthM ?? 41.0).toString(),
    );
    final widthCtrl = useTextEditingController(
      text: (input?.widthM ?? 8.0).toString(),
    );
    final heightCtrl = useTextEditingController(
      text: (input?.heightM ?? 11.0).toString(),
    );

    final lengthFocus = useFocusNode();
    final widthFocus = useFocusNode();
    final heightFocus = useFocusNode();

    // Keep controllers in sync when state changes
    useEffect(() {
      if (!lengthFocus.hasFocus && input != null) {
        lengthCtrl.text = input.lengthM.toString();
      }
      if (!widthFocus.hasFocus && input != null) {
        widthCtrl.text = input.widthM.toString();
      }
      if (!heightFocus.hasFocus && input != null) {
        heightCtrl.text = input.heightM.toString();
      }
      return null;
    }, [input?.lengthM, input?.widthM, input?.heightM]);

    void pushUpdate() {
      final bloc = context.read<LightningBloc>();
      final currentInput = input ?? const LightningCalculationInput();
      final updatedInput = currentInput.copyWith(
        lengthM: double.tryParse(lengthCtrl.text) ?? 41.0,
        widthM: double.tryParse(widthCtrl.text) ?? 8.0,
        heightM: double.tryParse(heightCtrl.text) ?? 11.0,
      );
      bloc.add(LightningEvent.calculate(updatedInput));
    }

    return SectionCard(
      title: 'Building Dimensions',
      description: 'Enter the building dimensions used for collective area calculation according to NFPA 780 methodology.',
      child: Column(
        children: [
          NumFieldTile(
            label: 'Length (L)',
            controller: lengthCtrl,
            focusNode: lengthFocus,
            suffix: 'm',
            onChanged: pushUpdate,
            info: 'Longest horizontal dimension of the building at ground level.',
          ),
          NumFieldTile(
            label: 'Width (W)',
            controller: widthCtrl,
            focusNode: widthFocus,
            suffix: 'm',
            onChanged: pushUpdate,
            info: 'Shortest horizontal dimension of the building at ground level.',
          ),
          NumFieldTile(
            label: 'Height (H)',
            controller: heightCtrl,
            focusNode: heightFocus,
            suffix: 'm',
            onChanged: pushUpdate,
            info: 'Vertical dimension from ground level to the highest point of the structure. Higher buildings have increased lightning strike probability.',
          ),
        ],
      ),
    );
  }
} 