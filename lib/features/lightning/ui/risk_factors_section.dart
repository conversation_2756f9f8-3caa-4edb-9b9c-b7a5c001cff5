import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/widgets/section_card.dart';
import 'package:pis_core/core/widgets/field_tile.dart';
import 'package:pis_core/core/widgets/centered_dropdown_form_field.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';

class RiskFactorsSection extends HookWidget {
  const RiskFactorsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<LightningBloc>().state;
    final input = switch (state) {
      LightningSuccessState state => state.input,
      _ => null,
    };

    void pushUpdate({
      StructureContentsCoeff? contentsCoeff,
      StructureOccupancyCoeff? occupancyCoeff,
      LightningConsequenceCoeff? consequenceCoeff,
    }) {
      final bloc = context.read<LightningBloc>();
      final currentInput = input ?? const LightningCalculationInput();
      final updatedInput = currentInput.copyWith(
        contentsCoeff: contentsCoeff ?? input?.contentsCoeff ?? StructureContentsCoeff.lowValue,
        occupancyCoeff: occupancyCoeff ?? input?.occupancyCoeff ?? StructureOccupancyCoeff.normallyOccupied,
        consequenceCoeff: consequenceCoeff ?? input?.consequenceCoeff ?? LightningConsequenceCoeff.continuityNotRequired,
      );
      bloc.add(LightningEvent.calculate(updatedInput));
    }

    return SectionCard(
      title: 'Risk Factors',
      description: 'Select structure characteristics to determine risk assessment coefficients.',
      child: Column(
        children: [
          FieldTile(
            label: 'Structure Contents (C3)',
            info: 'Value and combustibility of structure contents. Higher values indicate more valuable or combustible contents, leading to increased risk coefficients.',
            child: CenteredDropdownFormField<StructureContentsCoeff>(
              value: input?.contentsCoeff ?? StructureContentsCoeff.lowValue,
              onChanged: (value) {
                if (value != null) pushUpdate(contentsCoeff: value);
              },
              items: StructureContentsCoeff.values
                  .map((e) => DropdownMenuItem(
                        value: e,
                        child: Container(
                          height: 48,
                          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  e.description,
                                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
          FieldTile(
            label: 'Structure Occupancy (C4)',
            info: 'Type and density of structure occupancy. Higher occupancy or difficulty in evacuation increases the risk coefficient.',
            child: CenteredDropdownFormField<StructureOccupancyCoeff>(
              value: input?.occupancyCoeff ?? StructureOccupancyCoeff.normallyOccupied,
              onChanged: (value) {
                if (value != null) pushUpdate(occupancyCoeff: value);
              },
              items: StructureOccupancyCoeff.values
                  .map((e) => DropdownMenuItem(
                        value: e,
                        child: Container(
                          height: 48,
                          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  e.description,
                                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
          FieldTile(
            label: 'Lightning Consequence (C5)',
            info: 'Consequence of lightning strike to facility operations. Consider business continuity needs and environmental impact.',
            child: CenteredDropdownFormField<LightningConsequenceCoeff>(
              value: input?.consequenceCoeff ?? LightningConsequenceCoeff.continuityNotRequired,
              onChanged: (value) {
                if (value != null) pushUpdate(consequenceCoeff: value);
              },
              items: LightningConsequenceCoeff.values
                  .map((e) => DropdownMenuItem(
                        value: e,
                        child: Container(
                          height: 48,
                          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  e.description,
                                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }
} 