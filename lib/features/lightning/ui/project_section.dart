import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/widgets/section_card.dart';
import 'package:pis_core/core/widgets/field_tile.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';

class ProjectSection extends HookWidget {
  const ProjectSection({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<LightningBloc>().state;
    final input = switch (state) {
      LightningSuccessState state => state.input,
      _ => null,
    };

    final projectNameCtrl = useTextEditingController(
      text: input?.projectName ?? 'Data Center - Distributor Room',
    );
    final contractorCtrl = useTextEditingController(
      text: input?.mainContractor ?? '<PERSON>',
    );

    final projectNameFocus = useFocusNode();
    final contractorFocus = useFocusNode();

    // Keep controllers in sync when state changes
    useEffect(() {
      if (!projectNameFocus.hasFocus && input != null) {
        projectNameCtrl.text = input.projectName;
      }
      if (!contractorFocus.hasFocus && input != null) {
        contractorCtrl.text = input.mainContractor;
      }
      return null;
    }, [input?.projectName, input?.mainContractor]);

    void pushUpdate() {
      final bloc = context.read<LightningBloc>();
      final currentInput = input ?? const LightningCalculationInput();
      final updatedInput = currentInput.copyWith(
        projectName: projectNameCtrl.text,
        mainContractor: contractorCtrl.text,
      );
      bloc.add(LightningEvent.calculate(updatedInput));
    }

    return SectionCard(
      title: 'Project Information',
      description: 'Enter project and contractor details for documentation and lightning protection assessment report.',
      child: Column(
        children: [
          FieldTile(
            label: 'Project Name',
            info: 'Descriptive name of the project or facility being assessed for lightning protection.',
            child: TextFormField(
              controller: projectNameCtrl,
              focusNode: projectNameFocus,
              decoration: const InputDecoration(
                hintText: 'Enter project name',
                border: OutlineInputBorder(),
              ),
              onFieldSubmitted: (_) => pushUpdate(),
              onEditingComplete: pushUpdate,
            ),
          ),
          FieldTile(
            label: 'Main Contractor',
            info: 'Primary contractor responsible for the construction or lightning protection system installation.',
            child: TextFormField(
              controller: contractorCtrl,
              focusNode: contractorFocus,
              decoration: const InputDecoration(
                hintText: 'Enter contractor name',
                border: OutlineInputBorder(),
              ),
              onFieldSubmitted: (_) => pushUpdate(),
              onEditingComplete: pushUpdate,
            ),
          ),
        ],
      ),
    );
  }
} 