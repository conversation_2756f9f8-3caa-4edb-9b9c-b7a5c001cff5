import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';

import 'package:pis_core/features/lightning/ui/multipage/lightning_multipage_form.dart';

class LightningPage extends HookWidget {
  const LightningPage({super.key});

  Future<bool> _showExitDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Lightning Protection?'),
        content: const Text('Any unsaved calculations will be lost. Are you sure you want to leave?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Leave'),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => LightningBloc(),
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          final shouldPop = await _showExitDialog(context);
          if (shouldPop && context.mounted) {
            context.go('/modules');
          }
        },
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => FocusScope.of(context).unfocus(),
          child: LightningMultipageForm(
            onExit: () async {
              final shouldLeave = await _showExitDialog(context);
              if (shouldLeave && context.mounted) {
                context.go('/modules');
              }
            },
          ),
        ),
      ),
    );
  }
}