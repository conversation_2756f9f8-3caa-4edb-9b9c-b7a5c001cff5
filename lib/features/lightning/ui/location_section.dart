import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/widgets/section_card.dart';
import 'package:pis_core/core/widgets/field_tile.dart';
import 'package:pis_core/core/widgets/centered_dropdown_form_field.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';

class LocationSection extends HookWidget {
  const LocationSection({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<LightningBloc>().state;
    final input = switch (state) {
      LightningSuccessState state => state.input,
      _ => null,
    };

    void pushUpdate({
      String? location,
      double? flashDensityNg,
      LocationFactorCoeff? locationFactor,
    }) {
      final bloc = context.read<LightningBloc>();
      final currentInput = input ?? const LightningCalculationInput();
      final updatedInput = currentInput.copyWith(
        location: location ?? input?.location ?? 'Egypt',
        flashDensityNg: flashDensityNg ?? input?.flashDensityNg ?? 1.0,
        locationFactor: locationFactor ?? input?.locationFactor ?? LocationFactorCoeff.isolated,
      );
      bloc.add(LightningEvent.calculate(updatedInput));
    }

    return SectionCard(
      title: 'Location & Environment',
      description: 'Select location and lightning activity parameters for accurate risk assessment.',
      child: Column(
        children: [
          FieldTile(
            label: 'Location',
            info: 'Select your location to automatically set appropriate flash density values.',
            child: CenteredDropdownFormField<String>(
              value: input?.location ?? 'Egypt',
              onChanged: (value) {
                if (value != null) {
                  final flashDensity = NFPA780Constants.flashDensityByLocation[value] ?? 1.0;
                  pushUpdate(location: value, flashDensityNg: flashDensity);
                }
              },
              items: NFPA780Constants.flashDensityByLocation.entries
                  .map((e) => DropdownMenuItem(
                        value: e.key,
                        child: Container(
                          height: 48,
                          padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                e.key,
                                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                              ),
                              Text(
                                '${e.value} fl/km²/year',
                                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                              ),
                            ],
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
          FieldTile(
            label: 'Flash Density (Ng)',
            info: 'Annual lightning flash density for the selected location. This value is automatically set based on location but can be customized.',
            child: CenteredDropdownFormField<double>(
              value: input?.flashDensityNg ?? 1.0,
              onChanged: (value) {
                if (value != null) pushUpdate(flashDensityNg: value);
              },
              items: [0.5, 1.0, 1.2, 1.3, 1.5, 1.6, 1.8, 2.0, 2.5, 3.0, 4.0, 5.0]
                  .map((e) => DropdownMenuItem(
                        value: e,
                        child: Container(
                          height: 40,
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          alignment: Alignment.centerLeft,
                          child: Text(
                            '$e fl/km²/year',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
          FieldTile(
            label: 'Location Factor (C1)',
            info: 'Relative structure location coefficient based on surrounding structures and terrain. Isolated structures have higher risk.',
            child: CenteredDropdownFormField<LocationFactorCoeff>(
              value: input?.locationFactor ?? LocationFactorCoeff.isolated,
              onChanged: (value) {
                if (value != null) pushUpdate(locationFactor: value);
              },
              items: LocationFactorCoeff.values
                  .map((e) => DropdownMenuItem(
                        value: e,
                        child: Container(
                          height: 48,
                          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  e.description,
                                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }
} 