import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/services/report_to_pdf_service.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/core/widgets/report_preview/common_report_success_dialog.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/features/lightning/calculation/lightning_result.dart';
import 'package:pis_core/features/lightning/ui/report_preview/widgets/report_header_section.dart';
import 'package:pis_core/features/lightning/ui/report_preview/widgets/project_info_section.dart';
import 'package:pis_core/features/lightning/ui/report_preview/widgets/executive_summary_section.dart';
import 'package:pis_core/features/lightning/ui/report_preview/widgets/input_parameters_section.dart';
import 'package:pis_core/features/lightning/ui/report_preview/widgets/calculation_results_section.dart';
import 'package:pis_core/features/lightning/ui/report_preview/widgets/lightning_images_section.dart';
import 'package:pis_core/features/lightning/ui/report_preview/widgets/technical_notes_section.dart';
import 'dart:developer' as developer;

class LightningReportPreviewPage extends StatefulWidget {
  const LightningReportPreviewPage({super.key});

  @override
  State<LightningReportPreviewPage> createState() => _LightningReportPreviewPageState();
}

class _LightningReportPreviewPageState extends State<LightningReportPreviewPage> {
  bool _isGeneratingPdf = false;
  final GlobalKey _repaintBoundaryKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text('Lightning Protection Report Preview'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        elevation: 0,
        actions: [
          BlocBuilder<LightningBloc, LightningState>(
            builder: (context, state) {
              if (state is! LightningSuccessState) {
                return const SizedBox();
              }

              return Padding(
                padding: const EdgeInsets.only(right: 16),
                child: ElevatedButton.icon(
                  onPressed: _isGeneratingPdf ? null : () => _generatePdf(state.input, state.result),
                  icon: _isGeneratingPdf
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.picture_as_pdf),
                  label: Text(_isGeneratingPdf ? 'Generating...' : 'Generate PDF'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryRed,
                    foregroundColor: AppColors.white,
                    elevation: 2,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: BlocBuilder<LightningBloc, LightningState>(
        builder: (context, state) {
          if (state is! LightningSuccessState) {
            return const Center(
              child: Text('No calculation results available'),
            );
          }

          return _buildReportPreview(state.input, state.result);
        },
      ),
    );
  }

  Widget _buildReportPreview(LightningCalculationInput input, LightningCalculationResult result) {
    return Container(
      color: Colors.grey[100],
      child: Center(
        child: Container(
          width: 800, // A4-like width
          margin: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: RepaintBoundary(
              key: _repaintBoundaryKey,
              child: Container(
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Report Header
                    ReportHeaderSection(),

                    const SizedBox(height: 30),

                    // Project Information
                    ProjectInfoSection(input: input),

                    const SizedBox(height: 30),

                    // Executive Summary
                    ExecutiveSummarySection(result: result),

                    const SizedBox(height: 30),
                
                // Input Parameters
                InputParametersSection(input: input),
                
                const SizedBox(height: 30),
                
                // Calculation Results
                CalculationResultsSection(result: result),
                
                const SizedBox(height: 30),
                
                // Lightning Protection Images
                LightningImagesSection(),
                
                const SizedBox(height: 30),
                
                    // Technical Notes
                    TechnicalNotesSection(input: input, result: result),

                    const SizedBox(height: 50),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _generatePdf(LightningCalculationInput input, LightningCalculationResult result) async {
    if (_isGeneratingPdf) return;

    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      // Prepare input data for report (excluding project info which goes in PROJECT INFORMATION section)
      final Map<String, dynamic> inputData = {
        'Building Length': '${input.lengthM} m',
        'Building Width': '${input.widthM} m',
        'Building Height': '${input.heightM} m',
        'Location': input.location,
        'Flash Density (Ng)': '${input.flashDensityNg} fl/km²/year',
        'Location Factor': input.locationFactor.name,
        'Structure Material': input.structureMaterial.name,
        'Roof Material': input.roofMaterial.name,
        'Contents Coefficient': input.contentsCoeff.name,
        'Occupancy Coefficient': input.occupancyCoeff.name,
        'Consequence Coefficient': input.consequenceCoeff.name,
      };

      // Add manual overrides if applicable
      if (input.manualCollectiveArea != null) {
        inputData['Manual Collective Area'] = '${input.manualCollectiveArea} km²';
      }
      if (input.manualConstructionCoeff != null) {
        inputData['Manual Construction Coefficient'] = '${input.manualConstructionCoeff}';
      }

      // Prepare result data for report
      final Map<String, dynamic> resultData = {
        'Collective Area': '${result.collectiveAreaKm2.toStringAsFixed(6)} km²',
        'Expected Strike Frequency': '${result.expectedStrikeFrequency.toStringAsFixed(4)} events/year',
        'Tolerable Strike Frequency': '${result.tolerableStrikeFrequency.toStringAsFixed(4)} events/year',
        'Risk Level': '${result.riskLevel.toStringAsFixed(1)}%',
        'LPS Recommended': result.lpsRecommended ? 'YES' : 'NO',
        'Protection Level': 'Level ${result.protectionLevel}',
        'C1 Location Factor': '${result.c1LocationFactor}',
        'C2 Construction Coefficient': '${result.c2ConstructionCoeff}',
        'C3 Contents Coefficient': '${result.c3ContentsCoeff}',
        'C4 Occupancy Coefficient': '${result.c4OccupancyCoeff}',
        'C5 Consequence Coefficient': '${result.c5ConsequenceCoeff}',
        'Risk Assessment': result.riskLevelDescription,
        'Recommendation': result.recommendationText,
      };

      // Add calculation standards and methods
      resultData['Standard'] = 'NFPA 780:2020';
      resultData['Calculation Method'] = 'Risk Assessment per NFPA 780';
      resultData['Assessment Type'] = 'Lightning Protection System Risk Analysis';



      // Generate PDF from the exact preview widget
      final File reportFile = await ReportToPdfService.captureWidgetAsPdf(
        repaintBoundaryKey: _repaintBoundaryKey,
        filename: ReportToPdfService.generateFilename('Lightning_Protection'),
      );

      if (mounted) {
        CommonReportSuccessDialog.show(context, reportFile, 'Lightning Protection Risk Assessment Report');
      }
    } catch (e, stackTrace) {
      developer.log(
        'Error generating lightning protection PDF report',
        error: e,
        stackTrace: stackTrace,
        name: 'LightningReportPreview',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to generate PDF report. Please try again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      setState(() {
        _isGeneratingPdf = false;
      });
    }
  }




}
