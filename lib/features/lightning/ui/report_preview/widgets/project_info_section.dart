import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';

class ProjectInfoSection extends StatelessWidget {
  final LightningCalculationInput input;

  const ProjectInfoSection({
    super.key,
    required this.input,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'PROJECT INFORMATION',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Project Information Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryBlue, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              children: [
                _buildInfoRow(
                  context,
                  'Project Name',
                  input.projectName.isNotEmpty ? input.projectName : 'Not specified',
                  isHighlighted: true,
                ),
                _buildInfoRow(
                  context,
                  'Main Constructor',
                  input.mainContractor.isNotEmpty ? input.mainContractor : 'Not specified',
                  isHighlighted: true,
                ),
                _buildInfoRow(
                  context,
                  'Report Date',
                  DateTime.now().toString().split(' ')[0],
                  isHighlighted: true,
                ),
                _buildInfoRow(
                  context,
                  'Standard',
                  'NFPA 780:2020',
                  isLast: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String parameter,
    String value, {
    bool isHighlighted = false,
    bool isLast = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isHighlighted 
            ? AppColors.primaryBlue.withValues(alpha: 0.05)
            : Colors.transparent,
        border: isLast 
            ? null 
            : Border(
                bottom: BorderSide(
                  color: AppColors.primaryBlue.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                parameter,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGrey,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isHighlighted ? AppColors.primaryBlue : AppColors.darkGrey,
                  fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
