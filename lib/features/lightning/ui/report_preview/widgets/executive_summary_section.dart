import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/calculation/lightning_result.dart';

class ExecutiveSummarySection extends StatelessWidget {
  final LightningCalculationResult result;

  const ExecutiveSummarySection({
    super.key,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryRed,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.summarize,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'EXECUTIVE SUMMARY',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Risk Assessment Summary
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryRed, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Risk Level Card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _getRiskLevelColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _getRiskLevelColor(), width: 2),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getRiskLevelIcon(),
                        color: _getRiskLevelColor(),
                        size: 32,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Risk Level: ${result.riskLevel.toStringAsFixed(1)}%',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                color: _getRiskLevelColor(),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              result.riskLevelDescription,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: _getRiskLevelColor(),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Summary Table
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!, width: 1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      _buildSummaryRow(
                        context,
                        'LPS Required',
                        result.lpsRecommended ? 'YES' : 'NO',
                        isHighlighted: true,
                        statusColor: result.lpsRecommended ? Colors.red : Colors.green,
                      ),
                      _buildSummaryRow(
                        context,
                        'Protection Level',
                        'Level ${result.protectionLevel}',
                        isHighlighted: true,
                      ),
                      _buildSummaryRow(
                        context,
                        'Compliance Status',
                        result.lpsRecommended ? 'PROTECTION REQUIRED' : 'ACCEPTABLE RISK',
                        isLast: true,
                        statusColor: result.lpsRecommended ? Colors.red : Colors.green,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Recommendation Box
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.lightbulb_outline,
                            color: AppColors.primaryBlue,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'RECOMMENDATION',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        result.recommendationText,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.darkGrey,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String parameter,
    String value, {
    bool isHighlighted = false,
    bool isLast = false,
    Color? statusColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isHighlighted 
            ? AppColors.primaryRed.withValues(alpha: 0.05)
            : Colors.transparent,
        border: isLast 
            ? null 
            : Border(
                bottom: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                parameter,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGrey,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                padding: statusColor != null 
                    ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
                    : EdgeInsets.zero,
                decoration: statusColor != null
                    ? BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: statusColor, width: 1),
                      )
                    : null,
                child: Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: statusColor ?? (isHighlighted ? AppColors.primaryRed : AppColors.darkGrey),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getRiskLevelColor() {
    if (result.riskLevel > 100) {
      return Colors.red;
    } else if (result.riskLevel > 50) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  IconData _getRiskLevelIcon() {
    if (result.riskLevel > 100) {
      return Icons.warning;
    } else if (result.riskLevel > 50) {
      return Icons.info;
    } else {
      return Icons.check_circle;
    }
  }
}
