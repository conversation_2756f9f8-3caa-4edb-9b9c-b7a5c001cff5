import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';

class InputParametersSection extends StatelessWidget {
  final LightningCalculationInput input;

  const InputParametersSection({
    super.key,
    required this.input,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.input,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'INPUT PARAMETERS',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Input Parameters Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryBlue, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              children: [
                // Building Dimensions
                _buildCategoryHeader(context, 'Building Dimensions'),
                _buildInputRow(
                  context,
                  'Building Length',
                  '${input.lengthM} m',
                  isImportant: true,
                ),
                _buildInputRow(
                  context,
                  'Building Width',
                  '${input.widthM} m',
                  isImportant: true,
                ),
                _buildInputRow(
                  context,
                  'Building Height',
                  '${input.heightM} m',
                  isImportant: true,
                ),
                
                // Location Information
                _buildCategoryHeader(context, 'Location Information'),
                _buildInputRow(
                  context,
                  'Location',
                  input.location,
                  isImportant: true,
                ),
                _buildInputRow(
                  context,
                  'Flash Density (Ng)',
                  '${input.flashDensityNg} fl/km²/year',
                  isImportant: true,
                ),
                _buildInputRow(
                  context,
                  'Location Factor',
                  _getLocationFactorDescription(input.locationFactor),
                ),
                
                // Structure Properties
                _buildCategoryHeader(context, 'Structure Properties'),
                _buildInputRow(
                  context,
                  'Structure Material',
                  _getStructureMaterialDescription(input.structureMaterial),
                ),
                _buildInputRow(
                  context,
                  'Roof Material',
                  _getRoofMaterialDescription(input.roofMaterial),
                ),
                
                // Risk Coefficients
                _buildCategoryHeader(context, 'Risk Assessment Coefficients'),
                _buildInputRow(
                  context,
                  'Contents Coefficient',
                  _getContentsCoeffDescription(input.contentsCoeff),
                ),
                _buildInputRow(
                  context,
                  'Occupancy Coefficient',
                  _getOccupancyCoeffDescription(input.occupancyCoeff),
                ),
                _buildInputRow(
                  context,
                  'Consequence Coefficient',
                  _getConsequenceCoeffDescription(input.consequenceCoeff),
                  isLast: true,
                ),
                
                // Manual Overrides (if any)
                if (input.manualCollectiveArea != null || input.manualConstructionCoeff != null) ...[
                  _buildCategoryHeader(context, 'Manual Overrides'),
                  if (input.manualCollectiveArea != null)
                    _buildInputRow(
                      context,
                      'Manual Collective Area',
                      '${input.manualCollectiveArea} km²',
                      isImportant: true,
                    ),
                  if (input.manualConstructionCoeff != null)
                    _buildInputRow(
                      context,
                      'Manual Construction Coefficient',
                      '${input.manualConstructionCoeff}',
                      isImportant: true,
                      isLast: input.manualConstructionCoeff != null,
                    ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryHeader(BuildContext context, String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: AppColors.primaryBlue.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppColors.primaryBlue,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildInputRow(
    BuildContext context,
    String parameter,
    String value, {
    bool isImportant = false,
    bool isLast = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isImportant 
            ? AppColors.primaryBlue.withValues(alpha: 0.03)
            : Colors.transparent,
        border: isLast 
            ? null 
            : Border(
                bottom: BorderSide(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                parameter,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGrey,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isImportant ? AppColors.primaryBlue : AppColors.darkGrey,
                  fontWeight: isImportant ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getLocationFactorDescription(LocationFactorCoeff factor) {
    switch (factor) {
      case LocationFactorCoeff.isolated:
        return 'Isolated (${factor.value})';
      case LocationFactorCoeff.isolatedOnHilltop:
        return 'Isolated on Hilltop (${factor.value})';
      case LocationFactorCoeff.surroundedByEqual:
        return 'Surrounded by Equal Height (${factor.value})';
      case LocationFactorCoeff.surroundedByTaller:
        return 'Surrounded by Taller (${factor.value})';
    }
  }

  String _getStructureMaterialDescription(StructureMaterial material) {
    switch (material) {
      case StructureMaterial.metal:
        return 'Metal';
      case StructureMaterial.nonmetallic:
        return 'Non-metallic';
      case StructureMaterial.combustible:
        return 'Combustible';
    }
  }

  String _getRoofMaterialDescription(RoofMaterial material) {
    switch (material) {
      case RoofMaterial.metal:
        return 'Metal';
      case RoofMaterial.nonmetallic:
        return 'Non-metallic';
      case RoofMaterial.combustible:
        return 'Combustible';
    }
  }

  String _getContentsCoeffDescription(StructureContentsCoeff coeff) {
    switch (coeff) {
      case StructureContentsCoeff.lowValue:
        return 'Low Value (${coeff.value})';
      case StructureContentsCoeff.standardValue:
        return 'Standard Value (${coeff.value})';
      case StructureContentsCoeff.highValue:
        return 'High Value (${coeff.value})';
      case StructureContentsCoeff.exceptionalValue:
        return 'Exceptional Value (${coeff.value})';
      case StructureContentsCoeff.irreplaceableValue:
        return 'Irreplaceable Value (${coeff.value})';
    }
  }

  String _getOccupancyCoeffDescription(StructureOccupancyCoeff coeff) {
    switch (coeff) {
      case StructureOccupancyCoeff.unoccupied:
        return 'Unoccupied (${coeff.value})';
      case StructureOccupancyCoeff.normallyOccupied:
        return 'Normally Occupied (${coeff.value})';
      case StructureOccupancyCoeff.difficultEvacuation:
        return 'Difficult Evacuation (${coeff.value})';
    }
  }

  String _getConsequenceCoeffDescription(LightningConsequenceCoeff coeff) {
    switch (coeff) {
      case LightningConsequenceCoeff.continuityNotRequired:
        return 'Continuity Not Required (${coeff.value})';
      case LightningConsequenceCoeff.continuityRequired:
        return 'Continuity Required (${coeff.value})';
      case LightningConsequenceCoeff.consequenceToEnvironment:
        return 'Consequence to Environment (${coeff.value})';
    }
  }
}
