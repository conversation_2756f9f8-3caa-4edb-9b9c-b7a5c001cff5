import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/calculation/lightning_result.dart';

class CalculationResultsSection extends StatelessWidget {
  final LightningCalculationResult result;

  const CalculationResultsSection({
    super.key,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryRed,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calculate,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'CALCULATION RESULTS',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Results Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryRed, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              children: [
                // Key Results
                _buildCategoryHeader(context, 'Key Assessment Results'),
                _buildResultRow(
                  context,
                  'Risk Level',
                  '${result.riskLevel.toStringAsFixed(1)}%',
                  isHighlighted: true,
                  statusColor: _getRiskLevelColor(),
                ),
                _buildResultRow(
                  context,
                  'LPS Recommended',
                  result.lpsRecommended ? 'YES' : 'NO',
                  isHighlighted: true,
                  statusColor: result.lpsRecommended ? Colors.red : Colors.green,
                ),
                _buildResultRow(
                  context,
                  'Protection Level',
                  'Level ${result.protectionLevel}',
                  isHighlighted: true,
                ),
                
                // Frequency Calculations
                _buildCategoryHeader(context, 'Strike Frequency Analysis'),
                _buildResultRow(
                  context,
                  'Collective Area',
                  '${result.collectiveAreaKm2.toStringAsFixed(6)} km²',
                ),
                _buildResultRow(
                  context,
                  'Expected Strike Frequency (Ne)',
                  '${result.expectedStrikeFrequency.toStringAsFixed(4)} events/year',
                ),
                _buildResultRow(
                  context,
                  'Tolerable Strike Frequency (Nt)',
                  '${result.tolerableStrikeFrequency.toStringAsFixed(4)} events/year',
                ),
                
                // Coefficient Values
                _buildCategoryHeader(context, 'Risk Assessment Coefficients'),
                _buildResultRow(
                  context,
                  'C1 - Location Factor',
                  '${result.c1LocationFactor}',
                ),
                _buildResultRow(
                  context,
                  'C2 - Construction Coefficient',
                  '${result.c2ConstructionCoeff}',
                ),
                _buildResultRow(
                  context,
                  'C3 - Contents Coefficient',
                  '${result.c3ContentsCoeff}',
                ),
                _buildResultRow(
                  context,
                  'C4 - Occupancy Coefficient',
                  '${result.c4OccupancyCoeff}',
                ),
                _buildResultRow(
                  context,
                  'C5 - Consequence Coefficient',
                  '${result.c5ConsequenceCoeff}',
                ),
                
                // Assessment Details
                _buildCategoryHeader(context, 'Assessment Details'),
                _buildResultRow(
                  context,
                  'Standard',
                  'NFPA 780:2020',
                ),
                _buildResultRow(
                  context,
                  'Calculation Method',
                  'Risk Assessment per NFPA 780',
                ),
                _buildResultRow(
                  context,
                  'Assessment Type',
                  'Lightning Protection System Risk Analysis',
                  isLast: true,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Risk Assessment Summary Card
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: _getRiskLevelColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: _getRiskLevelColor(), width: 2),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _getRiskLevelIcon(),
                      color: _getRiskLevelColor(),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'RISK ASSESSMENT CONCLUSION',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: _getRiskLevelColor(),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  result.riskLevelDescription,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: _getRiskLevelColor(),
                    fontWeight: FontWeight.w600,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  result.recommendationText,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.darkGrey,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryHeader(BuildContext context, String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primaryRed.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: AppColors.primaryRed.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppColors.primaryRed,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildResultRow(
    BuildContext context,
    String parameter,
    String value, {
    bool isHighlighted = false,
    bool isLast = false,
    Color? statusColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isHighlighted 
            ? AppColors.primaryRed.withValues(alpha: 0.03)
            : Colors.transparent,
        border: isLast 
            ? null 
            : Border(
                bottom: BorderSide(
                  color: AppColors.primaryRed.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                parameter,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGrey,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                padding: statusColor != null 
                    ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
                    : EdgeInsets.zero,
                decoration: statusColor != null
                    ? BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: statusColor, width: 1),
                      )
                    : null,
                child: Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: statusColor ?? (isHighlighted ? AppColors.primaryRed : AppColors.darkGrey),
                    fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getRiskLevelColor() {
    if (result.riskLevel > 100) {
      return Colors.red;
    } else if (result.riskLevel > 50) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  IconData _getRiskLevelIcon() {
    if (result.riskLevel > 100) {
      return Icons.warning;
    } else if (result.riskLevel > 50) {
      return Icons.info;
    } else {
      return Icons.check_circle;
    }
  }
}
