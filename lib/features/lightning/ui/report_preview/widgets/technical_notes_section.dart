import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/features/lightning/calculation/lightning_result.dart';

class TechnicalNotesSection extends StatelessWidget {
  final LightningCalculationInput input;
  final LightningCalculationResult result;

  const TechnicalNotesSection({
    super.key,
    required this.input,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.notes, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'TECHNICAL NOTES & RECOMMENDATIONS',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          
          // Technical Notes Content
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryBlue, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Methodology
                _buildNoteSection(
                  context,
                  'LIGHTNING PROTECTION RISK ASSESSMENT METHODOLOGY',
                  'This lightning protection risk assessment follows NFPA 780:2020 standard.',
                  Icons.science,
                  AppColors.primaryBlue,
                ),
                
                const SizedBox(height: 20),
                
                // Building Information
                _buildNoteSection(
                  context,
                  'BUILDING INFORMATION',
                  '• Dimensions: ${input.lengthM}m × ${input.widthM}m × ${input.heightM}m\n'
                  '• Location: ${input.location}\n'
                  '• Flash Density: ${input.flashDensityNg} fl/km²/year',
                  Icons.business,
                  AppColors.primaryRed,
                ),
                
                const SizedBox(height: 20),
                
                // Risk Calculation
                _buildNoteSection(
                  context,
                  'RISK CALCULATION',
                  '• Expected Strike Frequency (Ne) = ${result.expectedStrikeFrequency.toStringAsFixed(4)} events/year\n'
                  '• Tolerable Strike Frequency (Nt) = ${result.tolerableStrikeFrequency.toStringAsFixed(4)} events/year\n'
                  '• Risk Level = ${result.riskLevel.toStringAsFixed(1)}%',
                  Icons.calculate,
                  Colors.orange,
                ),
                
                const SizedBox(height: 20),
                
                // Coefficient Values
                _buildNoteSection(
                  context,
                  'COEFFICIENT VALUES',
                  '• C1 (Location Factor): ${result.c1LocationFactor}\n'
                  '• C2 (Construction): ${result.c2ConstructionCoeff}\n'
                  '• C3 (Contents): ${result.c3ContentsCoeff}\n'
                  '• C4 (Occupancy): ${result.c4OccupancyCoeff}\n'
                  '• C5 (Consequence): ${result.c5ConsequenceCoeff}',
                  Icons.settings,
                  Colors.purple,
                ),
                
                const SizedBox(height: 20),
                
                // Assessment Result
                _buildHighlightedResult(context),
                
                const SizedBox(height: 20),
                
                // Protection Requirements
                _buildProtectionRequirements(context),
                
                const SizedBox(height: 20),
                
                // Compliance Notes
                _buildComplianceNotes(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteSection(
    BuildContext context,
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 18),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.darkGrey,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHighlightedResult(BuildContext context) {
    final Color resultColor = result.lpsRecommended ? Colors.red : Colors.green;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: resultColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: resultColor, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                result.lpsRecommended ? Icons.warning : Icons.check_circle,
                color: resultColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'ASSESSMENT RESULT',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: resultColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            result.riskLevelDescription,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: resultColor,
              fontWeight: FontWeight.w600,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: resultColor.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.lightbulb_outline, color: resultColor, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    result.recommendationText,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.darkGrey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProtectionRequirements(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primaryRed.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primaryRed.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.security, color: AppColors.primaryRed, size: 18),
              const SizedBox(width: 8),
              Text(
                'PROTECTION REQUIREMENTS',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.primaryRed,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildRequirementItem(
            context,
            result.lpsRecommended 
                ? 'Lightning Protection System (LPS) is REQUIRED'
                : 'Lightning Protection System (LPS) is NOT REQUIRED',
            result.lpsRecommended ? Icons.warning : Icons.check_circle,
            result.lpsRecommended ? Colors.red : Colors.green,
          ),
          if (result.lpsRecommended) ...[
            const SizedBox(height: 8),
            _buildRequirementItem(
              context,
              'Recommended Protection Level: ${result.protectionLevel}',
              Icons.shield,
              AppColors.primaryBlue,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRequirementItem(BuildContext context, String text, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.darkGrey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildComplianceNotes(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.gavel, color: Colors.grey[600], size: 18),
              const SizedBox(width: 8),
              Text(
                'COMPLIANCE NOTES',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.grey[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildComplianceItem(context, 'All installations must comply with local electrical codes'),
          _buildComplianceItem(context, 'Regular inspection and maintenance required if LPS is installed'),
          _buildComplianceItem(context, 'Professional installation recommended'),
          _buildComplianceItem(context, 'Consider surge protection devices for sensitive equipment'),
        ],
      ),
    );
  }

  Widget _buildComplianceItem(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[700],
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
