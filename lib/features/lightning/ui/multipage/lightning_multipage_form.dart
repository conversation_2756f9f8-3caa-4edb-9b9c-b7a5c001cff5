import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/ui/multipage/pages/building_location_page.dart';
import 'package:pis_core/features/lightning/ui/multipage/pages/risk_assessment_page.dart';
import 'package:pis_core/features/lightning/ui/multipage/pages/results_report_page.dart';
import 'package:pis_core/features/lightning/ui/multipage/widgets/form_navigation_bar.dart';

enum LightningFormStep {
  buildingLocation(0, 'Building & Location', 'Configure building dimensions and location parameters'),
  riskAssessment(1, 'Risk Assessment', 'Set occupancy, contents, and consequence factors'),
  results(2, 'Results', 'View calculations and generate report');

  const LightningFormStep(this.stepIndex, this.title, this.description);
  final int stepIndex;
  final String title;
  final String description;

  static LightningFormStep fromIndex(int index) {
    return LightningFormStep.values.firstWhere((step) => step.stepIndex == index);
  }
}

class LightningMultipageForm extends HookWidget {
  const LightningMultipageForm({super.key, this.onExit});

  final VoidCallback? onExit;

  @override
  Widget build(BuildContext context) {
    final currentStepIndex = useState(0);
    final pageController = usePageController();

    final currentStep = LightningFormStep.fromIndex(currentStepIndex.value);
    final isLastStep = currentStepIndex.value == LightningFormStep.values.length - 1;
    final isFirstStep = currentStepIndex.value == 0;

    void goToStep(int stepIndex) {
      if (stepIndex >= 0 && stepIndex < LightningFormStep.values.length) {
        currentStepIndex.value = stepIndex;
        pageController.animateToPage(
          stepIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }

    void nextStep() {
      if (!isLastStep) {
        goToStep(currentStepIndex.value + 1);
      }
    }

    void previousStep() {
      if (!isFirstStep) {
        goToStep(currentStepIndex.value - 1);
      }
    }

    final pages = [
      const BuildingLocationPage(),
      const RiskAssessmentPage(),
      const ResultsReportPage(),
    ];

    return Scaffold(
      backgroundColor: AppColors.surface,
      body: Column(
        children: [
          // Compact Progress Indicator with Exit Button
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            decoration: BoxDecoration(
              color: AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Exit button
                IconButton(
                  onPressed: onExit,
                  icon: const Icon(Icons.arrow_back),
                  style: IconButton.styleFrom(
                    foregroundColor: AppColors.primaryBlue,
                  ),
                ),
                const SizedBox(width: 8),
                // PIS Logo
                Container(
                  width: 24,
                  height: 24,
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(2),
                    child: Image.asset(
                      'assets/logo/PIS logo.png',
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // Progress indicator
                Expanded(
                  child: Row(
                    children: LightningFormStep.values.map((step) {
                      final isActive = step.stepIndex == currentStepIndex.value;
                      final isCompleted = step.stepIndex < currentStepIndex.value;
                      
                      return Expanded(
                        child: GestureDetector(
                          onTap: () => goToStep(step.stepIndex),
                          child: Container(
                            height: 4,
                            margin: const EdgeInsets.symmetric(horizontal: 2),
                            decoration: BoxDecoration(
                              color: isCompleted || isActive 
                                  ? AppColors.primaryBlue 
                                  : AppColors.lightGrey,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
                const SizedBox(width: 16),
                // Step info
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      currentStep.title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                    Text(
                      '${currentStepIndex.value + 1} of ${LightningFormStep.values.length}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.darkGrey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Page Content with proper spacing
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 24), // Add space above navigation
              child: PageView(
                controller: pageController,
                onPageChanged: (index) {
                  currentStepIndex.value = index;
                },
                children: pages,
              ),
            ),
          ),

          // Navigation Bar
          LightningFormNavigationBar(
            isFirstStep: isFirstStep,
            isLastStep: isLastStep,
            onPrevious: previousStep,
            onNext: nextStep,
            currentStep: currentStep,
          ),
        ],
      ),
    );
  }
}
