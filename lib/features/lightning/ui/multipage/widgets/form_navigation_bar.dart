import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/ui/multipage/lightning_multipage_form.dart';
import 'package:pis_core/features/lightning/ui/report_preview/lightning_report_preview_page.dart';

class LightningFormNavigationBar extends StatelessWidget {
  const LightningFormNavigationBar({
    super.key,
    required this.isFirstStep,
    required this.isLastStep,
    required this.onPrevious,
    required this.onNext,
    required this.currentStep,
  });

  final bool isFirstStep;
  final bool isLastStep;
  final VoidCallback onPrevious;
  final VoidCallback onNext;
  final LightningFormStep currentStep;

  void _navigateToReportPreview(BuildContext context) {
    final lightningBloc = context.read<LightningBloc>();
    final state = lightningBloc.state;
    if (state is! LightningSuccessState) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No calculation results available'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (newContext) => BlocProvider.value(
          value: lightningBloc,
          child: const LightningReportPreviewPage(),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Previous button
          if (!isFirstStep)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: onPrevious,
                icon: const Icon(Icons.arrow_back),
                label: const Text('Previous'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: AppColors.primaryBlue),
                  foregroundColor: AppColors.primaryBlue,
                ),
              ),
            )
          else
            const Expanded(child: SizedBox()),

          const SizedBox(width: 16),

          // Step indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              currentStep.description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.darkGrey,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Next/Preview Report Button
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: isLastStep
                ? () => _navigateToReportPreview(context)
                : onNext,
              icon: Icon(isLastStep ? Icons.preview : Icons.arrow_forward),
              label: Text(isLastStep ? 'Preview Report' : 'Next Step'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: isLastStep ? AppColors.primaryRed : AppColors.primaryBlue,
                foregroundColor: AppColors.white,
                elevation: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
