import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';

class LightningFormProgressIndicator extends StatelessWidget {
  const LightningFormProgressIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.onStepTapped,
  });

  final int currentStep;
  final int totalSteps;
  final void Function(int)? onStepTapped;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(totalSteps, (index) {
        final isActive = index == currentStep;
        final isCompleted = index < currentStep;
        final isClickable = onStepTapped != null;

        return Expanded(
          child: Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: isClickable ? () => onStepTapped!(index) : null,
                  child: MouseRegion(
                    cursor: isClickable ? SystemMouseCursors.click : SystemMouseCursors.basic,
                    child: Container(
                      height: 40,
                      decoration: BoxDecoration(
                        color: _getStepColor(isActive, isCompleted),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: _getStepBorderColor(isActive, isCompleted),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: _getStepIconColor(isActive, isCompleted),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: isCompleted
                                  ? Icon(
                                      Icons.check,
                                      size: 12,
                                      color: AppColors.white,
                                    )
                                  : Text(
                                      '${index + 1}',
                                      style: TextStyle(
                                        color: isActive ? AppColors.white : AppColors.darkGrey,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              _getStepTitle(index),
                              style: TextStyle(
                                color: _getStepTextColor(isActive, isCompleted),
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              if (index < totalSteps - 1)
                Container(
                  width: 8,
                  height: 2,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    color: index < currentStep 
                        ? AppColors.primaryBlue 
                        : AppColors.outline,
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
            ],
          ),
        );
      }),
    );
  }

  String _getStepTitle(int index) {
    const titles = ['Building & Location', 'Risk Assessment', 'Results'];
    return index < titles.length ? titles[index] : 'Step ${index + 1}';
  }

  Color _getStepColor(bool isActive, bool isCompleted) {
    if (isCompleted) return AppColors.primaryBlue.withValues(alpha: 0.1);
    if (isActive) return AppColors.primaryBlue.withValues(alpha: 0.1);
    return AppColors.surface;
  }

  Color _getStepBorderColor(bool isActive, bool isCompleted) {
    if (isCompleted) return AppColors.primaryBlue;
    if (isActive) return AppColors.primaryBlue;
    return AppColors.outline;
  }

  Color _getStepIconColor(bool isActive, bool isCompleted) {
    if (isCompleted) return AppColors.primaryBlue;
    if (isActive) return AppColors.primaryBlue;
    return AppColors.mediumGrey;
  }

  Color _getStepTextColor(bool isActive, bool isCompleted) {
    if (isCompleted) return AppColors.primaryBlue;
    if (isActive) return AppColors.primaryBlue;
    return AppColors.darkGrey;
  }
}
