import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';

class LocationConstructionPage extends HookWidget {
  const LocationConstructionPage({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<LightningBloc>().state;
    final input = switch (state) {
      LightningSuccessState state => state.input,
      _ => const LightningCalculationInput(),
    };

    // Controllers for location
    final locationCtrl = useTextEditingController(text: input.location);
    final flashDensityCtrl = useTextEditingController(text: input.flashDensityNg.toString());

    // State for dropdowns
    final selectedStructureMaterial = useState(input.structureMaterial);
    final selectedRoofMaterial = useState(input.roofMaterial);
    final selectedLocationFactor = useState(input.locationFactor);

    void pushBlocUpdate() {
      final bloc = context.read<LightningBloc>();
      final updatedInput = input.copyWith(
        location: locationCtrl.text,
        flashDensityNg: double.tryParse(flashDensityCtrl.text) ?? input.flashDensityNg,
        structureMaterial: selectedStructureMaterial.value,
        roofMaterial: selectedRoofMaterial.value,
        locationFactor: selectedLocationFactor.value,
      );
      bloc.add(LightningEvent.calculate(updatedInput));
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final isWide = constraints.maxWidth > 800;

        if (isWide) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Row(
              children: [
                // Left Panel - Location Information
                Expanded(
                  flex: 4,
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryGreen.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.location_on,
                                  color: AppColors.primaryGreen,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Geographic Location',
                                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        color: AppColors.primaryGreen,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                    Text(
                                      'Location coordinates and environmental factors',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: AppColors.darkGrey,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 32),

                          // Location fields
                          _buildLocationFields(
                            context: context,
                            locationCtrl: locationCtrl,
                            flashDensityCtrl: flashDensityCtrl,
                            selectedLocationFactor: selectedLocationFactor,
                            pushBlocUpdate: pushBlocUpdate,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Right Panel - Construction Details
                Expanded(
                  flex: 6,
                  child: Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryRed.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.construction,
                                  color: AppColors.primaryRed,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Construction Details',
                                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        color: AppColors.primaryRed,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                    Text(
                                      'Building structure and material specifications',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: AppColors.darkGrey,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 32),

                          // Construction fields
                          _buildConstructionFields(
                            context: context,
                            selectedStructureMaterial: selectedStructureMaterial,
                            selectedRoofMaterial: selectedRoofMaterial,
                            pushBlocUpdate: pushBlocUpdate,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          // Mobile layout - stack vertically
          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Column(
              children: [
                // Location Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Geographic Location',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: AppColors.primaryGreen,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildLocationFields(
                        context: context,
                        locationCtrl: locationCtrl,
                        flashDensityCtrl: flashDensityCtrl,
                        selectedLocationFactor: selectedLocationFactor,
                        pushBlocUpdate: pushBlocUpdate,
                      ),
                    ],
                  ),
                ),

                // Construction Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Construction Details',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: AppColors.primaryRed,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildConstructionFields(
                        context: context,
                        selectedStructureMaterial: selectedStructureMaterial,
                        selectedRoofMaterial: selectedRoofMaterial,
                        pushBlocUpdate: pushBlocUpdate,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildLocationFields({
    required BuildContext context,
    required TextEditingController locationCtrl,
    required TextEditingController flashDensityCtrl,
    required ValueNotifier<LocationFactorCoeff> selectedLocationFactor,
    required VoidCallback pushBlocUpdate,
  }) {
    return Column(
      children: [
        // Location
        _buildTextField(
          context: context,
          label: 'Location',
          controller: locationCtrl,
          onChanged: pushBlocUpdate,
        ),
        const SizedBox(height: 16),

        // Flash Density
        _buildNumberField(
          context: context,
          label: 'Flash Density (fl/km²/year)',
          controller: flashDensityCtrl,
          onChanged: pushBlocUpdate,
        ),
        const SizedBox(height: 16),

        // Location Factor
        _buildDropdownField<LocationFactorCoeff>(
          context: context,
          label: 'Location Factor',
          value: selectedLocationFactor.value,
          items: LocationFactorCoeff.values,
          onChanged: (value) {
            selectedLocationFactor.value = value;
            pushBlocUpdate();
          },
          getDisplayText: (factor) => factor.description,
        ),
      ],
    );
  }

  Widget _buildConstructionFields({
    required BuildContext context,
    required ValueNotifier<StructureMaterial> selectedStructureMaterial,
    required ValueNotifier<RoofMaterial> selectedRoofMaterial,
    required VoidCallback pushBlocUpdate,
  }) {
    return Column(
      children: [
        // Structure Material
        _buildDropdownField<StructureMaterial>(
          context: context,
          label: 'Structure Material',
          value: selectedStructureMaterial.value,
          items: StructureMaterial.values,
          onChanged: (value) {
            selectedStructureMaterial.value = value;
            pushBlocUpdate();
          },
          getDisplayText: (material) => material.label,
        ),
        const SizedBox(height: 16),

        // Roof Material
        _buildDropdownField<RoofMaterial>(
          context: context,
          label: 'Roof Material',
          value: selectedRoofMaterial.value,
          items: RoofMaterial.values,
          onChanged: (value) {
            selectedRoofMaterial.value = value;
            pushBlocUpdate();
          },
          getDisplayText: (material) => material.label,
        ),
      ],
    );
  }

  Widget _buildTextField({
    required BuildContext context,
    required String label,
    required TextEditingController controller,
    required VoidCallback onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          onChanged: (_) => onChanged(),
          decoration: InputDecoration(
            hintText: 'Enter value',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.mediumGrey,
                width: 1.5,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.mediumGrey,
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.primaryBlue,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: AppColors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildNumberField({
    required BuildContext context,
    required String label,
    required TextEditingController controller,
    required VoidCallback onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: TextInputType.number,
          onChanged: (_) => onChanged(),
          decoration: InputDecoration(
            hintText: 'Enter value',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.mediumGrey,
                width: 1.5,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.mediumGrey,
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.primaryBlue,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: AppColors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField<T>({
    required BuildContext context,
    required String label,
    required T value,
    required List<T> items,
    required ValueChanged<T> onChanged,
    required String Function(T) getDisplayText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          onChanged: (newValue) {
            if (newValue != null) {
              onChanged(newValue);
            }
          },
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.mediumGrey,
                width: 1.5,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.mediumGrey,
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.primaryBlue,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: AppColors.white,
          ),
          items: items.map((item) {
            return DropdownMenuItem<T>(
              value: item,
              child: Text(getDisplayText(item)),
            );
          }).toList(),
        ),
      ],
    );
  }
}
