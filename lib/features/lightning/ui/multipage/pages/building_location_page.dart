import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';

class BuildingLocationPage extends HookWidget {
  const BuildingLocationPage({super.key});

  @override
  Widget build(BuildContext context) {
    final input = context.watch<LightningBloc>().state is LightningSuccessState
        ? (context.watch<LightningBloc>().state as LightningSuccessState).input
        : const LightningCalculationInput();

    final projectNameCtrl = useTextEditingController(text: input.projectName);
    final contractorCtrl = useTextEditingController(text: input.mainContractor);
    final lengthCtrl = useTextEditingController(text: input.lengthM.toString());
    final widthCtrl = useTextEditingController(text: input.widthM.toString());
    final heightCtrl = useTextEditingController(text: input.heightM.toString());
    final flashDensityCtrl = useTextEditingController(text: input.flashDensityNg.toString());
    final selectedLocation = useState(input.location);
    final selectedLocationFactor = useState(input.locationFactor);
    final selectedStructureMaterial = useState(input.structureMaterial);
    final selectedRoofMaterial = useState(input.roofMaterial);

    void pushBlocUpdate() {
      final bloc = context.read<LightningBloc>();
      final updatedInput = input.copyWith(
        projectName: projectNameCtrl.text,
        mainContractor: contractorCtrl.text,
        lengthM: double.tryParse(lengthCtrl.text) ?? input.lengthM,
        widthM: double.tryParse(widthCtrl.text) ?? input.widthM,
        heightM: double.tryParse(heightCtrl.text) ?? input.heightM,
        location: selectedLocation.value,
        flashDensityNg: double.tryParse(flashDensityCtrl.text) ?? input.flashDensityNg,
        locationFactor: selectedLocationFactor.value,
        structureMaterial: selectedStructureMaterial.value,
        roofMaterial: selectedRoofMaterial.value,
      );
      bloc.add(LightningEvent.calculate(updatedInput));
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final isWide = constraints.maxWidth > 800;

        if (isWide) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Row(
              children: [
                // Left Panel - Building Dimensions
                Expanded(
                  flex: 4,
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: _buildBuildingDimensionsSection(
                        context, lengthCtrl, widthCtrl, heightCtrl, pushBlocUpdate,
                      ),
                    ),
                  ),
                ),

                // Right Panel - Location & Project
                Expanded(
                  flex: 6,
                  child: Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _buildProjectInfoSection(
                            context, projectNameCtrl, contractorCtrl, pushBlocUpdate,
                          ),
                          const SizedBox(height: 32),
                          _buildLocationSection(
                            context, selectedLocation, flashDensityCtrl, selectedLocationFactor, 
                            selectedStructureMaterial, selectedRoofMaterial, pushBlocUpdate,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          // Mobile layout
          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Column(
              children: [
                _buildBuildingDimensionsSection(
                  context, lengthCtrl, widthCtrl, heightCtrl, pushBlocUpdate,
                ),
                const SizedBox(height: 16),
                _buildProjectInfoSection(
                  context, projectNameCtrl, contractorCtrl, pushBlocUpdate,
                ),
                const SizedBox(height: 16),
                _buildLocationSection(
                  context, selectedLocation, flashDensityCtrl, selectedLocationFactor,
                  selectedStructureMaterial, selectedRoofMaterial, pushBlocUpdate,
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildBuildingDimensionsSection(
    BuildContext context,
    TextEditingController lengthCtrl,
    TextEditingController widthCtrl,
    TextEditingController heightCtrl,
    VoidCallback pushBlocUpdate,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.primaryRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.architecture,
                color: AppColors.primaryRed,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Building Dimensions',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                  Text(
                    'Physical dimensions for collective area calculation',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.darkGrey,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 32),

        // Length input
        _buildDimensionInput(
          context: context,
          label: 'Building Length (L)',
          controller: lengthCtrl,
          unit: 'm',
          color: AppColors.primaryRed,
          onChanged: pushBlocUpdate,
        ),

        const SizedBox(height: 24),

        // Width input
        _buildDimensionInput(
          context: context,
          label: 'Building Width (W)',
          controller: widthCtrl,
          unit: 'm',
          color: AppColors.primaryBlue,
          onChanged: pushBlocUpdate,
        ),

        const SizedBox(height: 24),

        // Height input
        _buildDimensionInput(
          context: context,
          label: 'Building Height (H)',
          controller: heightCtrl,
          unit: 'm',
          color: AppColors.primaryGreen,
          onChanged: pushBlocUpdate,
        ),

        const SizedBox(height: 24),

        // Formula info
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.info_outline, color: AppColors.primaryBlue, size: 20),
                  const SizedBox(width: 12),
                  Text(
                    'Collective Area Formula',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Ac = (L + 6H)(W + 6H) × 10⁻⁶ km²',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontFamily: 'monospace',
                  color: AppColors.primaryBlue,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDimensionInput({
    required BuildContext context,
    required String label,
    required TextEditingController controller,
    required String unit,
    required Color color,
    required VoidCallback onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            overflow: TextOverflow.visible,
            softWrap: true,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: controller,
            onChanged: (_) => onChanged(),
            keyboardType: TextInputType.number,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            decoration: InputDecoration(
              hintText: '0.0',
              suffixText: unit,
              suffixStyle: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: AppColors.darkGrey,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.mediumGrey, width: 1.5),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.mediumGrey, width: 1.5),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: color, width: 2),
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectInfoSection(
    BuildContext context,
    TextEditingController projectNameCtrl,
    TextEditingController contractorCtrl,
    VoidCallback pushBlocUpdate,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.business,
                color: AppColors.primaryBlue,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Project Information',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                  Text(
                    'Basic project details and identification',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.darkGrey,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 32),

        // Project Name
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.outline),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Project Name',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: projectNameCtrl,
                onChanged: (_) => pushBlocUpdate(),
                decoration: InputDecoration(
                  hintText: 'Enter project name',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.mediumGrey, width: 1.5),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.mediumGrey, width: 1.5),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.primaryBlue, width: 2),
                  ),
                  contentPadding: const EdgeInsets.all(16),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Main Contractor
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.outline),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Main Contractor',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: contractorCtrl,
                onChanged: (_) => pushBlocUpdate(),
                decoration: InputDecoration(
                  hintText: 'Enter contractor name',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.mediumGrey, width: 1.5),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.mediumGrey, width: 1.5),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.primaryBlue, width: 2),
                  ),
                  contentPadding: const EdgeInsets.all(16),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLocationSection(
    BuildContext context,
    ValueNotifier<String> selectedLocation,
    TextEditingController flashDensityCtrl,
    ValueNotifier<LocationFactorCoeff> selectedLocationFactor,
    ValueNotifier<StructureMaterial> selectedStructureMaterial,
    ValueNotifier<RoofMaterial> selectedRoofMaterial,
    VoidCallback pushBlocUpdate,
  ) {
    // Location flash density mapping
    final locationFlashDensity = {
      'Egypt': 1.0,
      'Saudi Arabia': 0.5,
      'UAE': 0.3,
      'Kuwait': 0.4,
      'Qatar': 0.2,
      'Bahrain': 0.3,
      'Oman': 0.8,
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.location_on,
                color: AppColors.primaryGreen,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Location & Environment',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                  Text(
                    'Geographic location and environmental factors',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.darkGrey,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 32),

        // Location Selection
        _buildSelectionCard(
          context: context,
          title: 'Geographic Location',
          description: 'Select your location to automatically set flash density',
          icon: Icons.public,
          color: AppColors.primaryGreen,
          child: Column(
            children: [
              DropdownButtonFormField<String>(
                value: selectedLocation.value,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.mediumGrey, width: 1.5),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.mediumGrey, width: 1.5),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.primaryGreen, width: 2),
                  ),
                  contentPadding: const EdgeInsets.all(16),
                ),
                items: locationFlashDensity.keys.map((location) {
                  return DropdownMenuItem(
                    value: location,
                    child: Text(
                      '$location (${locationFlashDensity[location]} fl/km²/year)',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    selectedLocation.value = value;
                    flashDensityCtrl.text = locationFlashDensity[value]!.toString();
                    pushBlocUpdate();
                  }
                },
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.primaryGreen.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.primaryGreen.withValues(alpha: 0.2)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.flash_on, color: AppColors.primaryGreen, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Flash Density: ${flashDensityCtrl.text} fl/km²/year',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.primaryGreen,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Location Factor
        _buildSelectionCard(
          context: context,
          title: 'Location Factor (C1)',
          description: 'Coefficient based on structure surroundings',
          icon: Icons.landscape,
          color: AppColors.primaryBlue,
          child: Column(
            children: LocationFactorCoeff.values.map((factor) {
              final isSelected = factor == selectedLocationFactor.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: GestureDetector(
                  onTap: () {
                    selectedLocationFactor.value = factor;
                    pushBlocUpdate();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.primaryBlue.withValues(alpha: 0.1)
                          : AppColors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? AppColors.primaryBlue : AppColors.outline,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: isSelected ? AppColors.primaryBlue : AppColors.surface,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected ? AppColors.primaryBlue : AppColors.mediumGrey,
                            ),
                          ),
                          child: isSelected
                              ? Icon(Icons.check, size: 16, color: AppColors.white)
                              : null,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${factor.value} - ${factor.description}',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected ? AppColors.primaryBlue : AppColors.darkGrey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectionCard({
    required BuildContext context,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 18),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.darkGrey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          child,
        ],
      ),
    );
  }
}
