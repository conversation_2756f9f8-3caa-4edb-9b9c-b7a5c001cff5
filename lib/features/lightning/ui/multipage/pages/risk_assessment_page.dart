import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';

class RiskAssessmentPage extends HookWidget {
  const RiskAssessmentPage({super.key});

  @override
  Widget build(BuildContext context) {
    final input = context.watch<LightningBloc>().state is LightningSuccessState
        ? (context.watch<LightningBloc>().state as LightningSuccessState).input
        : const LightningCalculationInput();

    final selectedOccupancyCoeff = useState(input.occupancyCoeff);
    final selectedContentsCoeff = useState(input.contentsCoeff);
    final selectedConsequenceCoeff = useState(input.consequenceCoeff);

    void pushBlocUpdate() {
      final bloc = context.read<LightningBloc>();
      final updatedInput = input.copyWith(
        occupancyCoeff: selectedOccupancyCoeff.value,
        contentsCoeff: selectedContentsCoeff.value,
        consequenceCoeff: selectedConsequenceCoeff.value,
      );
      bloc.add(LightningEvent.calculate(updatedInput));
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final isWide = constraints.maxWidth > 800;

        if (isWide) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Row(
              children: [
                // Left Panel - Risk Factors
                Expanded(
                  flex: 6,
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _buildOccupancySection(
                            context, selectedOccupancyCoeff, pushBlocUpdate,
                          ),
                          const SizedBox(height: 32),
                          _buildContentsSection(
                            context, selectedContentsCoeff, pushBlocUpdate,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Right Panel - Consequence & Guidelines
                Expanded(
                  flex: 4,
                  child: Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _buildConsequenceSection(
                            context, selectedConsequenceCoeff, pushBlocUpdate,
                          ),
                          const SizedBox(height: 32),
                          _buildGuidelinesSection(context),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          // Mobile layout
          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Column(
              children: [
                _buildOccupancySection(
                  context, selectedOccupancyCoeff, pushBlocUpdate,
                ),
                const SizedBox(height: 16),
                _buildContentsSection(
                  context, selectedContentsCoeff, pushBlocUpdate,
                ),
                const SizedBox(height: 16),
                _buildConsequenceSection(
                  context, selectedConsequenceCoeff, pushBlocUpdate,
                ),
                const SizedBox(height: 16),
                _buildGuidelinesSection(context),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildOccupancySection(
    BuildContext context,
    ValueNotifier<StructureOccupancyCoeff> selectedOccupancyCoeff,
    VoidCallback pushBlocUpdate,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.primaryRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.people,
                color: AppColors.primaryRed,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Occupancy Classification',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                  Text(
                    'Building occupancy type and evacuation difficulty',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.darkGrey,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 32),

        // Occupancy options
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.outline),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.primaryRed.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.people, color: AppColors.primaryRed, size: 18),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Occupancy Factor',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              ...StructureOccupancyCoeff.values.map((occupancy) {
                final isSelected = occupancy == selectedOccupancyCoeff.value;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: GestureDetector(
                    onTap: () {
                      selectedOccupancyCoeff.value = occupancy;
                      pushBlocUpdate();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? AppColors.primaryRed.withValues(alpha: 0.1) 
                            : AppColors.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected ? AppColors.primaryRed : AppColors.outline,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 28,
                            height: 28,
                            decoration: BoxDecoration(
                              color: isSelected ? AppColors.primaryRed : AppColors.surface,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isSelected ? AppColors.primaryRed : AppColors.mediumGrey,
                                width: 2,
                              ),
                            ),
                            child: isSelected
                                ? Icon(Icons.check, size: 18, color: AppColors.white)
                                : null,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryRed.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Text(
                                        occupancy.value.toString(),
                                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.primaryRed,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        occupancy.description,
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: isSelected ? AppColors.primaryRed : AppColors.darkGrey,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _getOccupancyDetails(occupancy),
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.darkGrey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  String _getOccupancyDetails(StructureOccupancyCoeff occupancy) {
    switch (occupancy) {
      case StructureOccupancyCoeff.unoccupied:
        return 'Buildings with no regular occupancy or minimal human presence';
      case StructureOccupancyCoeff.normallyOccupied:
        return 'Standard office buildings, residential, and commercial structures';
      case StructureOccupancyCoeff.difficultEvacuation:
        return 'Hospitals, schools, high-rise buildings, or structures with mobility-impaired occupants';
    }
  }

  Widget _buildContentsSection(
    BuildContext context,
    ValueNotifier<StructureContentsCoeff> selectedContentsCoeff,
    VoidCallback pushBlocUpdate,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.inventory,
                color: AppColors.primaryBlue,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Contents Classification',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                  Text(
                    'Value and importance of building contents',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.darkGrey,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 32),

        // Contents options
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.outline),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.inventory, color: AppColors.primaryBlue, size: 18),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Contents Factor',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              ...StructureContentsCoeff.values.map((contents) {
                final isSelected = contents == selectedContentsCoeff.value;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: GestureDetector(
                    onTap: () {
                      selectedContentsCoeff.value = contents;
                      pushBlocUpdate();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppColors.primaryBlue.withValues(alpha: 0.1)
                            : AppColors.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected ? AppColors.primaryBlue : AppColors.outline,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 28,
                            height: 28,
                            decoration: BoxDecoration(
                              color: isSelected ? AppColors.primaryBlue : AppColors.surface,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isSelected ? AppColors.primaryBlue : AppColors.mediumGrey,
                                width: 2,
                              ),
                            ),
                            child: isSelected
                                ? Icon(Icons.check, size: 18, color: AppColors.white)
                                : null,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryBlue.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Text(
                                        contents.value.toString(),
                                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.primaryBlue,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        contents.description,
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: isSelected ? AppColors.primaryBlue : AppColors.darkGrey,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _getContentsDetails(contents),
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.darkGrey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  String _getContentsDetails(StructureContentsCoeff contents) {
    switch (contents) {
      case StructureContentsCoeff.lowValue:
        return 'Basic furnishings, standard office equipment, replaceable items';
      case StructureContentsCoeff.standardValue:
        return 'Standard commercial contents, typical industrial equipment';
      case StructureContentsCoeff.highValue:
        return 'Expensive equipment, valuable inventory, specialized machinery';
      case StructureContentsCoeff.exceptionalValue:
        return 'Critical systems, rare artifacts, extremely valuable contents';
      case StructureContentsCoeff.irreplaceableValue:
        return 'Historical artifacts, unique research data, irreplaceable items';
    }
  }

  Widget _buildConsequenceSection(
    BuildContext context,
    ValueNotifier<LightningConsequenceCoeff> selectedConsequenceCoeff,
    VoidCallback pushBlocUpdate,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.warning,
                color: AppColors.primaryGreen,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Consequence Factor',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                  Text(
                    'Impact of lightning damage on operations',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.darkGrey,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 32),

        // Consequence options
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.outline),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.primaryGreen.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.warning, color: AppColors.primaryGreen, size: 18),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Consequence Factor',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              ...LightningConsequenceCoeff.values.map((consequence) {
                final isSelected = consequence == selectedConsequenceCoeff.value;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: GestureDetector(
                    onTap: () {
                      selectedConsequenceCoeff.value = consequence;
                      pushBlocUpdate();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppColors.primaryGreen.withValues(alpha: 0.1)
                            : AppColors.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected ? AppColors.primaryGreen : AppColors.outline,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 28,
                            height: 28,
                            decoration: BoxDecoration(
                              color: isSelected ? AppColors.primaryGreen : AppColors.surface,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isSelected ? AppColors.primaryGreen : AppColors.mediumGrey,
                                width: 2,
                              ),
                            ),
                            child: isSelected
                                ? Icon(Icons.check, size: 18, color: AppColors.white)
                                : null,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryGreen.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Text(
                                        consequence.value.toString(),
                                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.primaryGreen,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        consequence.description,
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: isSelected ? AppColors.primaryGreen : AppColors.darkGrey,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _getConsequenceDetails(consequence),
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.darkGrey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  String _getConsequenceDetails(LightningConsequenceCoeff consequence) {
    switch (consequence) {
      case LightningConsequenceCoeff.continuityNotRequired:
        return 'Service interruption acceptable, no critical operations';
      case LightningConsequenceCoeff.continuityRequired:
        return 'Service continuity important, some operational impact';
      case LightningConsequenceCoeff.consequenceToEnvironment:
        return 'Environmental consequences, potential ecological damage';
    }
  }

  Widget _buildGuidelinesSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.info,
                  color: AppColors.primaryBlue,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'NFPA 780 Guidelines',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: AppColors.primaryBlue,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.visible,
                      softWrap: true,
                    ),
                    Text(
                      'Risk assessment criteria and recommendations',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.darkGrey,
                      ),
                      overflow: TextOverflow.visible,
                      softWrap: true,
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Risk Assessment Formula',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryBlue,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'R = Ng × Ac × C1 × C2 × C3 × C4 × C5 × 10⁻⁶',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'monospace',
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Where:\n'
                  '• R = Annual frequency of lightning strikes\n'
                  '• Ng = Ground flash density (fl/km²/year)\n'
                  '• Ac = Collective area (km²)\n'
                  '• C1 = Location factor\n'
                  '• C2 = Construction factor\n'
                  '• C3 = Contents factor\n'
                  '• C4 = Occupancy factor\n'
                  '• C5 = Consequence factor',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.darkGrey,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.primaryGreen.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.primaryGreen.withValues(alpha: 0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.check_circle, color: AppColors.primaryGreen, size: 20),
                    const SizedBox(width: 12),
                    Text(
                      'Protection Criteria',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryGreen,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  'Lightning protection is recommended when:\n'
                  '• R > 10⁻⁵ (1 strike per 100,000 years)\n'
                  '• Critical facilities or high-value contents\n'
                  '• Public safety considerations\n'
                  '• Environmental impact concerns',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.darkGrey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
