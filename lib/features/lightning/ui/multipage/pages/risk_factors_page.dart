import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';

class RiskFactorsPage extends HookWidget {
  const RiskFactorsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<LightningBloc>().state;
    final input = switch (state) {
      LightningSuccessState state => state.input,
      _ => const LightningCalculationInput(),
    };

    // State for risk factors
    final selectedOccupancyCoeff = useState(input.occupancyCoeff);
    final selectedContentsCoeff = useState(input.contentsCoeff);
    final selectedConsequenceCoeff = useState(input.consequenceCoeff);

    void pushBlocUpdate() {
      final bloc = context.read<LightningBloc>();
      final updatedInput = input.copyWith(
        occupancyCoeff: selectedOccupancyCoeff.value,
        contentsCoeff: selectedContentsCoeff.value,
        consequenceCoeff: selectedConsequenceCoeff.value,
      );
      bloc.add(LightningEvent.calculate(updatedInput));
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final isWide = constraints.maxWidth > 800;

        if (isWide) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Row(
              children: [
                // Left Panel - Occupancy & Contents
                Expanded(
                  flex: 4,
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryRed.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.people,
                                  color: AppColors.primaryRed,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Occupancy & Contents',
                                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        color: AppColors.primaryRed,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                    Text(
                                      'Building usage and content risk assessment',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: AppColors.darkGrey,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 32),

                          // Occupancy & Contents fields
                          _buildOccupancyFields(
                            context: context,
                            selectedOccupancyCoeff: selectedOccupancyCoeff,
                            selectedContentsCoeff: selectedContentsCoeff,
                            pushBlocUpdate: pushBlocUpdate,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Right Panel - Service & Environment
                Expanded(
                  flex: 6,
                  child: Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.settings,
                                  color: AppColors.primaryBlue,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Service & Environment',
                                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        color: AppColors.primaryBlue,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                    Text(
                                      'Service continuity and environmental factors',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: AppColors.darkGrey,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 32),

                          // Consequence fields
                          _buildConsequenceFields(
                            context: context,
                            selectedConsequenceCoeff: selectedConsequenceCoeff,
                            pushBlocUpdate: pushBlocUpdate,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          // Mobile layout - stack vertically
          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Column(
              children: [
                // Occupancy & Contents Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Occupancy & Contents',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: AppColors.primaryRed,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildOccupancyFields(
                        context: context,
                        selectedOccupancyCoeff: selectedOccupancyCoeff,
                        selectedContentsCoeff: selectedContentsCoeff,
                        pushBlocUpdate: pushBlocUpdate,
                      ),
                    ],
                  ),
                ),

                // Service & Environment Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Service & Environment',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildConsequenceFields(
                        context: context,
                        selectedConsequenceCoeff: selectedConsequenceCoeff,
                        pushBlocUpdate: pushBlocUpdate,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildOccupancyFields({
    required BuildContext context,
    required ValueNotifier<StructureOccupancyCoeff> selectedOccupancyCoeff,
    required ValueNotifier<StructureContentsCoeff> selectedContentsCoeff,
    required VoidCallback pushBlocUpdate,
  }) {
    return Column(
      children: [
        // Occupancy Coefficient
        _buildDropdownField<StructureOccupancyCoeff>(
          context: context,
          label: 'Occupancy Type',
          value: selectedOccupancyCoeff.value,
          items: StructureOccupancyCoeff.values,
          onChanged: (value) {
            selectedOccupancyCoeff.value = value;
            pushBlocUpdate();
          },
          getDisplayText: (coeff) => coeff.description,
        ),
        const SizedBox(height: 16),

        // Contents Coefficient
        _buildDropdownField<StructureContentsCoeff>(
          context: context,
          label: 'Contents Type',
          value: selectedContentsCoeff.value,
          items: StructureContentsCoeff.values,
          onChanged: (value) {
            selectedContentsCoeff.value = value;
            pushBlocUpdate();
          },
          getDisplayText: (coeff) => coeff.description,
        ),
      ],
    );
  }

  Widget _buildConsequenceFields({
    required BuildContext context,
    required ValueNotifier<LightningConsequenceCoeff> selectedConsequenceCoeff,
    required VoidCallback pushBlocUpdate,
  }) {
    return Column(
      children: [
        // Consequence Coefficient
        _buildDropdownField<LightningConsequenceCoeff>(
          context: context,
          label: 'Lightning Consequence',
          value: selectedConsequenceCoeff.value,
          items: LightningConsequenceCoeff.values,
          onChanged: (value) {
            selectedConsequenceCoeff.value = value;
            pushBlocUpdate();
          },
          getDisplayText: (coeff) => coeff.description,
        ),

        const SizedBox(height: 24),

        // Risk Assessment Summary
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.primaryBlue.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.primaryBlue,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Risk Assessment Guidelines',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Higher risk factors increase lightning protection requirements. Consider:\n'
                '• Critical facilities need enhanced protection\n'
                '• Hazardous contents require special measures\n'
                '• Isolated locations have higher strike probability\n'
                '• Service continuity affects protection level',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.darkGrey,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField<T>({
    required BuildContext context,
    required String label,
    required T value,
    required List<T> items,
    required ValueChanged<T> onChanged,
    required String Function(T) getDisplayText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          onChanged: (newValue) {
            if (newValue != null) {
              onChanged(newValue);
            }
          },
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.mediumGrey,
                width: 1.5,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.mediumGrey,
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.primaryBlue,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: AppColors.white,
          ),
          items: items.map((item) {
            return DropdownMenuItem<T>(
              value: item,
              child: Text(getDisplayText(item)),
            );
          }).toList(),
        ),
      ],
    );
  }
}
