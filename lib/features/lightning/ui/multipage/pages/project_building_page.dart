import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';

class ProjectBuildingPage extends HookWidget {
  const ProjectBuildingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<LightningBloc>().state;
    final input = switch (state) {
      LightningSuccessState state => state.input,
      _ => const LightningCalculationInput(),
    };

    // Controllers
    final projectNameCtrl = useTextEditingController(text: input.projectName);
    final contractorCtrl = useTextEditingController(text: input.mainContractor);
    final buildingLengthCtrl = useTextEditingController(text: input.lengthM.toString());
    final buildingWidthCtrl = useTextEditingController(text: input.widthM.toString());
    final buildingHeightCtrl = useTextEditingController(text: input.heightM.toString());

    // Focus nodes
    final projectNameFocus = useFocusNode();
    final contractorFocus = useFocusNode();

    void pushBlocUpdate() {
      final bloc = context.read<LightningBloc>();
      final updatedInput = input.copyWith(
        projectName: projectNameCtrl.text,
        mainContractor: contractorCtrl.text,
        lengthM: double.tryParse(buildingLengthCtrl.text) ?? input.lengthM,
        widthM: double.tryParse(buildingWidthCtrl.text) ?? input.widthM,
        heightM: double.tryParse(buildingHeightCtrl.text) ?? input.heightM,
      );
      bloc.add(LightningEvent.calculate(updatedInput));
    }

    // Keep controllers in sync when state changes
    useEffect(() {
      if (!projectNameFocus.hasFocus) {
        projectNameCtrl.text = input.projectName;
      }
      if (!contractorFocus.hasFocus) {
        contractorCtrl.text = input.mainContractor;
      }
      return null;
    }, [input.projectName, input.mainContractor]);

    return LayoutBuilder(
      builder: (context, constraints) {
        final isWide = constraints.maxWidth > 800;

        if (isWide) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Row(
              children: [
                // Left Panel - Project Information
                Expanded(
                  flex: 4,
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.business,
                                  color: AppColors.primaryBlue,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Project Information',
                                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        color: AppColors.primaryBlue,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                    Text(
                                      'Basic project details and identification',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: AppColors.darkGrey,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 32),

                          // Project Name
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Project Name',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextFormField(
                                controller: projectNameCtrl,
                                focusNode: projectNameFocus,
                                onChanged: (_) => pushBlocUpdate(),
                                decoration: InputDecoration(
                                  hintText: 'Enter project name',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: AppColors.mediumGrey,
                                      width: 1.5,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: AppColors.mediumGrey,
                                      width: 1.5,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: AppColors.primaryBlue,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: AppColors.white,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 24),

                          // Main Contractor
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Main Contractor',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextFormField(
                                controller: contractorCtrl,
                                focusNode: contractorFocus,
                                onChanged: (_) => pushBlocUpdate(),
                                decoration: InputDecoration(
                                  hintText: 'Enter contractor name',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: AppColors.mediumGrey,
                                      width: 1.5,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: AppColors.mediumGrey,
                                      width: 1.5,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: AppColors.primaryBlue,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: AppColors.white,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Right Panel - Building Dimensions
                Expanded(
                  flex: 6,
                  child: Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.outline),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryRed.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.architecture,
                                  color: AppColors.primaryRed,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Building Dimensions',
                                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        color: AppColors.primaryRed,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                    Text(
                                      'Physical dimensions of the building structure',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: AppColors.darkGrey,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 32),

                          // Building dimensions form will be added here
                          _buildDimensionFields(
                            context: context,
                            lengthCtrl: buildingLengthCtrl,
                            widthCtrl: buildingWidthCtrl,
                            heightCtrl: buildingHeightCtrl,
                            pushBlocUpdate: pushBlocUpdate,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          // Mobile layout - stack vertically
          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Column(
              children: [
                // Project Information Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Project form content for mobile
                      Text(
                        'Project Information',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Add project fields here
                    ],
                  ),
                ),

                // Building Dimensions Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.outline),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Building Dimensions',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: AppColors.primaryRed,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildDimensionFields(
                        context: context,
                        lengthCtrl: buildingLengthCtrl,
                        widthCtrl: buildingWidthCtrl,
                        heightCtrl: buildingHeightCtrl,
                        pushBlocUpdate: pushBlocUpdate,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildDimensionFields({
    required BuildContext context,
    required TextEditingController lengthCtrl,
    required TextEditingController widthCtrl,
    required TextEditingController heightCtrl,
    required VoidCallback pushBlocUpdate,
  }) {
    return Column(
      children: [
        // Building Length
        _buildDimensionField(
          context: context,
          label: 'Building Length (m)',
          controller: lengthCtrl,
          onChanged: pushBlocUpdate,
        ),
        const SizedBox(height: 16),

        // Building Width
        _buildDimensionField(
          context: context,
          label: 'Building Width (m)',
          controller: widthCtrl,
          onChanged: pushBlocUpdate,
        ),
        const SizedBox(height: 16),

        // Building Height
        _buildDimensionField(
          context: context,
          label: 'Building Height (m)',
          controller: heightCtrl,
          onChanged: pushBlocUpdate,
        ),
      ],
    );
  }

  Widget _buildDimensionField({
    required BuildContext context,
    required String label,
    required TextEditingController controller,
    required VoidCallback onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: TextInputType.number,
          onChanged: (_) => onChanged(),
          decoration: InputDecoration(
            hintText: 'Enter value',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.mediumGrey,
                width: 1.5,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.mediumGrey,
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.primaryBlue,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: AppColors.white,
          ),
        ),
      ],
    );
  }
}
