import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/widgets/section_card.dart';
import 'package:pis_core/core/widgets/field_tile.dart';
import 'package:pis_core/core/widgets/centered_dropdown_form_field.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';

class ConstructionSection extends HookWidget {
  const ConstructionSection({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<LightningBloc>().state;
    final input = switch (state) {
      LightningSuccessState state => state.input,
      _ => null,
    };

    void pushUpdate({
      StructureMaterial? structureMaterial,
      RoofMaterial? roofMaterial,
    }) {
      final bloc = context.read<LightningBloc>();
      final currentInput = input ?? const LightningCalculationInput();
      final updatedInput = currentInput.copyWith(
        structureMaterial: structureMaterial ?? input?.structureMaterial ?? StructureMaterial.metal,
        roofMaterial: roofMaterial ?? input?.roofMaterial ?? RoofMaterial.nonmetallic,
      );
      bloc.add(LightningEvent.calculate(updatedInput));
    }

    // Get the current C2 value for display
    final currentC2 = NFPA780Constants.constructionCoefficientTable[
            input?.structureMaterial ?? StructureMaterial.metal]
        ?[input?.roofMaterial ?? RoofMaterial.nonmetallic] ?? 1.0;

    return SectionCard(
      title: 'Construction Details',
      description: 'Select structure and roof materials to determine construction coefficient C2 based on NFPA 780 standards.',
      child: Column(
        children: [
          FieldTile(
            label: 'Structure Material',
            info: 'Primary structural material of the building. Metal structures provide better lightning protection due to conductivity.',
            child: CenteredDropdownFormField<StructureMaterial>(
              value: input?.structureMaterial ?? StructureMaterial.metal,
              onChanged: (value) {
                if (value != null) pushUpdate(structureMaterial: value);
              },
              items: StructureMaterial.values
                  .map((e) => DropdownMenuItem(
                        value: e,
                        child: Container(
                          height: 48,
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          alignment: Alignment.centerLeft,
                          child: Text(
                            e.label,
                            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
          FieldTile(
            label: 'Roof Material',
            info: 'Roof material affects lightning strike probability and fire risk. Metal roofs provide better protection.',
            child: CenteredDropdownFormField<RoofMaterial>(
              value: input?.roofMaterial ?? RoofMaterial.nonmetallic,
              onChanged: (value) {
                if (value != null) pushUpdate(roofMaterial: value);
              },
              items: RoofMaterial.values
                  .map((e) => DropdownMenuItem(
                        value: e,
                        child: Container(
                          height: 48,
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          alignment: Alignment.centerLeft,
                          child: Text(
                            e.label,
                            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
          FieldTile(
            label: 'Construction Coefficient (C2)',
            info: 'Automatically calculated from structure and roof material combination according to NFPA 780 Table. Lower values indicate better inherent protection.',
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Current C2 Value:',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  Text(
                    currentC2.toString(),
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 