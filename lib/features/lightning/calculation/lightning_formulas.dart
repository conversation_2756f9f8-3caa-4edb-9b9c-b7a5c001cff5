// lightning_formulas.dart — Lightning Protection System calculations
// =================================================================================
// Implements lightning protection system risk assessment calculations based on 
// NFPA 780 standard as shown in the provided calculation sheet images.
// All formulas are cross-referenced with the risk assessment workflow.
// No map literals are used; enums provide all look-ups for full type-safety.
// =================================================================================
//   Step-1  Collective area calculation (rectangular structure)
//   Step-2  Expected lightning strike frequency calculation (Ne)
//   Step-3  Construction coefficient lookup (C2)
//   Step-4  Tolerable lightning strike frequency calculation (Nt)
//   Step-5  Risk assessment and LPS recommendation
//   Step-6  Protection level determination
// =================================================================================
// © Author: Ibrahim Nasreddin Ibrahim

import 'dart:math' as math;
import 'package:pis_core/core/constants/nfpa780_constants.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';
import 'package:pis_core/features/lightning/calculation/lightning_result.dart';

// -----------------------------------------------------------------------------
// Common typedefs -------------------------------------------------------------

typedef Area = double; // km²
typedef Frequency = double; // events/year or fl/km²/year
typedef Length = double; // m
typedef Percentage = double; // %

// -----------------------------------------------------------------------------
// STEP-1  Collective Area Calculation ----------------------------------------

/// Validates building dimensions are positive
bool isValidBuildingDimensions(Length length, Length width, Length height) =>
    length > 0 && width > 0 && height > 0;

/// Calculates equivalent collective area (Ac) for a rectangular structure.
/// 
/// NFPA 780 formula for rectangular structure:
///   Ac = L×W + 6H(L + W) + 9πH²
/// 
/// Where:
///   L = length (m)
///   W = width (m) 
///   H = height (m)
///   Result in m², then converted to km²
/// 
/// Reference: Risk assessment sheet Page 1, Collective Area Calculation section
Area calcCollectiveAreaRectangular({
  required Length lengthM,
  required Length widthM,
  required Length heightM,
}) {
  assert(isValidBuildingDimensions(lengthM, widthM, heightM),
      'Building dimensions must be positive');
  
  final double areaM2 = lengthM * widthM + 
                       6 * heightM * (lengthM + widthM) + 
                       9 * math.pi * heightM * heightM;
  
  // Convert m² to km²
  return areaM2 / 1e6;
}

// -----------------------------------------------------------------------------
// STEP-2  Expected Lightning Strike Frequency --------------------------------

/// Calculates yearly expected lightning strike frequency to the structure (Ne).
/// 
/// NFPA 780 formula:
///   Ne = Ng × Ac × C1 × 10⁻⁶
/// 
/// Where:
///   Ng = yearly average flash density (fl/km²/year)
///   Ac = equivalent collective area (m²)
///   C1 = location factor coefficient
/// 
/// Reference: Risk assessment sheet Page 2, Calculation of Expected Lightning Strike Frequency
Frequency calcExpectedStrikeFrequency({
  required Frequency flashDensityNg,
  required Area collectiveAreaKm2,
  required double locationFactorC1,
}) {
  assert(flashDensityNg >= 0, 'Flash density must be non-negative');
  assert(collectiveAreaKm2 >= 0, 'Collective area must be non-negative');
  assert(locationFactorC1 > 0, 'Location factor must be positive');
  
  // Convert km² to m² for the calculation
  final areaM2 = collectiveAreaKm2 * 1e6;
  return flashDensityNg * areaM2 * locationFactorC1 * NFPA780Constants.neFormulaFactor;
}

// -----------------------------------------------------------------------------
// STEP-3  Construction Coefficient Lookup ------------------------------------

/// Gets construction coefficient (C2) from structure and roof material combination.
/// 
/// Reference: Risk assessment sheet Page 2, Determination of Construction Coefficient
/// Uses lookup table from NFPA780Constants.constructionCoefficientTable
double getConstructionCoefficient({
  required StructureMaterial structureMaterial,
  required RoofMaterial roofMaterial,
}) {
  final c2 = NFPA780Constants.constructionCoefficientTable[structureMaterial]?[roofMaterial];
  assert(c2 != null, 'Invalid material combination for C2 lookup');
  return c2!;
}

// -----------------------------------------------------------------------------
// STEP-4  Tolerable Lightning Strike Frequency -------------------------------

/// Calculates tolerable lightning strike frequency to the structure (Nt).
/// 
/// NFPA 780 formula:
///   Nt = (1.5 × 10⁻³) / (C2 × C3 × C4 × C5)
/// 
/// Where:
///   C2 = construction coefficient
///   C3 = structure contents coefficient  
///   C4 = structure occupancy coefficient
///   C5 = lightning consequence coefficient
/// 
/// Reference: Risk assessment sheet Page 3, Calculation of Tolerable Lightning Strike Frequency
Frequency calcTolerableStrikeFrequency({
  required double constructionCoeffC2,
  required double contentsCoeffC3,
  required double occupancyCoeffC4,
  required double consequenceCoeffC5,
}) {
  assert(constructionCoeffC2 > 0, 'Construction coefficient must be positive');
  assert(contentsCoeffC3 > 0, 'Contents coefficient must be positive');
  assert(occupancyCoeffC4 > 0, 'Occupancy coefficient must be positive');
  assert(consequenceCoeffC5 > 0, 'Consequence coefficient must be positive');
  
  final denominator = constructionCoeffC2 * contentsCoeffC3 * occupancyCoeffC4 * consequenceCoeffC5;
  return NFPA780Constants.tolerableBaseValue / denominator;
}

// -----------------------------------------------------------------------------
// STEP-5  Risk Assessment and LPS Recommendation -----------------------------

/// Calculates risk level as percentage using formula: 1 - (Nt/Ne).
/// 
/// Excel formula: 1-(C127/C72) where C127=Nt, C72=Ne
/// Reference: Risk assessment sheet Page 4, Risk Calculation
Percentage calcRiskLevel({
  required Frequency expectedFrequencyNe,
  required Frequency tolerableFrequencyNt,
}) {
  assert(expectedFrequencyNe > 0, 'Expected frequency must be positive');
  assert(tolerableFrequencyNt > 0, 'Tolerable frequency must be positive');
  return (1 - (tolerableFrequencyNt / expectedFrequencyNe)) * 100;
}

/// Determines if Lightning Protection System (LPS) should be installed.
/// 
/// General rule: If Ne > Nt, then LPS should be installed
/// Reference: Risk assessment sheet Page 4
bool shouldInstallLPS({
  required Frequency expectedFrequencyNe,
  required Frequency tolerableFrequencyNt,
}) {
  return expectedFrequencyNe > tolerableFrequencyNt;
}

// -----------------------------------------------------------------------------
// STEP-6  Protection Level Determination -------------------------------------

/// Determines protection level (1-4) based on risk percentage.
/// 
/// Protection levels (based on image reference):
///   Level 1: 0-25% risk
///   Level 2: 25-40% risk  
///   Level 3: 40-55% risk
///   Level 4: 55%+ risk
int determineProtectionLevel(Percentage riskLevel) {
  if (riskLevel < 25) return 1;
  if (riskLevel < 40) return 2;
  if (riskLevel < 55) return 3;
  return 4;
}

/// Gets risk level description text.
String getRiskLevelDescription(int protectionLevel) {
  switch (protectionLevel) {
    case 1: return 'Very Low Risk - LPS not typically required';
    case 2: return 'Low Risk - LPS may be considered'; 
    case 3: return 'Moderate Risk - LPS recommended';
    case 4: return 'High Risk - LPS should be installed';
    default: return 'Unknown risk level';
  }
}

/// Gets recommendation text based on LPS determination.
String getRecommendationText(bool lpsRecommended) {
  return lpsRecommended 
      ? 'Lightning Protection System (LPS) Should Be Installed'
      : 'Lightning Protection System (LPS) May Be Optional';
}

// -----------------------------------------------------------------------------
// MAIN CALCULATION FUNCTION --------------------------------------------------

/// Performs complete lightning protection system risk assessment.
/// 
/// This is the main entry point that orchestrates all calculation steps
/// and returns a comprehensive result object.
LightningCalculationResult performLightningRiskAssessment({
  required LightningCalculationInput input,
}) {
  // Step 1: Calculate collective area (or use manual override)
  final collectiveArea = input.manualCollectiveArea ?? 
      calcCollectiveAreaRectangular(
        lengthM: input.lengthM,
        widthM: input.widthM,
        heightM: input.heightM,
      );

  // Step 2: Calculate expected strike frequency
  final expectedFrequency = calcExpectedStrikeFrequency(
    flashDensityNg: input.flashDensityNg,
    collectiveAreaKm2: collectiveArea,
    locationFactorC1: input.c1,
  );

  // Step 3: Get construction coefficient (or use manual override)
  final c2 = input.manualConstructionCoeff ?? 
      getConstructionCoefficient(
        structureMaterial: input.structureMaterial,
        roofMaterial: input.roofMaterial,
      );

  // Step 4: Calculate tolerable strike frequency
  final tolerableFrequency = calcTolerableStrikeFrequency(
    constructionCoeffC2: c2,
    contentsCoeffC3: input.c3,
    occupancyCoeffC4: input.c4,
    consequenceCoeffC5: input.c5,
  );

  // Step 5: Calculate risk level and LPS recommendation
  final riskLevel = calcRiskLevel(
    expectedFrequencyNe: expectedFrequency,
    tolerableFrequencyNt: tolerableFrequency,
  );
  
  final lpsRecommended = shouldInstallLPS(
    expectedFrequencyNe: expectedFrequency,
    tolerableFrequencyNt: tolerableFrequency,
  );

  // Step 6: Determine protection level and descriptions
  final protectionLevel = determineProtectionLevel(riskLevel);
  final riskDescription = getRiskLevelDescription(protectionLevel);
  final recommendationText = getRecommendationText(lpsRecommended);

  return LightningCalculationResult(
    collectiveAreaKm2: collectiveArea,
    expectedStrikeFrequency: expectedFrequency,
    tolerableStrikeFrequency: tolerableFrequency,
    riskLevel: riskLevel,
    lpsRecommended: lpsRecommended,
    protectionLevel: protectionLevel,
    c1LocationFactor: input.c1,
    c2ConstructionCoeff: c2,
    c3ContentsCoeff: input.c3,
    c4OccupancyCoeff: input.c4,
    c5ConsequenceCoeff: input.c5,
    buildingLengthM: input.lengthM,
    buildingWidthM: input.widthM,
    buildingHeightM: input.heightM,
    location: input.location,
    flashDensityNg: input.flashDensityNg,
    riskLevelDescription: riskDescription,
    recommendationText: recommendationText,
  );
}

// -----------------------------------------------------------------------------
// Validation helpers ----------------------------------------------------------

/// Validates all input parameters are within reasonable ranges
bool validateLightningInput(LightningCalculationInput input) {
  return isValidBuildingDimensions(input.lengthM, input.widthM, input.heightM) &&
         input.flashDensityNg >= 0 &&
         input.c1 > 0 &&
         input.c3 > 0 &&
         input.c4 > 0 &&
         input.c5 > 0;
} 