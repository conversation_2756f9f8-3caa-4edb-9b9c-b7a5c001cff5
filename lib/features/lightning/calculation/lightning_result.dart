// lightning_result.dart
// ---------------------------------------------------------------------------
// Results from lightning protection system risk assessment calculations
// based on NFPA 780 standard.
// ---------------------------------------------------------------------------

import 'package:freezed_annotation/freezed_annotation.dart';

part 'lightning_result.freezed.dart';

@freezed
abstract class LightningCalculationResult with _$Lightning<PERSON>alculation<PERSON>esult {
  const LightningCalculationResult._();
  
  const factory LightningCalculationResult({
    // --- Core Calculation Results ------------------------------------------
    required double collectiveAreaKm2,          // Ac (km²)
    required double expectedStrikeFrequency,    // Ne (events/year)
    required double tolerableStrikeFrequency,   // Nt (events/year)
    required double riskLevel,                  // Percentage (Ne/Nt * 100)
    required bool lpsRecommended,               // Whether LPS should be installed
    required int protectionLevel,               // 1-4 protection level

    // --- Intermediate Calculation Values -----------------------------------
    required double c1LocationFactor,
    required double c2ConstructionCoeff,
    required double c3ContentsCoeff,
    required double c4OccupancyCoeff,
    required double c5ConsequenceCoeff,

    // --- Building Information Used -----------------------------------------
    required double buildingLengthM,
    required double buildingWidthM, 
    required double buildingHeightM,
    required String location,
    required double flashDensityNg,

    // --- Risk Assessment Details -------------------------------------------
    required String riskLevelDescription,
    required String recommendationText,
  }) = _LightningCalculationResult;

  @override
  String toString() =>
      '''Lightning Protection Result:
  collectiveAreaKm2        = ${collectiveAreaKm2.toStringAsFixed(6)} km²
  expectedStrikeFrequency  = ${expectedStrikeFrequency.toStringAsFixed(4)} events/year
  tolerableStrikeFrequency = ${tolerableStrikeFrequency.toStringAsFixed(4)} events/year
  riskLevel                = ${riskLevel.toStringAsFixed(1)}%
  lpsRecommended           = $lpsRecommended
  protectionLevel          = Level $protectionLevel
  
  Coefficients:
  C1 (Location Factor)     = $c1LocationFactor
  C2 (Construction)        = $c2ConstructionCoeff
  C3 (Contents)            = $c3ContentsCoeff
  C4 (Occupancy)           = $c4OccupancyCoeff  
  C5 (Consequence)         = $c5ConsequenceCoeff
  
  Building:
  Dimensions               = ${buildingLengthM}m × ${buildingWidthM}m × ${buildingHeightM}m
  Location                 = $location (Ng = $flashDensityNg fl/km²/year)
  
  Assessment:
  Risk Level               = $riskLevelDescription
  Recommendation           = $recommendationText''';
} 