// lightning_input.dart
// ---------------------------------------------------------------------------
// All user-supplied parameters needed for lightning protection system
// risk assessment calculations based on NFPA 780.
// Safe engineering defaults are provided where practical.
// ---------------------------------------------------------------------------

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';

part 'lightning_input.freezed.dart';

@freezed
abstract class LightningCalculationInput with _$LightningCalculationInput {
  const LightningCalculationInput._(); // enables custom getters

  const factory LightningCalculationInput({
    // ---------------- Project Details ------------------------------------
    @Default('Data Center - Distributor Room') String projectName,
    @Default('Hassan Allam') String mainContractor,

    // ---------------- Building Dimensions --------------------------------
    @Default(41.0) double lengthM, // L (m)
    @Default(8.0) double widthM,   // W (m) 
    @Default(11.0) double heightM, // H (m)

    // ---------------- Location and Environmental ----------------------
    @Default('Egypt') String location,
    @Default(1.0) double flashDensityNg, // fl/km²/year (from location)
    @Default(LocationFactorCoeff.isolated) LocationFactorCoeff locationFactor, // C1

    // ---------------- Construction Coefficients ----------------------
    @Default(StructureMaterial.metal) StructureMaterial structureMaterial,
    @Default(RoofMaterial.nonmetallic) RoofMaterial roofMaterial,
    // C2 is calculated from structureMaterial + roofMaterial combination

    // ---------------- Structure Characteristics ----------------------
    @Default(StructureContentsCoeff.lowValue) StructureContentsCoeff contentsCoeff, // C3
    @Default(StructureOccupancyCoeff.normallyOccupied) StructureOccupancyCoeff occupancyCoeff, // C4
    @Default(LightningConsequenceCoeff.continuityNotRequired) LightningConsequenceCoeff consequenceCoeff, // C5

    // ---------------- Manual Overrides (for testing/validation) -------
    double? manualCollectiveArea, // km² - if provided, overrides calculated Ac
    double? manualConstructionCoeff, // if provided, overrides C2 table lookup
  }) = _LightningCalculationInput;

  // Quick helpers
  double get c1 => locationFactor.value;
  double get c3 => contentsCoeff.value;
  double get c4 => occupancyCoeff.value;
  double get c5 => consequenceCoeff.value;

  /// Get construction coefficient C2 from lookup table
  double get c2 => manualConstructionCoeff ?? 
      NFPA780Constants.constructionCoefficientTable[structureMaterial]?[roofMaterial] ?? 1.0;

  @override
  String toString() {
    return '''Lightning Input:
  projectName          = $projectName
  mainContractor       = $mainContractor
  lengthM              = $lengthM m
  widthM               = $widthM m
  heightM              = $heightM m
  location             = $location
  flashDensityNg       = $flashDensityNg fl/km²/year
  locationFactor       = $locationFactor (C1 = $c1)
  structureMaterial    = $structureMaterial
  roofMaterial         = $roofMaterial
  constructionCoeff    = $c2 (C2)
  contentsCoeff        = $contentsCoeff (C3 = $c3)
  occupancyCoeff       = $occupancyCoeff (C4 = $c4)
  consequenceCoeff     = $consequenceCoeff (C5 = $c5)
  manualCollectiveArea = $manualCollectiveArea km²
  manualConstructionCoeff = $manualConstructionCoeff''';
  }
} 