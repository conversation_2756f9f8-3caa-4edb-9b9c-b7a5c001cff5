import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:animated_text_kit/animated_text_kit.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth > 800;
    
    return Scaffold(
      backgroundColor: AppColors.surface,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                AppColors.primaryBlue,
                AppColors.darkBlue,
                AppColors.primaryRed.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: AppBar(
            title: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 32,
                  height: 32,
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: Image.asset(
                      'assets/logo/PIS logo.png',
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                const Text('PIS Core'),
              ],
            ),
            centerTitle: true,
            elevation: 0,
            backgroundColor: Colors.transparent,
          ),
        ),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.lightBlue.withValues(alpha: 0.8),
              AppColors.primaryRed.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: isDesktop ? 80 : 24,
              vertical: isDesktop ? 40 : 20,
            ),
            child: Column(
              children: [
                // Single PIS Logo - Made bigger
                Expanded(
                  flex: 3,
                  child: Center(
                    child: Container(
                      width: double.infinity, // Take full width minus padding
                      constraints: BoxConstraints(
                        maxHeight: isDesktop ? 300 : 200,
                      ),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(
                          color: AppColors.white.withValues(alpha: 0.2),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryBlue.withValues(alpha: 0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Image.asset(
                          'assets/logo/PIS logo full.png',
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                ),

                // Animated Labels Row
                if (isDesktop) ...[
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          // Professional Solutions Label
                          Expanded(
                            child: _AnimatedTextLabel(
                              texts: [
                                'Professional Solutions',
                                'Engineering Excellence',
                                'Technical Innovation',
                                'Quality Assurance',
                              ],
                              color: AppColors.primaryBlue,
                            ),
                          ),
                          const SizedBox(width: 16),
                          // Manufacturing Excellence Label
                          Expanded(
                            child: _AnimatedTextLabel(
                              texts: [
                                'Manufacturing Excellence',
                                'Industrial Solutions',
                                'Production Quality',
                                'Advanced Technology',
                              ],
                              color: AppColors.primaryRed,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ] else ...[
                  // Mobile: Stack the labels vertically
                  Expanded(
                    child: Column(
                      children: [
                        Expanded(
                          child: _AnimatedTextLabel(
                            texts: [
                              'Professional Solutions',
                              'Engineering Excellence',
                              'Technical Innovation',
                            ],
                            color: AppColors.primaryBlue,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: _AnimatedTextLabel(
                            texts: [
                              'Manufacturing Excellence',
                              'Industrial Solutions',
                              'Production Quality',
                            ],
                            color: AppColors.primaryRed,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                // Animated Title
                Expanded(
                  child: Center(
                    child: Stack(
                      children: [
                        // Text stroke (outline)
                        AnimatedTextKit(
                          animatedTexts: [
                            TypewriterAnimatedText(
                              'Professional Engineering Calculations',
                              textAlign: TextAlign.center,
                              textStyle: Theme.of(context).textTheme.displaySmall?.copyWith(
                                foreground: Paint()
                                  ..style = PaintingStyle.stroke
                                  ..strokeWidth = 2
                                  ..color = AppColors.white,
                                fontWeight: FontWeight.bold,
                              ),
                              speed: const Duration(milliseconds: 60),
                            ),
                          ],
                          totalRepeatCount: 1,
                          pause: const Duration(milliseconds: 2000),
                          displayFullTextOnTap: true,
                          stopPauseOnTap: false,
                        ),
                        // Text fill
                        AnimatedTextKit(
                          animatedTexts: [
                            TypewriterAnimatedText(
                              'Professional Engineering Calculations',
                              textAlign: TextAlign.center,
                              textStyle: Theme.of(context).textTheme.displaySmall?.copyWith(
                                color: AppColors.primaryBlue,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              speed: const Duration(milliseconds: 60),
                            ),
                          ],
                          totalRepeatCount: 1,
                          pause: const Duration(milliseconds: 2000),
                          displayFullTextOnTap: true,
                          stopPauseOnTap: false,
                        ),
                      ],
                    ),
                  ),
                ),

                // Animated Subtitle
                Expanded(
                  child: Center(
                    child: Stack(
                      children: [
                        // Text stroke (outline)
                        AnimatedTextKit(
                          animatedTexts: [
                            TypewriterAnimatedText(
                              'Integrated Protection, Professionally Delivered',
                              textAlign: TextAlign.center,
                              textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                                foreground: Paint()
                                  ..style = PaintingStyle.stroke
                                  ..strokeWidth = 1.5
                                  ..color = AppColors.white,
                                fontWeight: FontWeight.w600,
                              ),
                              speed: const Duration(milliseconds: 80),
                            ),
                          ],
                          totalRepeatCount: 1,
                          pause: const Duration(milliseconds: 1800),
                          displayFullTextOnTap: true,
                          stopPauseOnTap: false,
                        ),
                        // Text fill
                        AnimatedTextKit(
                          animatedTexts: [
                            TypewriterAnimatedText(
                              'Integrated Protection, Professionally Delivered',
                              textAlign: TextAlign.center,
                              textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                                color: AppColors.primaryRed,
                                fontWeight: FontWeight.w600,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withValues(alpha: 0.2),
                                    blurRadius: 2,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              speed: const Duration(milliseconds: 80),
                            ),
                          ],
                          totalRepeatCount: 1,
                          pause: const Duration(milliseconds: 1800),
                          displayFullTextOnTap: true,
                          stopPauseOnTap: false,
                        ),
                      ],
                    ),
                  ),
                ),

                // Continue Button
                Expanded(
                  child: Center(
                    child: ElevatedButton(
                      onPressed: () => context.go('/modules'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: AppColors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: isDesktop ? 60 : 40,
                          vertical: isDesktop ? 20 : 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                        elevation: 8,
                        shadowColor: AppColors.primaryBlue.withValues(alpha: 0.3),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Text(
                              isDesktop ? 'Continue to Modules' : 'Continue',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Icon(Icons.arrow_forward, color: AppColors.white),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _AnimatedTextLabel extends StatelessWidget {
  final List<String> texts;
  final Color color;

  const _AnimatedTextLabel({
    required this.texts,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: AnimatedTextKit(
          animatedTexts: [
            for (String text in texts)
              TypewriterAnimatedText(
                text,
                textAlign: TextAlign.center,
                textStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                ),
                speed: const Duration(milliseconds: 80),
              ),
          ],
          totalRepeatCount: 999999, // Infinite loop
          pause: const Duration(milliseconds: 2000),
          displayFullTextOnTap: true,
          stopPauseOnTap: false,
        ),
      ),
    );
  }
}
