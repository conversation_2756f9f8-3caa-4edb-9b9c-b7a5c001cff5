import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/ui/widgets/module_card.dart';

class ModuleSelectionPage extends StatelessWidget {
  const ModuleSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth > 800;

    return Scaffold(
      backgroundColor: AppColors.white,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          'Select a Calculation Module',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        elevation: 2,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.white),
          onPressed: () => context.go('/home'),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.lightBlue.withValues(alpha: 0.1),
              AppColors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: isDesktop ? 80 : 24,
              vertical: isDesktop ? 40 : 20,
            ),
            child: Column(
              children: [
                // Description text
                Flexible(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      vertical: isDesktop ? 20 : 10,
                    ),
                    child: Text(
                      'Choose from our professional calculation modules designed for electrical protection systems',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.darkGrey,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),

                // Module Cards
                Expanded(
                  flex: 3,
                  child: Center(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxWidth: isDesktop ? 1000 : double.infinity,
                      ),
                      child: isDesktop
                          ? Row(
                              children: [
                                Expanded(
                                  child: ModuleCard(
                                    icon: Icons.cable,
                                    title: 'Earthing System',
                                    description: 'Calculate earth electrode resistance, cable sizing, and soil resistivity using BS 7430 standards',
                                    color: AppColors.primaryRed,
                                    imagePath: 'assets/images/earthing_system.svg',
                                    onTap: () => context.go('/earthing'),
                                    isDesktop: isDesktop,
                                  ),
                                ),
                                const SizedBox(width: 24),
                                Expanded(
                                  child: ModuleCard(
                                    icon: Icons.flash_on,
                                    title: 'Lightning Protection',
                                    description: 'Risk assessment and lightning protection system design using NFPA 780 standards',
                                    color: AppColors.primaryBlue,
                                    imagePath: 'assets/images/lightning_protection.svg',
                                    onTap: () => context.go('/lightning'),
                                    isDesktop: isDesktop,
                                  ),
                                ),
                              ],
                            )
                          : Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                ModuleCard(
                                  icon: Icons.cable,
                                  title: 'Earthing System',
                                  description: 'Calculate earth electrode resistance, cable sizing, and soil resistivity using BS 7430 standards',
                                  color: AppColors.primaryRed,
                                  imagePath: 'assets/images/earthing_system.svg',
                                  onTap: () => context.go('/earthing'),
                                  isDesktop: isDesktop,
                                ),
                                const SizedBox(height: 24),
                                ModuleCard(
                                  icon: Icons.flash_on,
                                  title: 'Lightning Protection',
                                  description: 'Risk assessment and lightning protection system design using NFPA 780 standards',
                                  color: AppColors.primaryBlue,
                                  imagePath: 'assets/images/lightning_protection.svg',
                                  onTap: () => context.go('/lightning'),
                                  isDesktop: isDesktop,
                                ),
                              ],
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
