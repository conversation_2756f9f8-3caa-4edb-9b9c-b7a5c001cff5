import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';

class CommonInfoRow extends StatelessWidget {
  final String parameter;
  final String value;
  final bool isHighlighted;
  final bool isLast;
  final Color? statusColor;
  final Color borderColor;

  const CommonInfoRow({
    super.key,
    required this.parameter,
    required this.value,
    this.isHighlighted = false,
    this.isLast = false,
    this.statusColor,
    this.borderColor = AppColors.primaryBlue,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: isHighlighted 
            ? borderColor.withValues(alpha: 0.05)
            : Colors.transparent,
        border: isLast 
            ? null 
            : Border(
                bottom: BorderSide(
                  color: borderColor.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                parameter,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGrey,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: statusColor != null 
                  ? Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor!.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: statusColor!, width: 1),
                      ),
                      child: Text(
                        value,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: statusColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    )
                  : Text(
                      value,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isHighlighted ? borderColor : AppColors.darkGrey,
                        fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
