import 'package:flutter/material.dart';
import 'package:pis_core/core/widgets/report_preview/common_section_header.dart';

class CommonSectionContainer extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color headerColor;
  final Color borderColor;
  final List<Widget> children;

  const CommonSectionContainer({
    super.key,
    required this.title,
    required this.icon,
    required this.headerColor,
    required this.borderColor,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          CommonSectionHeader(
            title: title,
            icon: icon,
            backgroundColor: headerColor,
          ),
          
          // Section Content
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: borderColor, width: 1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }
}
