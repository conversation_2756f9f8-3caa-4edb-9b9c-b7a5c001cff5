import 'dart:io';
import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/app_theme.dart';
import 'package:pis_core/core/services/pdf_service.dart';
import 'dart:developer' as developer;

class CommonReportSuccessDialog extends StatelessWidget {
  final File reportFile;
  final String reportTitle;

  const CommonReportSuccessDialog({
    super.key,
    required this.reportFile,
    required this.reportTitle,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green, size: 28),
          SizedBox(width: 12),
          Text('PDF Generated Successfully'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('$reportTitle has been generated and saved to:'),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              reportFile.path,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
        ElevatedButton.icon(
          onPressed: () async {
            try {
              final directory = reportFile.parent;
              if (Platform.isMacOS) {
                await Process.run('open', [directory.path]);
              } else if (Platform.isWindows) {
                await Process.run('explorer', [directory.path]);
              } else if (Platform.isLinux) {
                await Process.run('xdg-open', [directory.path]);
              }
            } catch (e) {
              developer.log('Error opening file location: $e');
            }
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          icon: const Icon(Icons.folder_open),
          label: Text(Platform.isMacOS ? 'Open Finder' : 'Open Explorer'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryRed,
            foregroundColor: Colors.white,
          ),
        ),
        ElevatedButton.icon(
          onPressed: () async {
            try {
              Navigator.of(context).pop(); // Close dialog first
              await PdfService.shareReport(reportFile, reportTitle);
            } catch (e, stackTrace) {
              developer.log(
                'Error sharing PDF report',
                error: e,
                stackTrace: stackTrace,
                name: 'CommonReportSuccessDialog',
              );

              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Failed to share report. File has been saved to your documents.'),
                    backgroundColor: Colors.orange,
                    duration: Duration(seconds: 3),
                  ),
                );
              }
            }
          },
          icon: const Icon(Icons.share),
          label: const Text('Share'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryBlue,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  static void show(BuildContext context, File reportFile, String reportTitle) {
    showDialog(
      context: context,
      builder: (context) => CommonReportSuccessDialog(
        reportFile: reportFile,
        reportTitle: reportTitle,
      ),
    );
  }
}
