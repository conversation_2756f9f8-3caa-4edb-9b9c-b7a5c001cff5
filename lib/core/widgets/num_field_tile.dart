import 'package:flutter/material.dart';
import 'field_tile.dart';

class NumFieldTile extends StatelessWidget {
  const NumFieldTile({
    super.key,
    required this.label,
    required this.controller,
    this.suffix,
    this.info,
    this.equation,
    this.onChanged,
    this.focusNode,
  });

  final String label;
  final TextEditingController controller;
  final String? suffix;
  final String? info;
  final String? equation;
  final VoidCallback? onChanged;
  final FocusNode? focusNode;

  @override
  Widget build(BuildContext context) {
    return FieldTile(
      label: label,
      info: info,
      equation: equation,
      child: SizedBox(
        child: TextField(
          textAlign: TextAlign.center,
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            suffixText: suffix,
            border: const OutlineInputBorder(),
            isDense: true,
            contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          ),
          onChanged: (_) => onChanged?.call(),
          focusNode: focusNode,
        ),
      ),
    );
  }
} 