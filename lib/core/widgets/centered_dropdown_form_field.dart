import 'package:flutter/material.dart';

/// A wrapper around [DropdownButtonFormField] that ensures the selected value
/// (when the dropdown is closed) is centred, and that the menu is expanded to
/// the full width of the field.
class CenteredDropdownFormField<T> extends StatelessWidget {
  const CenteredDropdownFormField({
    super.key,
    required this.value,
    required this.items,
    this.onChanged,
    this.decoration = const InputDecoration(
      border: OutlineInputBorder(),
      isDense: true,
    ),
    this.onSaved,
    this.validator,
  });

  final T? value;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final InputDecoration decoration;
  final FormFieldSetter<T>? onSaved;
  final FormFieldValidator<T>? validator;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: onChanged,
      onSaved: onSaved,
      validator: validator,
      decoration: decoration,
      isExpanded: true,
      alignment: Alignment.center,
      selectedItemBuilder: (ctx) => items
          .map((item) => Align(
                alignment: Alignment.center,
                child: _extractCenteredText(item.child),
              ))
          .toList(),
    );
  }

  /// Ensures that any [Text] inside the dropdown uses centred alignment.
  Widget _extractCenteredText(Widget child) {
    if (child is Text) {
      return Text(
        child.data ?? '',
        style: child.style,
        textAlign: TextAlign.center,
      );
    }
    if (child is Center || child is Align) {
      // Attempt to find a Text inside.
      final inner = (child as dynamic).child;
      if (inner is Text) {
        return Text(
          inner.data ?? '',
          style: inner.style,
          textAlign: TextAlign.center,
        );
      }
    }
    return child;
  }
} 