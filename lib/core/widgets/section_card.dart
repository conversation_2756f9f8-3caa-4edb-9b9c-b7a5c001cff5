import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../theme/app_theme.dart';

class SectionCard extends HookWidget {
  const SectionCard({
    super.key,
    required this.title,
    required this.child,
    this.description,
    this.initiallyExpanded = false,
  });

  final String title;
  final String? description;
  final Widget child;
  final bool initiallyExpanded;

  @override
  Widget build(BuildContext context) {
    final isHovered = useState(false);
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth > 800;

    return MouseRegion(
      onEnter: (_) => isHovered.value = true,
      onExit: (_) => isHovered.value = false,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        child: Card(
          elevation: isHovered.value && isDesktop ? 8 : 4,
          shadowColor: AppColors.primaryBlue.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: isHovered.value && isDesktop
                ? BorderSide(color: AppColors.primaryBlue.withValues(alpha: 0.2), width: 1)
                : BorderSide.none,
          ),
          child: ExpansionTile(
            title: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.primaryBlue,
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: description == null
                ? null
                : Text(
                    description!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.darkGrey,
                    ),
                  ),
            initiallyExpanded: initiallyExpanded,
            iconColor: AppColors.primaryBlue,
            collapsedIconColor: AppColors.primaryBlue,
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: child,
              ),
            ],
          ),
        ),
      ),
    );
  }
}