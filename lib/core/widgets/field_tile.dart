import 'package:flutter/material.dart';

/// A reusable form row that displays a label, the input widget and an optional
/// info icon. Tapping the info icon pops up a dialog showing the description
/// and equation (if supplied).
class FieldTile extends StatelessWidget {
  const FieldTile({
    super.key,
    required this.label,
    required this.child,
    this.info,
    this.equation,
  });

  final String label;
  final Widget child;
  final String? info;
  final String? equation;

  void _showInfo(BuildContext context) {
    if (info == null && equation == null) return;
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(label),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (info != null) Text(info!),
            if (equation != null) ...[
              const SizedBox(height: 12),
              // Display equations using RichText for simple super/sub-script.
              SelectableText(
                equation!,
                style: const TextStyle(fontFamily: 'monospace'),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final inputRow = Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          flex: 1,
          child: Row(
            children: [
              Flexible(
                child: Text(
                  label,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ),
              if (info != null || equation != null)
                IconButton(
                  icon: const Icon(Icons.info_outline),
                  splashRadius: 20,
                  color: Theme.of(context).colorScheme.primary,
                  onPressed: () => _showInfo(context),
                ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        Expanded(flex: 2, child: child),
      ],
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: inputRow,
    );
  }
} 