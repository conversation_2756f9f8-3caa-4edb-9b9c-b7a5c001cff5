import 'package:flutter/material.dart';
import 'package:pis_core/core/theme/theme_helper.dart';
import 'package:pis_core/core/theme/app_theme.dart';

/// A responsive layout widget that provides consistent spacing and layout patterns
class ResponsiveLayout extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final bool addBottomSpacing;
  final double? maxWidth;

  const ResponsiveLayout({
    super.key,
    required this.child,
    this.padding,
    this.addBottomSpacing = true,
    this.maxWidth,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = child;

    // Add bottom spacing for navigation buttons if requested
    if (addBottomSpacing) {
      content = Column(
        children: [
          Expanded(child: child),
          SizedBox(height: context.scaledFontSize(24)), // Responsive spacing
        ],
      );
    }

    // Apply padding
    if (padding != null) {
      content = Padding(padding: padding!, child: content);
    }

    // Apply max width constraint if specified
    if (maxWidth != null) {
      content = Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: maxWidth!),
          child: content,
        ),
      );
    }

    return content;
  }
}

/// A responsive section card with consistent styling
class ResponsiveSectionCard extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final Widget child;
  final IconData? icon;
  final Color? iconColor;
  final EdgeInsets? padding;
  final bool isScrollable;

  const ResponsiveSectionCard({
    super.key,
    this.title,
    this.subtitle,
    required this.child,
    this.icon,
    this.iconColor,
    this.padding,
    this.isScrollable = false,
  });

  @override
  Widget build(BuildContext context) {
    final cardPadding = padding ?? context.responsiveAllPadding(24);
    
    Widget content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          if (icon != null)
            Row(
              children: [
                Container(
                  width: context.responsiveIconSize(48),
                  height: context.responsiveIconSize(48),
                  decoration: BoxDecoration(
                    color: (iconColor ?? AppColors.primaryBlue).withValues(alpha: 0.1),
                    borderRadius: context.responsiveBorderRadius(12),
                  ),
                  child: Icon(
                    icon,
                    color: iconColor ?? AppColors.primaryBlue,
                    size: context.responsiveIconSize(24),
                  ),
                ),
                SizedBox(width: context.scaledFontSize(16)),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title!,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.visible,
                        softWrap: true,
                      ),
                      if (subtitle != null) ...[
                        SizedBox(height: context.scaledFontSize(4)),
                        Text(
                          subtitle!,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.darkGrey,
                          ),
                          overflow: TextOverflow.visible,
                          softWrap: true,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            )
          else
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title!,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.visible,
                  softWrap: true,
                ),
                if (subtitle != null) ...[
                  SizedBox(height: context.scaledFontSize(4)),
                  Text(
                    subtitle!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.darkGrey,
                    ),
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                ],
              ],
            ),
          SizedBox(height: context.scaledFontSize(24)),
        ],
        if (isScrollable)
          Expanded(child: SingleChildScrollView(child: child))
        else
          child,
      ],
    );

    return Container(
      padding: cardPadding,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: context.responsiveBorderRadius(12),
        border: Border.all(color: AppColors.outline),
      ),
      child: content,
    );
  }
}

/// A responsive row widget that handles text wrapping properly
class ResponsiveRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final double spacing;

  const ResponsiveRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.spacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1) {
        spacedChildren.add(SizedBox(width: context.scaledFontSize(spacing)));
      }
    }

    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: spacedChildren,
    );
  }
}

/// A responsive text widget that handles overflow properly
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow overflow;
  final bool softWrap;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow = TextOverflow.visible,
    this.softWrap = true,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      softWrap: softWrap,
    );
  }
}

/// A responsive flexible text widget for use in rows
class ResponsiveFlexibleText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int flex;
  final FlexFit fit;

  const ResponsiveFlexibleText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.flex = 1,
    this.fit = FlexFit.loose,
  });

  @override
  Widget build(BuildContext context) {
    return Flexible(
      flex: flex,
      fit: fit,
      child: ResponsiveText(
        text,
        style: style,
        textAlign: textAlign,
      ),
    );
  }
}
