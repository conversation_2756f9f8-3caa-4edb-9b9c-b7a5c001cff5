/// Lightning Protection System constants and lookup tables based on NFPA 780
/// Referenced from risk assessment sheet images provided
class NFPA780Constants {
  /// Yearly expected lightning strike frequency to the structure (Ne) factor
  static const double neFormulaFactor = 1e-6; // 10^-6

  /// Base value for tolerable lightning strike frequency calculation
  static const double tolerableBaseValue = 1.5e-3; // 1.5 × 10^-3

  /// Lightning consequence coefficient descriptions for UI reference
  static const Map<LightningConsequenceCoeff, String> consequenceCoefficientDescriptions = {
    LightningConsequenceCoeff.continuityNotRequired: 'Continuity of facility services not required, no environmental impact',
    LightningConsequenceCoeff.continuityRequired: 'Continuity of facility services required, no environmental impact',
    LightningConsequenceCoeff.consequenceToEnvironment: 'Consequence to the environment',
  };

  /// Structure occupancy coefficient descriptions for UI reference
  static const Map<StructureOccupancyCoeff, String> occupancyCoefficientDescriptions = {
    StructureOccupancyCoeff.unoccupied: 'Unoccupied',
    StructureOccupancyCoeff.normallyOccupied: 'Normally Occupied',
    StructureOccupancyCoeff.difficultEvacuation: 'Difficult to evacuate or risk of panic',
  };

  /// Structure contents coefficient descriptions for UI reference
  static const Map<StructureContentsCoeff, String> contentsCoefficientDescriptions = {
    StructureContentsCoeff.lowValue: 'Low value and noncombustible',
    StructureContentsCoeff.standardValue: 'Standard value and noncombustible',
    StructureContentsCoeff.highValue: 'High value, moderate combustibility',
    StructureContentsCoeff.exceptionalValue: 'Exceptional value, flammable liquids, computer or electronics',
    StructureContentsCoeff.irreplaceableValue: 'Exceptional value, irreplaceable cultural items',
  };

  /// Location factor descriptions for UI reference
  static const Map<LocationFactorCoeff, String> locationFactorDescriptions = {
    LocationFactorCoeff.surroundedByTaller: 'Structure surrounded by taller structures or trees within a distance of 3H',
    LocationFactorCoeff.surroundedByEqual: 'Structure surrounded by structures of equal or lesser height within a distance of 3H',
    LocationFactorCoeff.isolated: 'Isolated structure, with no other structures located within a distance of 3H',
    LocationFactorCoeff.isolatedOnHilltop: 'Isolated structure on hilltop',
  };

  /// Construction coefficient lookup table
  /// Rows: Structure Material, Columns: Roof Material (Metal, Nonmetallic, Combustible)
  static const Map<StructureMaterial, Map<RoofMaterial, double>> constructionCoefficientTable = {
    StructureMaterial.metal: {
      RoofMaterial.metal: 0.5,
      RoofMaterial.nonmetallic: 1.0,
      RoofMaterial.combustible: 2.0,
    },
    StructureMaterial.nonmetallic: {
      RoofMaterial.metal: 1.0,
      RoofMaterial.nonmetallic: 1.0,
      RoofMaterial.combustible: 2.5,
    },
    StructureMaterial.combustible: {
      RoofMaterial.metal: 2.0,
      RoofMaterial.nonmetallic: 2.5,
      RoofMaterial.combustible: 3.0,
    },
  };

  /// Flash density lookup by country/region (examples shown in image)
  /// Users should select based on their location
  static const Map<String, double> flashDensityByLocation = {
    'Egypt': 1.0,
    'Saudi Arabia': 2.0,
    'UAE': 1.5,
    'Kuwait': 1.8,
    'Qatar': 1.2,
    'Bahrain': 1.3,
    'Oman': 1.6,
    // Add more locations as needed
  };

  /// Abbreviations for UI reference and documentation
  static const Map<String, String> abbreviations = {
    'Ne': 'Yearly expected lightning strike frequency to the structure',
    'Nt': 'Tolerable lightning strike frequency to the structure',
    'Ng': 'Yearly average flash density in the region (fl/km²/year)',
    'Ac': 'Equivalent collective area of the structure (km²)',
    'Ce': 'Environmental coefficient',
    'C1': 'Location Factor Coefficient',
    'C2': 'Construction Coefficient',
    'C3': 'Structure Contents Coefficient',
    'C4': 'Structure Occupancy Coefficient',
    'C5': 'Lightning Consequence Coefficient',
  };

  /// Risk level determination thresholds and descriptions
  static const Map<String, String> riskLevelDescriptions = {
    'Level 1': 'Very Low Risk - LPS not typically required',
    'Level 2': 'Low Risk - LPS may be considered',
    'Level 3': 'Moderate Risk - LPS recommended',
    'Level 4': 'High Risk - LPS should be installed',
  };
}

/// Location factor coefficient C1
enum LocationFactorCoeff {
  surroundedByTaller(0.25),
  surroundedByEqual(0.5),
  isolated(1.0),
  isolatedOnHilltop(2.0);

  const LocationFactorCoeff(this.value);
  final double value;

  String get description => NFPA780Constants.locationFactorDescriptions[this] ?? '';
}

/// Structure material for construction coefficient C2
enum StructureMaterial {
  metal('Metal'),
  nonmetallic('Nonmetallic'),
  combustible('Combustible');

  const StructureMaterial(this.label);
  final String label;
}

/// Roof material for construction coefficient C2
enum RoofMaterial {
  metal('Metal'),
  nonmetallic('Nonmetallic'),
  combustible('Combustible');

  const RoofMaterial(this.label);
  final String label;
}

/// Structure contents coefficient C3
enum StructureContentsCoeff {
  lowValue(0.5),
  standardValue(1.0),
  highValue(2.0),
  exceptionalValue(3.0),
  irreplaceableValue(4.0);

  const StructureContentsCoeff(this.value);
  final double value;

  String get description => NFPA780Constants.contentsCoefficientDescriptions[this] ?? '';
}

/// Structure occupancy coefficient C4
enum StructureOccupancyCoeff {
  unoccupied(0.5),
  normallyOccupied(1.0),
  difficultEvacuation(3.0);

  const StructureOccupancyCoeff(this.value);
  final double value;

  String get description => NFPA780Constants.occupancyCoefficientDescriptions[this] ?? '';
}

/// Lightning consequence coefficient C5
enum LightningConsequenceCoeff {
  continuityNotRequired(1.0),
  continuityRequired(5.0),
  consequenceToEnvironment(10.0);

  const LightningConsequenceCoeff(this.value);
  final double value;

  String get description => NFPA780Constants.consequenceCoefficientDescriptions[this] ?? '';
} 