/// Cable and rod specification data class
class CableRodSpec {
  /// Cross-sectional area in mm²
  final String size;
  /// Diameter in meters
  final double diameter;
  /// Rod description (e.g., '2*1.2')
  final String rod;
  /// Length in meters
  final double length;
  const CableRodSpec({
    required this.size,
    required this.diameter,
    required this.rod,
    required this.length,
  });

  /// JSON serialization
  factory CableRodSpec.fromJson(Map<String, dynamic> json) => CableRodSpec(
        size: json['size'] as String,
        diameter: (json['diameter'] as num).toDouble(),
        rod: json['rod'] as String,
        length: (json['length'] as num).toDouble(),
      );
  Map<String, dynamic> toJson() => {
        'size': size,
        'diameter': diameter,
        'rod': rod,
        'length': length,
      };
}

// Constants and values from BS 7430:2011+A1:2015 standard
class BS7430Constants {
  /// Default enhancement material resistivity (Ohm.m)
  static const double defaultEnhancementResistivity = 0.36;

  /// Default infill diameter (m)
  static const double defaultInfillDiameter = 0.025;

  /// Default arrangement factor for straight run
  static const double straightRunArrangementFactor = 1.83;

  /// Target maximum total resistance (Ohm)
  static const double targetMaxResistance = 2.0;

  /// List of cable/rod specifications from the reference table
  static const List<CableRodSpec> cableRodSpecs = [
    CableRodSpec(size: '1x10', diameter: 0.0037, rod: '2*1.2', length: 2.4),
    CableRodSpec(size: '1x16', diameter: 0.0047, rod: '2*1.5', length: 3.0),
    CableRodSpec(size: '1x25', diameter: 0.0058, rod: '3*1.2', length: 3.6),
    CableRodSpec(size: '1x35', diameter: 0.0069, rod: '3*1.5', length: 4.5),
    CableRodSpec(size: '1x50', diameter: 0.0087, rod: '4*1.2', length: 4.8),
    CableRodSpec(size: '1x70', diameter: 0.0098, rod: '4*1.5', length: 6.0),
    CableRodSpec(size: '1x95', diameter: 0.0113, rod: '5*1.2', length: 6.0),
    CableRodSpec(size: '1x120', diameter: 0.0127, rod: '5*1.5', length: 7.5),
    CableRodSpec(size: '1x150', diameter: 0.0141, rod: '6*1.2', length: 7.2),
    CableRodSpec(size: '1x185', diameter: 0.0158, rod: '6*1.5', length: 9.0),
    CableRodSpec(size: '1x240', diameter: 0.0182, rod: '8*1.2', length: 8.4),
    CableRodSpec(size: '1x300', diameter: 0.0206, rod: '7*1.5', length: 10.5),
    CableRodSpec(size: '1x400', diameter: 0.0232, rod: '8*1.2', length: 9.6),
    CableRodSpec(size: '1x500', diameter: 0.0266, rod: '8*1.5', length: 12.0),
  ];

  /// Rod diameter lookup by inch and meter
  static const Map<String, double> rodDiameters = {
    '3/4"': 0.0172,
    '5/8"': 0.0142,
  };

  /// Cable sizing constant K (for copper, from reference sheet)
  static const double cableSizingK = 220.0; // See calculation sheet, copper

  /// K1 constant for copper (from reference sheet)
  static const double k1Copper = 226.0; // See calculation sheet, copper

  /// Beta constant for copper (from reference sheet)
  static const double betaCopper = 234.5; // See calculation sheet, copper

  /// Returns the next available cable size (rounded up) in mm² as int, given a required area.
  static int nextCableSizeMm2(double requiredMm2) {
    // Extract all sizes as ints (e.g., '1x120' -> 120)
    final sizes = cableRodSpecs
        .map((spec) => int.tryParse(spec.size.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0)
        .where((v) => v > 0)
        .toList()
      ..sort();
    for (final s in sizes) {
      if (s >= requiredMm2.ceil()) return s;
    }
    // If required size is larger than any available, return the largest
    return sizes.isNotEmpty ? sizes.last : requiredMm2.ceil();
  }

  /// Returns the CableRodSpec for a given mm² size (as int), or null if not found.
  static CableRodSpec? specForMm2(int mm2) {
    final found = cableRodSpecs.where(
      (spec) => int.tryParse(spec.size.replaceAll(RegExp(r'[^0-9]'), '')) == mm2,
    );
    return found.isNotEmpty ? found.first : null;
  }

  // Add more constants as needed from the calculation sheet or standard
} 