import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import '../constants/company_constants.dart';

class PdfService {
  // Document state management
  static late PdfDocument _document;
  static late PdfPage _currentPage;
  static late PdfGraphics _graphics;
  static late Size _pageSize;
  static double _yPosition = 0;
  static int _pageNumber = 1;
  
  // Layout constants - redesigned for better spacing
  static const double _margin = 30.0;
  static const double _headerHeight = 80.0;
  static const double _footerHeight = 70.0;
  static const double _sectionTitleHeight = 32.0;
  static const double _sectionGap = 25.0;
  static const double _textLineHeight = 14.0;
  
  // Colors and styling
  static final PdfColor _primaryColor = PdfColor(25, 118, 210);
  static final PdfColor _headerBgColor = PdfColor(240, 248, 255);
  static final PdfColor _tableBorderColor = PdfColor(200, 200, 200);
  static final PdfColor _importantRowColor = PdfColor(255, 245, 157);
  static final PdfColor _whiteRowColor = PdfColor(255, 255, 255);
  static final PdfColor _lightGrayRowColor = PdfColor(248, 250, 252);
  
  // Typography - Using standard fonts for maximum compatibility
  static PdfFont get titleFont => PdfStandardFont(PdfFontFamily.helvetica, 16, style: PdfFontStyle.bold);
  static PdfFont get headerFont => PdfStandardFont(PdfFontFamily.helvetica, 12, style: PdfFontStyle.bold);
  static PdfFont get bodyFont => PdfStandardFont(PdfFontFamily.helvetica, 10);
  static PdfFont get tableHeaderFont => PdfStandardFont(PdfFontFamily.helvetica, 11, style: PdfFontStyle.bold);
  static PdfFont get tableDataFont => PdfStandardFont(PdfFontFamily.helvetica, 9);
  
  /// Initialize fonts - using standard fonts for maximum compatibility
  static Future<void> _initializeFonts() async {
    debugPrint('Using standard fonts for maximum compatibility');
  }
  
  /// Initialize a new document
  static Future<void> _initializeDocument() async {
    // Dispose existing document to prevent memory leaks and state persistence
    try {
      _document.dispose();
    } catch (e) {
      // Ignore disposal errors - may not be initialized yet
    }
    
    // Initialize fonts (now using standard fonts for reliability)
    await _initializeFonts();
    
    // Reset all static variables
    _document = PdfDocument();
    _yPosition = 0;
    _pageNumber = 1;
    _addNewPage();
  }
  
  /// Add a new page and reset position
  static void _addNewPage() {
    _currentPage = _document.pages.add();
    _graphics = _currentPage.graphics;
    _pageSize = _currentPage.getClientSize();
    _yPosition = _margin;
    _pageNumber = _document.pages.count;
  }
  
  /// Calculate available space on current page
  static double _getAvailableHeight() {
    return _pageSize.height - _yPosition - _footerHeight - _margin;
  }
  
  /// Check if we need a new page for the given content height
  static void _ensureSpace(double requiredHeight) {
    if (_getAvailableHeight() < requiredHeight) {
      _drawFooter();
      _addNewPage();
    }
  }
  
  /// Clean text for PDF compatibility with standard fonts
  static String _cleanText(dynamic text) {
    if (text == null) return 'N/A';
    
    String original = text.toString();
    String cleaned = original;
    
    // Log original text with character codes for debugging
    if (original.length < 200) {
      debugPrint('_cleanText input: "$original"');
      for (int i = 0; i < original.length; i++) {
        final char = original[i];
        final charCode = char.codeUnitAt(0);
        if (charCode > 127) {
          debugPrint('Found high Unicode character: "$char" (code: $charCode) at position $i');
        }
      }
    }
    
    // Remove or replace all non-ASCII characters that might cause issues
    // First handle common engineering symbols
    cleaned = cleaned.replaceAll('Ω', ' Ohm');
    cleaned = cleaned.replaceAll('ρ', 'rho');
    cleaned = cleaned.replaceAll('α', 'alpha');
    cleaned = cleaned.replaceAll('β', 'beta');
    cleaned = cleaned.replaceAll('γ', 'gamma');
    cleaned = cleaned.replaceAll('δ', 'delta');
    cleaned = cleaned.replaceAll('ε', 'epsilon');
    cleaned = cleaned.replaceAll('θ', 'theta');
    cleaned = cleaned.replaceAll('λ', 'lambda');
    cleaned = cleaned.replaceAll('μ', 'mu');
    cleaned = cleaned.replaceAll('π', 'pi');
    cleaned = cleaned.replaceAll('σ', 'sigma');
    cleaned = cleaned.replaceAll('τ', 'tau');
    cleaned = cleaned.replaceAll('φ', 'phi');
    cleaned = cleaned.replaceAll('χ', 'chi');
    cleaned = cleaned.replaceAll('ψ', 'psi');
    cleaned = cleaned.replaceAll('ω', 'omega');
    cleaned = cleaned.replaceAll('²', '^2');
    cleaned = cleaned.replaceAll('³', '^3');
    cleaned = cleaned.replaceAll('¹', '^1');
    cleaned = cleaned.replaceAll('⁰', '^0');
    cleaned = cleaned.replaceAll('⁴', '^4');
    cleaned = cleaned.replaceAll('⁵', '^5');
    cleaned = cleaned.replaceAll('⁶', '^6');
    cleaned = cleaned.replaceAll('⁷', '^7');
    cleaned = cleaned.replaceAll('⁸', '^8');
    cleaned = cleaned.replaceAll('⁹', '^9');
    cleaned = cleaned.replaceAll('°', ' deg');
    cleaned = cleaned.replaceAll('·', '.');
    cleaned = cleaned.replaceAll('×', 'x');
    cleaned = cleaned.replaceAll('÷', '/');
    cleaned = cleaned.replaceAll('±', '+/-');
    cleaned = cleaned.replaceAll('√', 'sqrt');
    cleaned = cleaned.replaceAll('≈', '~');
    cleaned = cleaned.replaceAll('≤', '<=');
    cleaned = cleaned.replaceAll('≥', '>=');
    cleaned = cleaned.replaceAll('≠', '!=');
    cleaned = cleaned.replaceAll('≡', '=');
    cleaned = cleaned.replaceAll('∞', 'infinity');
    cleaned = cleaned.replaceAll('∑', 'sum');
    cleaned = cleaned.replaceAll('∫', 'integral');
    cleaned = cleaned.replaceAll('∂', 'partial');
    cleaned = cleaned.replaceAll('∇', 'nabla');
    cleaned = cleaned.replaceAll('∆', 'delta');
    cleaned = cleaned.replaceAll('✓', 'YES');
    cleaned = cleaned.replaceAll('✗', 'NO');
    cleaned = cleaned.replaceAll('✔', 'YES');
    cleaned = cleaned.replaceAll('✘', 'NO');
    cleaned = cleaned.replaceAll('–', '-');
    cleaned = cleaned.replaceAll('—', '-');
    cleaned = cleaned.replaceAll(''', "'");
    cleaned = cleaned.replaceAll(''', "'");
    cleaned = cleaned.replaceAll('"', '"');
    cleaned = cleaned.replaceAll('"', '"');
    cleaned = cleaned.replaceAll('…', '...');
    
    // Remove any remaining high Unicode characters (above ASCII 127)
    // Use a more aggressive approach - replace with descriptive text
    cleaned = cleaned.replaceAll(RegExp(r'[^\x00-\x7F]'), '[SYMBOL]');
    
    final result = cleaned.trim();
    
    // Log the result if it changed
    if (result != original) {
      debugPrint('_cleanText result: "$result"');
    }
    
    return result;
  }
  
  /// Draw document header with logos and company info
  static Future<void> _drawDocumentHeader() async {
    _yPosition = _margin;
    
    // Load logos
    Uint8List? pisLogo;
    Uint8List? factoryLogo;
    try {
      final ByteData pisData = await rootBundle.load('assets/images/PIS PROFESSIONAL INTEGRATED SOLUTIONS.png');
      pisLogo = pisData.buffer.asUint8List();
      
      final ByteData factoryData = await rootBundle.load('assets/images/PIS FACTORY.png');
      factoryLogo = factoryData.buffer.asUint8List();
    } catch (e) {
      debugPrint('Logo loading failed: $e');
    }
    
    // Draw logos
    if (pisLogo != null) {
      final PdfBitmap leftLogo = PdfBitmap(pisLogo);
      _graphics.drawImage(leftLogo, Rect.fromLTWH(_margin, _yPosition, 90, 45));
    }
    
    if (factoryLogo != null) {
      final PdfBitmap rightLogo = PdfBitmap(factoryLogo);
      _graphics.drawImage(rightLogo, Rect.fromLTWH(_pageSize.width - _margin - 90, _yPosition, 90, 45));
    }
    
    // Company information (centered)
    final double centerStart = _pageSize.width / 2 - 150;
    
    // Debug prints to verify text content and fonts
    debugPrint('Drawing company name: "${CompanyConstants.companyName}"');
    debugPrint('Header font: $headerFont');
    
    _graphics.drawString(_cleanText(CompanyConstants.companyName), headerFont,
        bounds: Rect.fromLTWH(centerStart, _yPosition + 5, 300, 15),
        format: PdfStringFormat(alignment: PdfTextAlignment.center));
    
    _graphics.drawString(_cleanText(CompanyConstants.companyAddress), bodyFont,
        bounds: Rect.fromLTWH(centerStart, _yPosition + 22, 300, 12),
        format: PdfStringFormat(alignment: PdfTextAlignment.center));
    
    _graphics.drawString(_cleanText('Tel: ${CompanyConstants.companyPhone}'), bodyFont,
        bounds: Rect.fromLTWH(centerStart, _yPosition + 36, 300, 12),
        format: PdfStringFormat(alignment: PdfTextAlignment.center));
    
    // Separator line
    _graphics.drawLine(
      PdfPen(_primaryColor)..width = 2,
      Offset(_margin, _yPosition + 60),
      Offset(_pageSize.width - _margin, _yPosition + 60)
    );
    
    _yPosition += _headerHeight;
  }
  
  /// Draw main report title
  static void _drawReportTitle(String title) {
    _ensureSpace(50);
    
    // Clean the title text before drawing
    final String cleanTitle = _cleanText(title);
    debugPrint('Drawing title: "$cleanTitle"');
    debugPrint('Title font: $titleFont');
    
    final double titleWidth = _pageSize.width - (_margin * 2);
    final Rect titleRect = Rect.fromLTWH(_margin, _yPosition, titleWidth, 40);
    
    // Title background
    _graphics.drawRectangle(
      brush: PdfSolidBrush(_headerBgColor),
      pen: PdfPen(_primaryColor)..width = 2,
      bounds: titleRect
    );
    
    // Title text
    _graphics.drawString(cleanTitle, titleFont,
        brush: PdfSolidBrush(_primaryColor),
        bounds: Rect.fromLTWH(_margin + 15, _yPosition + 12, titleWidth - 30, 20),
        format: PdfStringFormat(alignment: PdfTextAlignment.center));
    
    _yPosition += 50;
  }
  
  /// Draw section header
  static void _drawSectionHeader(String title) {
    _ensureSpace(_sectionTitleHeight + 15); // Increased space requirement
    
    // Clean the title text before drawing
    final String cleanTitle = _cleanText(title);
    debugPrint('Drawing section header: "$cleanTitle"');
    
    final double sectionWidth = _pageSize.width - (_margin * 2);
    final Rect sectionRect = Rect.fromLTWH(_margin, _yPosition, sectionWidth, _sectionTitleHeight);
    
    // Section background
    _graphics.drawRectangle(
      brush: PdfSolidBrush(_headerBgColor),
      pen: PdfPen(_primaryColor)..width = 1.5,
      bounds: sectionRect
    );
    
    // Section title
    _graphics.drawString(cleanTitle, headerFont,
        brush: PdfSolidBrush(_primaryColor),
        bounds: Rect.fromLTWH(_margin + 12, _yPosition + 8, sectionWidth - 24, 16));
    
    _yPosition += _sectionTitleHeight + 10; // Increased gap after header
  }
  
  /// Draw section header specifically for tables (ensures they stay together)
  static void _drawSectionHeaderWithTable(String title, List<String> headers, List<List<String>> data, {List<int>? importantRows}) {
    // Calculate minimum space needed for header + first few table rows
    final double headerSpace = _sectionTitleHeight + 10;
    final double tableHeaderSpace = 28.0;
    final double firstRowSpace = 22.0;
    final double minimumSpace = headerSpace + tableHeaderSpace + (firstRowSpace * 2); // Header + table header + 2 rows minimum
    
    // Ensure we have enough space for header and at least start of table
    _ensureSpace(minimumSpace);
    
    // Draw the section header
    final double sectionWidth = _pageSize.width - (_margin * 2);
    final Rect sectionRect = Rect.fromLTWH(_margin, _yPosition, sectionWidth, _sectionTitleHeight);
    
    // Section background
    _graphics.drawRectangle(
      brush: PdfSolidBrush(_headerBgColor),
      pen: PdfPen(_primaryColor)..width = 1.5,
      bounds: sectionRect
    );
    
    // Section title
    _graphics.drawString(title, headerFont,
        brush: PdfSolidBrush(_primaryColor),
        bounds: Rect.fromLTWH(_margin + 12, _yPosition + 8, sectionWidth - 24, 16));
    
    _yPosition += _sectionTitleHeight + 10; // Gap after header
    
    // Now draw the table immediately
    _drawTable(headers: headers, data: data, importantRows: importantRows);
  }
  
  /// Draw technical notes section on a new page
  static void _drawTechnicalNotesSection(String content) {
    // Force a new page for technical notes
    _drawFooter();
    _addNewPage();
    
    // Draw the section header
    _drawSectionHeader('TECHNICAL NOTES & RECOMMENDATIONS');
    
    // Clean the content before drawing
    final String cleanContent = _cleanText(content);
    debugPrint('Drawing technical notes - cleaned length: ${cleanContent.length}');
    
    // Draw the content
    _drawTextContent(cleanContent);
  }
  
  /// Draw text content with proper wrapping
  static void _drawTextContent(String text, {PdfFont? font, double? maxWidth}) {
    font ??= bodyFont;
    maxWidth ??= _pageSize.width - (_margin * 2);
    
    // CRITICAL: Clean the entire text first before any processing
    final String cleanedText = _cleanText(text);
    debugPrint('Drawing text content - Original: "${text.length > 100 ? '${text.substring(0, 100)}...' : text}"');
    debugPrint('Drawing text content - Cleaned: "${cleanedText.length > 100 ? '${cleanedText.substring(0, 100)}...' : cleanedText}"');
    
    // Split text into paragraphs
    final paragraphs = cleanedText.split('\n');
    
    for (String paragraph in paragraphs) {
      if (paragraph.trim().isEmpty) {
        _yPosition += _textLineHeight / 2;
        continue;
      }
      
      // Double clean each paragraph for safety
      final String cleanParagraph = _cleanText(paragraph);
      debugPrint('Processing paragraph: "$cleanParagraph"');
      
      // Calculate required height for this paragraph
      final Size textSize = font.measureString(cleanParagraph);
      final double linesNeeded = (textSize.width / maxWidth).ceil().toDouble();
      final double requiredHeight = linesNeeded * _textLineHeight + 5;
      
      _ensureSpace(requiredHeight);
      
      // Draw paragraph
      _graphics.drawString(cleanParagraph, font,
          bounds: Rect.fromLTWH(_margin, _yPosition, maxWidth, requiredHeight),
          format: PdfStringFormat(
            alignment: PdfTextAlignment.justify,
            lineAlignment: PdfVerticalAlignment.top
          ));
      
      _yPosition += requiredHeight;
    }
    
    _yPosition += _sectionGap; // Gap after text section
  }
  
  /// Create and draw a professional table
  static void _drawTable({
    required List<String> headers,
    required List<List<String>> data,
    List<int>? importantRows,
  }) {
    // Calculate table dimensions
    final double tableWidth = _pageSize.width - (_margin * 2);
    final double columnWidth = tableWidth / headers.length;
    final double headerRowHeight = 28.0;
    final double dataRowHeight = 22.0;
    final double totalTableHeight = headerRowHeight + (data.length * dataRowHeight) + 15;
    
    // Check if table fits on current page
    _ensureSpace(totalTableHeight);
    
    // Create table
    final PdfGrid grid = PdfGrid();
    grid.columns.add(count: headers.length);
    
    // Set column widths
    for (int i = 0; i < headers.length; i++) {
      grid.columns[i].width = columnWidth;
    }
    
    // Configure table style
    grid.style = PdfGridStyle(
      cellPadding: PdfPaddings(left: 8, top: 6, right: 8, bottom: 6),
      backgroundBrush: PdfSolidBrush(_whiteRowColor),
      textBrush: PdfSolidBrush(PdfColor(0, 0, 0)),
      font: tableDataFont,
    );
    
    // Add header row
    grid.headers.add(1);
    final PdfGridRow headerRow = grid.headers[0];
    for (int i = 0; i < headers.length; i++) {
      headerRow.cells[i].value = _cleanText(headers[i]);
      headerRow.cells[i].style = PdfGridCellStyle(
        backgroundBrush: PdfSolidBrush(_primaryColor),
        textBrush: PdfSolidBrush(PdfColor(255, 255, 255)),
        font: tableHeaderFont,
        borders: PdfBorders(
          left: PdfPen(_tableBorderColor),
          top: PdfPen(_tableBorderColor),
          right: PdfPen(_tableBorderColor),
          bottom: PdfPen(_tableBorderColor),
        ),
      );
    }
    
    // Add data rows
    for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
      final PdfGridRow row = grid.rows.add();
      final bool isImportantRow = importantRows?.contains(rowIndex) ?? false;
      
      for (int colIndex = 0; colIndex < data[rowIndex].length && colIndex < headers.length; colIndex++) {
        row.cells[colIndex].value = _cleanText(data[rowIndex][colIndex]);
        
        // Determine row color
        PdfColor bgColor;
        PdfColor textColor = PdfColor(0, 0, 0);
        PdfFont font = tableDataFont;
        
        if (isImportantRow) {
          // Important rows get yellow background
          bgColor = _importantRowColor;
          font = tableHeaderFont; // Make important rows bold
        } else {
          // Alternate between white and light gray for better readability
          bgColor = rowIndex % 2 == 0 ? _whiteRowColor : _lightGrayRowColor;
        }
        
        row.cells[colIndex].style = PdfGridCellStyle(
          backgroundBrush: PdfSolidBrush(bgColor),
          textBrush: PdfSolidBrush(textColor),
          font: font,
          borders: PdfBorders(
            left: PdfPen(_tableBorderColor),
            top: PdfPen(_tableBorderColor),
            right: PdfPen(_tableBorderColor),
            bottom: PdfPen(_tableBorderColor),
          ),
        );
      }
    }
    
    // Draw the table
    final PdfLayoutResult? result = grid.draw(
      graphics: _graphics,
      bounds: Rect.fromLTWH(_margin, _yPosition, tableWidth, _getAvailableHeight()),
      format: PdfLayoutFormat(
        layoutType: PdfLayoutType.paginate,
        breakType: PdfLayoutBreakType.fitPage,
      ),
    );
    
    // Update position after table
    if (result != null) {
      _yPosition = result.bounds.bottom + _sectionGap;
      
      // Update page reference if table spanned multiple pages
      if (result.page != _currentPage) {
        _currentPage = result.page;
        _graphics = _currentPage.graphics;
        _pageNumber = _document.pages.indexOf(_currentPage) + 1;
      }
    } else {
      _yPosition += totalTableHeight + _sectionGap;
    }
  }
  
  /// Draw colored text (for status messages)
  static void _drawColoredText(String text, Color color, {PdfFont? font}) {
    font ??= headerFont;
    final double requiredHeight = 25.0;
    
    _ensureSpace(requiredHeight);
    
    _graphics.drawString(text, font,
        brush: PdfSolidBrush(PdfColor(
          (color.r * 255).round(),
          (color.g * 255).round(),
          (color.b * 255).round()
        )),
        bounds: Rect.fromLTWH(_margin, _yPosition, _pageSize.width - (_margin * 2), 20));
    
    _yPosition += requiredHeight;
  }
  
  /// Draw footer on current page
  static void _drawFooter() {
    final double footerY = _pageSize.height - _footerHeight;
    
    // Footer background
    _graphics.drawRectangle(
      brush: PdfSolidBrush(_primaryColor),
      bounds: Rect.fromLTWH(0, footerY, _pageSize.width, _footerHeight)
    );
    
    final PdfBrush whiteBrush = PdfSolidBrush(PdfColor(255, 255, 255));
    
    // Company info
    _graphics.drawString(
      '${CompanyConstants.reportFooter} | Generated by PIS Core',
      bodyFont,
      brush: whiteBrush,
      bounds: Rect.fromLTWH(_margin, footerY + 10, _pageSize.width - (_margin * 2), 12),
      format: PdfStringFormat(alignment: PdfTextAlignment.center)
    );
    
    _graphics.drawString(
      '${CompanyConstants.companyAddress} | Tel: ${CompanyConstants.companyPhone}',
      bodyFont,
      brush: whiteBrush,
      bounds: Rect.fromLTWH(_margin, footerY + 25, _pageSize.width - (_margin * 2), 12),
      format: PdfStringFormat(alignment: PdfTextAlignment.center)
    );
    
    // Date and page number
    final String timestamp = DateTime.now().toString().split('.')[0];
    _graphics.drawString(
      'Generated: $timestamp | Page $_pageNumber',
      bodyFont,
      brush: whiteBrush,
      bounds: Rect.fromLTWH(_pageSize.width - 250, footerY + 45, 230, 12),
      format: PdfStringFormat(alignment: PdfTextAlignment.right)
    );
  }
  
  /// Save document to file
  static Future<File> _saveDocument(String filename) async {
    // Draw footer on last page
    _drawFooter();
    
    final List<int> bytes = await _document.save();
    _document.dispose();
    
    if (kIsWeb) {
      // On web, we can't save to file system - throw exception for now
      // In a real web app, you'd trigger a download here
      throw UnsupportedError('File saving is not supported on web platform. Use native platforms (iOS, Android, macOS, Windows, Linux) for PDF generation.');
    } else {
      // On native platforms, save to documents directory
      final Directory directory = await getApplicationDocumentsDirectory();
      final String path = '${directory.path}/$filename';
      final File file = File(path);
      await file.writeAsBytes(bytes);
      return file;
    }
  }
  
  /// Generate Earthing System Calculation Report
  static Future<File> generateEarthingReport({
    required String projectName,
    required String location,
    required String date,
    required Map<String, dynamic> inputs,
    required Map<String, dynamic> results,
    String? notes,
  }) async {
    await _initializeDocument();
    
    // Document header
    await _drawDocumentHeader();
    
    // Report title
    _drawReportTitle('EARTHING SYSTEM CALCULATION REPORT');
    
    // Project Information Section
    _drawSectionHeaderWithTable(
      'PROJECT INFORMATION',
      ['Parameter', 'Value'],
      [
        ['Project Name', _cleanText(inputs['Project Name'] ?? projectName)],
        ['Main Constructor', _cleanText(inputs['Main Constructor'] ?? 'Not specified')],
        ['Report Date', _cleanText(date)],
        ['Standard', 'BS 7430:2011'],
      ],
      importantRows: [0, 2], // Highlight Project Name and Report Date
    );
    
    // Executive Summary Section
    _drawSectionHeader('EXECUTIVE SUMMARY');
    
    final bool isCompliant = results['Compliance Status'] == 'COMPLIANT';
    _drawColoredText(
      'COMPLIANCE STATUS: ${results['Compliance Status']}',
      isCompliant ? Colors.green : Colors.red,
    );
    
    final targetResistance = inputs['Target Resistance'] ?? results['Target Resistance'] ?? results['target_resistance'] ?? 'N/A';
    _drawTable(
      headers: ['Parameter', 'Value'],
      data: [
        ['Total Resistance', _cleanText(results['Total Resistance'] ?? results['total_resistance'])],
        ['Target Resistance', targetResistance.toString() == 'null' ? 'N/A' : _cleanText(targetResistance)],
        ['Rod Resistance', _cleanText(results['Rod Resistance'] ?? results['rod_resistance'])],
        ['Cable Resistance', _cleanText(results['Cable Resistance'] ?? results['cable_resistance'])],
      ],
      importantRows: [0, 1], // Highlight Total Resistance and Target Resistance
    );
    
        // Input Parameters Section
    final List<List<String>> inputData = [];
    final List<int> importantInputRows = [];
    
    inputs.forEach((key, value) {
      // Skip project information fields as they're shown in PROJECT INFORMATION section
      if (key == 'Project Name' || key == 'Main Constructor') {
        return;
      }

      if (value != null && value.toString().isNotEmpty && value.toString() != 'null') {
        inputData.add([key, _cleanText(value)]);
        // Highlight important parameters
        if (key.toLowerCase().contains('target resistance') ||
            key.toLowerCase().contains('soil resistivity') ||
            key.toLowerCase().contains('rod length') ||
            key.toLowerCase().contains('cable length')) {
          importantInputRows.add(inputData.length - 1);
        }
      }
    });

    if (inputData.isNotEmpty) {
      _drawSectionHeaderWithTable(
        'INPUT PARAMETERS',
        ['Parameter', 'Value'],
        inputData,
        importantRows: importantInputRows,
      );
    } else {
      _drawSectionHeader('INPUT PARAMETERS');
      _drawTextContent('No input parameters available.');
    }
    
        // Calculation Results Section
    final List<List<String>> resultData = [];
    final List<int> importantResultRows = [];
    
    results.forEach((key, value) {
      if (value != null && value.toString().isNotEmpty && value.toString() != 'null') {
        String displayValue = _cleanText(value);
        if (key.contains('OVERRIDDEN') || displayValue.contains('OVERRIDDEN')) {
          displayValue = '${displayValue.replaceAll(' (OVERRIDDEN)', '')} (MANUAL OVERRIDE)';
        }
        resultData.add([key, displayValue]);
        // Highlight important results
        if (key.toLowerCase().contains('total resistance') ||
            key.toLowerCase().contains('compliance status') ||
            key.toLowerCase().contains('total_resistance') ||
            key.toLowerCase().contains('rod resistance') ||
            key.toLowerCase().contains('cable resistance')) {
          importantResultRows.add(resultData.length - 1);
        }
      }
    });

    if (resultData.isNotEmpty) {
      _drawSectionHeaderWithTable(
        'CALCULATION RESULTS',
        ['Result Parameter', 'Calculated Value'],
        resultData,
        importantRows: importantResultRows,
      );
    } else {
      _drawSectionHeader('CALCULATION RESULTS');
      _drawTextContent('No calculation results available.');
    }
    
    // Technical Notes Section - Always on new page
    if (notes != null && notes.isNotEmpty) {
      _drawTechnicalNotesSection(_cleanText(notes));
    } else {
      _drawTechnicalNotesSection('''CALCULATION METHODOLOGY:
This earthing system calculation follows BS 7430:2011 standard.

Rod Resistance Calculation:
- Method: Single Rod
- Formula: Based on BS 7430 Clause 9.5

Cable Sizing:
- Adiabatic equation per BS 7430 Clause 9.7
- Material: Copper
- Temperature rise: 30.0 degC to 450.0 degC

SAFETY CONSIDERATIONS:
- All calculations include appropriate safety factors
- Installation must comply with local electrical codes
- Regular testing and maintenance required

RECOMMENDATIONS:
- Design meets target resistance requirements
- Proceed with installation as calculated''');
    }
    
    return await _saveDocument('PIS_Earthing_Report_${DateTime.now().millisecondsSinceEpoch}.pdf');
  }
  
  /// Generate Lightning Protection Report
  static Future<File> generateLightningProtectionReport({
    required String projectName,
    required String location,
    required String date,
    required Map<String, dynamic> inputs,
    required Map<String, dynamic> results,
    String? notes,
  }) async {
    await _initializeDocument();

    // Document header
    await _drawDocumentHeader();

    // Report title
    _drawReportTitle('LIGHTNING PROTECTION SYSTEM RISK ASSESSMENT REPORT');

    // Project Information
    _drawSectionHeaderWithTable(
      'PROJECT INFORMATION',
      ['Parameter', 'Value'],
      [
        ['Project Name', _cleanText(inputs['Project Name'] ?? projectName)],
        ['Main Constructor', _cleanText(inputs['Main Constructor'] ?? 'Not specified')],
        ['Report Date', _cleanText(date)],
        ['Standard', 'NFPA 780:2020'],
      ],
      importantRows: [0, 2], // Highlight Project Name and Report Date
    );

    // Executive Summary
    _drawSectionHeader('EXECUTIVE SUMMARY');
    final riskLevel = results['Risk Level'] ?? '0.0';
    final lpsRecommendedValue = results['LPS Recommended'];
    final lpsRecommended = lpsRecommendedValue is bool
        ? lpsRecommendedValue
        : (lpsRecommendedValue?.toString().toUpperCase() == 'YES');
    final protectionLevel = results['Protection Level'] ?? 'N/A';

    final summaryData = [
      ['Risk Level', _cleanText(riskLevel.toString())],
      ['LPS Required', lpsRecommended ? 'YES' : 'NO'],
      ['Protection Level', _cleanText(protectionLevel.toString())],
      ['Compliance Status', lpsRecommended ? 'PROTECTION REQUIRED' : 'ACCEPTABLE RISK'],
    ];

    _drawSectionHeaderWithTable(
      'RISK ASSESSMENT SUMMARY',
      ['Parameter', 'Value'],
      summaryData,
      importantRows: [0, 1], // Highlight Risk Level and LPS Required
    );

    // Input Parameters Section (excluding project info which is in PROJECT INFORMATION)
    final List<List<String>> inputData = [];
    final List<int> importantInputRows = [];

    inputs.forEach((key, value) {
      // Skip project information fields as they're shown in PROJECT INFORMATION section
      if (key == 'Project Name' || key == 'Main Constructor') {
        return;
      }

      if (value != null && value.toString().isNotEmpty && value.toString() != 'null') {
        inputData.add([key, _cleanText(value)]);
        // Highlight important parameters
        if (key.toLowerCase().contains('building') ||
            key.toLowerCase().contains('flash density') ||
            key.toLowerCase().contains('location factor')) {
          importantInputRows.add(inputData.length - 1);
        }
      }
    });

    if (inputData.isNotEmpty) {
      _drawSectionHeaderWithTable(
        'INPUT PARAMETERS',
        ['Parameter', 'Value'],
        inputData,
        importantRows: importantInputRows,
      );
    } else {
      _drawSectionHeader('INPUT PARAMETERS');
      _drawTextContent('No input parameters available.');
    }

    // Calculation Results Section - Force new page to avoid overlap
    _drawFooter();
    _addNewPage();

    final List<List<String>> resultData = [];
    final List<int> importantResultRows = [];

    results.forEach((key, value) {
      if (value != null && value.toString().isNotEmpty && value.toString() != 'null') {
        resultData.add([key, _cleanText(value.toString())]);

        // Mark important results
        if (key.toLowerCase().contains('risk level') ||
            key.toLowerCase().contains('lps recommended') ||
            key.toLowerCase().contains('protection level')) {
          importantResultRows.add(resultData.length - 1);
        }
      }
    });

    if (resultData.isNotEmpty) {
      _drawSectionHeaderWithTable(
        'CALCULATION RESULTS',
        ['Result Parameter', 'Calculated Value'],
        resultData,
        importantRows: importantResultRows,
      );
    }

    // Add lightning protection images (flash rate and area calculation)
    await _drawLightningProtectionImages();

    // Technical Notes - always on a new page, always with visible header
    if (notes != null && notes.isNotEmpty) {
      _drawTechnicalNotesSection(_cleanText(notes));
    } else {
      _drawTechnicalNotesSection(_buildDefaultLightningNotes());
    }

    return await _saveDocument('PIS_Lightning_Protection_Report_${DateTime.now().millisecondsSinceEpoch}.pdf');
  }


  

  
  /// Share generated report
  static Future<void> shareReport(File file, String title) async {
    if (kIsWeb) {
      throw UnsupportedError('File sharing is not supported on web platform.');
    } else {
      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(file.path)],
          text: title,
          subject: title,
        ),
      );
    }
  }
  
  /// Calculate representative soil resistivity from Wenner measurements
  /// This method can be used by earthing calculations to get the soil resistivity value
  static double calculateRepresentativeSoilResistivity(List zones) {
    List<double> allResistivityValues = [];
    
    for (int zoneIndex = 0; zoneIndex < zones.length; zoneIndex++) {
      final dynamic zone = zones[zoneIndex];
      
      List<double> rReadings;
      
      if (zone is Map<String, dynamic>) {
        rReadings = zone['rReadings'] is List
            ? List<double>.from((zone['rReadings'] as List).map((e) => e?.toDouble() ?? 0.0))
            : [0.0, 0.0, 0.0, 0.0];
      } else {
        try {
          rReadings = zone?.rReadings is List
              ? List<double>.from(zone.rReadings.map((e) => e?.toDouble() ?? 0.0))
              : [0.0, 0.0, 0.0, 0.0];
        } catch (e) {
          rReadings = [0.0, 0.0, 0.0, 0.0];
        }
      }
      
      final spacings = [1, 2, 3, 4];
      
      for (int i = 0; i < 4; i++) {
        final double resistance = i < rReadings.length ? rReadings[i] : 0.0;
        final double resistivity = resistance > 0 ? 2 * 3.14159 * spacings[i] * resistance : 0.0;
        
        if (resistivity > 0) {
          allResistivityValues.add(resistivity);
        }
      }
    }
    
    double representativeValue = allResistivityValues.isNotEmpty 
        ? allResistivityValues.reduce((a, b) => a + b) / allResistivityValues.length 
        : 25.8; // Default fallback value if no measurements
    
    debugPrint('Calculated representative soil resistivity: ${representativeValue.toStringAsFixed(2)} Ohm.m from ${allResistivityValues.length} measurements');
    return representativeValue;
  }
  
  /// Get soil resistivity zones breakdown for detailed analysis
  static Map<String, dynamic> getSoilResistivityAnalysis(List zones) {
    List<double> allResistivityValues = [];
    List<Map<String, dynamic>> zoneResults = [];
    
    for (int zoneIndex = 0; zoneIndex < zones.length; zoneIndex++) {
      final dynamic zone = zones[zoneIndex];
      
      String zoneAlias;
      List<double> rReadings;
      
      if (zone is Map<String, dynamic>) {
        zoneAlias = zone['alias']?.toString() ?? 'Zone ${zoneIndex + 1}';
        rReadings = zone['rReadings'] is List
            ? List<double>.from((zone['rReadings'] as List).map((e) => e?.toDouble() ?? 0.0))
            : [0.0, 0.0, 0.0, 0.0];
      } else {
        try {
          zoneAlias = zone?.alias?.toString() ?? 'Zone ${zoneIndex + 1}';
          rReadings = zone?.rReadings is List
              ? List<double>.from(zone.rReadings.map((e) => e?.toDouble() ?? 0.0))
              : [0.0, 0.0, 0.0, 0.0];
        } catch (e) {
          zoneAlias = 'Zone ${zoneIndex + 1}';
          rReadings = [0.0, 0.0, 0.0, 0.0];
        }
      }
      
      final spacings = [1, 2, 3, 4];
      List<double> zoneResistivityValues = [];
      
      for (int i = 0; i < 4; i++) {
        final double resistance = i < rReadings.length ? rReadings[i] : 0.0;
        final double resistivity = resistance > 0 ? 2 * 3.14159 * spacings[i] * resistance : 0.0;
        
        if (resistivity > 0) {
          zoneResistivityValues.add(resistivity);
          allResistivityValues.add(resistivity);
        }
      }
      
      double zoneAverage = zoneResistivityValues.isNotEmpty 
          ? zoneResistivityValues.reduce((a, b) => a + b) / zoneResistivityValues.length 
          : 0.0;
      
      zoneResults.add({
        'alias': zoneAlias,
        'average': zoneAverage,
        'values': zoneResistivityValues,
        'validMeasurements': zoneResistivityValues.length,
      });
    }
    
    double siteAverage = allResistivityValues.isNotEmpty 
        ? allResistivityValues.reduce((a, b) => a + b) / allResistivityValues.length 
        : 25.8; // Default fallback
    
    return {
      'representativeValue': siteAverage,
      'totalMeasurements': allResistivityValues.length,
      'zones': zoneResults,
      'allValues': allResistivityValues,
      'isValid': allResistivityValues.isNotEmpty,
    };
  }

  /// Draw lightning protection images (flash rate and area calculation)
  static Future<void> _drawLightningProtectionImages() async {
    try {
      // --- FLASH RATE IMAGE SECTION ---
      // Ensure enough space for header + image + caption
      const double flashHeaderHeight = _sectionTitleHeight + 10;
      const double flashImageHeight = 180.0; // Estimate for aspect ratio
      const double flashCaptionHeight = 40.0;
      _ensureSpace(flashHeaderHeight + flashImageHeight + flashCaptionHeight + 20);

      // Draw section header (always visible, never hidden)
      _drawSectionHeader('GLOBAL LIGHTNING FLASH RATE DISTRIBUTION');

      // Draw image centered
      final flashRateImageData = await rootBundle.load('assets/images/flash rate.png');
      final flashRateImage = PdfBitmap(flashRateImageData.buffer.asUint8List());
      final flashRateImageWidth = _pageSize.width - 80;
      final flashRateImageHeight = flashRateImageWidth * 0.6;
      _graphics.drawImage(
        flashRateImage,
        Rect.fromLTWH(40, _yPosition, flashRateImageWidth, flashRateImageHeight),
      );
      _yPosition += flashRateImageHeight + 10;

      // Draw caption directly below image, grouped visually
      _drawTextContent(
        'Global distribution of lightning flash density (fl/km^2/year) showing regional variations. This data is used to determine the appropriate flash density (Ng) value for the project location.'
      );
      _yPosition += 10;

      // --- AREA CALCULATION IMAGE SECTION ---
      // Ensure enough space for header + side-by-side layout
      const double areaHeaderHeight = _sectionTitleHeight + 10;
      const double areaSectionHeight = 150.0; // Height for side-by-side layout
      _ensureSpace(areaHeaderHeight + areaSectionHeight + 20);

      // Draw section header (always visible, never hidden)
      _drawSectionHeader('COLLECTIVE AREA CALCULATION METHOD');

      // Side-by-side layout: text on left, image on right
      final areaCalcImageData = await rootBundle.load('assets/images/Area Calculation.png');
      final areaCalcImage = PdfBitmap(areaCalcImageData.buffer.asUint8List());

      // Layout constants
      final double contentMargin = 40;
      final double availableWidth = _pageSize.width - (contentMargin * 2);
      final double imageWidth = availableWidth * 0.42; // 42% for image
      final double textWidth = availableWidth * 0.52;  // 52% for text, with 6% gap
      final double gap = availableWidth * 0.06;
      final double imageHeight = imageWidth * 0.4; // Maintain aspect ratio
      final double imageX = contentMargin + textWidth + gap;
      final double imageY = _yPosition;
      final double textX = contentMargin;
      final double textY = _yPosition;

      // Draw text on left
      final String captionText =
        'Collective area calculation for rectangular structures according to NFPA 780.\n\n'
        'The collective area (Ac) represents the equivalent area that attracts lightning strikes to the structure.'
        '\n\nFormula: Ac = LW + 2H(L+W) + piH^2'
        '\n\nWhere:'
        '\nL = Building length'
        '\nW = Building width'
        '\nH = Building height';

      final PdfStringFormat textFormat = PdfStringFormat(
        alignment: PdfTextAlignment.left,
        lineAlignment: PdfVerticalAlignment.top,
        wordWrap: PdfWordWrapType.word,
      );
      final Size textSize = bodyFont.measureString(_cleanText(captionText), format: textFormat);
      // Add a buffer to the text box height to ensure all lines are visible
      final double textBoxHeight = (textSize.height + 30) > imageHeight ? (textSize.height + 30) : imageHeight + 10;
      _graphics.drawString(
        _cleanText(captionText),
        bodyFont,
        bounds: Rect.fromLTWH(textX, textY, textWidth, textBoxHeight),
        format: textFormat,
      );

      // Draw image on right
      _graphics.drawImage(
        areaCalcImage,
        Rect.fromLTWH(imageX, imageY, imageWidth, imageHeight),
      );

      // Advance position by the maximum of image height or text height (with buffer)
      final double maxHeight = textBoxHeight;
      _yPosition += maxHeight + 20;
    } catch (e) {
      debugPrint('Error loading lightning protection images: $e');
      // Continue without images if they fail to load
      _drawTextContent('Lightning protection reference images could not be loaded.');
    }
  }

  /// Build default technical notes for lightning protection reports
  static String _buildDefaultLightningNotes() {
    return '''LIGHTNING PROTECTION RISK ASSESSMENT METHODOLOGY:
This lightning protection risk assessment follows NFPA 780:2020 standard.

RISK CALCULATION:
- Expected Strike Frequency (Ne) = Ng × Ac × C1
- Tolerable Strike Frequency (Nt) = 1 / (C2 × C3 × C4 × C5)
- Risk Level = (Ne / Nt) × 100%

COEFFICIENT DEFINITIONS:
C1 - Location Factor: Accounts for structure location and surroundings
C2 - Construction Coefficient: Based on structure and roof materials
C3 - Contents Coefficient: Reflects value and nature of structure contents
C4 - Occupancy Coefficient: Considers number of people and evacuation difficulty
C5 - Consequence Coefficient: Addresses consequences of lightning damage

PROTECTION LEVELS:
Level I: Highest protection (99% efficiency)
Level II: High protection (97% efficiency)
Level III: Standard protection (91% efficiency)
Level IV: Basic protection (84% efficiency)

COMPLIANCE REQUIREMENTS:
- Risk level <= 100% indicates acceptable risk
- Risk level > 100% requires lightning protection system
- Protection level determined by risk assessment results

SAFETY CONSIDERATIONS:
- All installations must comply with local electrical codes
- Regular inspection and maintenance required
- Professional installation recommended
- Consider surge protection devices for sensitive equipment

RECOMMENDATIONS:
- Implement recommended protection measures
- Consider seasonal lightning activity variations
- Review assessment if building use or contents change significantly''';
  }

  /// Dispose resources to prevent memory leaks
  static void dispose() {
    try {
      _document.dispose();
    } catch (e) {
      // Ignore disposal errors - may not be initialized yet
    }
  }
} 