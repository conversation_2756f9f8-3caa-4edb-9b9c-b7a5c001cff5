import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';

class ReportToPdfService {
  /// Capture a widget as an image and convert to PDF
  static Future<File> captureWidgetAsPdf({
    required GlobalKey repaintBoundaryKey,
    required String filename,
  }) async {
    try {
      // Get the render object
      final RenderRepaintBoundary boundary = repaintBoundaryKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;

      // Capture as image with high quality
      final ui.Image image = await boundary.toImage(pixelRatio: 2.5);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        throw Exception('Failed to capture widget as image');
      }

      final Uint8List imageBytes = byteData.buffer.asUint8List();

      // Create PDF document
      final pdf = pw.Document();

      // Convert image to PDF image
      final pdfImage = pw.MemoryImage(imageBytes);



      // A4 page width but custom height to fit content
      const double pageWidth = 595.0; // A4 width in points
      const double margin = 20.0; // Small margin

      final double availableWidth = pageWidth - (2 * margin);

      // Get actual image dimensions
      final double imageWidth = image.width.toDouble();
      final double imageHeight = image.height.toDouble();

      // Calculate the scale factor to fit width
      final double scaleFactor = availableWidth / imageWidth;
      final double scaledImageHeight = imageHeight * scaleFactor;

      // Create a custom page format with A4 width but height to fit content
      final double customPageHeight = scaledImageHeight + (2 * margin);
      final PdfPageFormat customPageFormat = PdfPageFormat(
        pageWidth,
        customPageHeight,
        marginAll: margin,
      );

      // Add single page with custom height to fit all content
      pdf.addPage(
        pw.Page(
          pageFormat: customPageFormat,
          margin: const pw.EdgeInsets.all(margin),
          build: (pw.Context context) {
            return pw.Image(
              pdfImage,
              width: availableWidth,
              fit: pw.BoxFit.fitWidth,
              alignment: pw.Alignment.topCenter,
            );
          },
        ),
      );

      // Save PDF to file
      final Directory directory = await getApplicationDocumentsDirectory();
      final String pisCorePath = '${directory.path}/PIS Core';
      final Directory pisCoreDir = Directory(pisCorePath);
      
      // Create PIS Core directory if it doesn't exist
      if (!await pisCoreDir.exists()) {
        await pisCoreDir.create(recursive: true);
      }
      
      final String filePath = '$pisCorePath/$filename';
      final File file = File(filePath);
      await file.writeAsBytes(await pdf.save());
      
      return file;
    } catch (e) {
      throw Exception('Failed to generate PDF from widget: $e');
    }
  }

  /// Capture multiple widgets as images and convert to multi-page PDF
  static Future<File> captureMultipleWidgetsAsPdf({
    required List<GlobalKey> repaintBoundaryKeys,
    required String filename,
  }) async {
    try {
      final pdf = pw.Document();
      
      for (int i = 0; i < repaintBoundaryKeys.length; i++) {
        final GlobalKey key = repaintBoundaryKeys[i];
        
        // Get the render object
        final RenderRepaintBoundary boundary = key.currentContext!
            .findRenderObject() as RenderRepaintBoundary;
        
        // Capture as image with high quality
        final ui.Image image = await boundary.toImage(pixelRatio: 2.5);
        final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

        if (byteData == null) {
          throw Exception('Failed to capture widget ${i + 1} as image');
        }

        final Uint8List imageBytes = byteData.buffer.asUint8List();
        final pdfImage = pw.MemoryImage(imageBytes);

        // A4 page width but custom height to fit content
        const double pageWidth = 595.0; // A4 width in points
        const double margin = 20.0; // Small margin

        final double availableWidth = pageWidth - (2 * margin);

        // Get actual image dimensions
        final double imageWidth = image.width.toDouble();
        final double imageHeight = image.height.toDouble();

        // Calculate the scale factor to fit width
        final double scaleFactor = availableWidth / imageWidth;
        final double scaledImageHeight = imageHeight * scaleFactor;

        // Create a custom page format with A4 width but height to fit content
        final double customPageHeight = scaledImageHeight + (2 * margin);
        final PdfPageFormat customPageFormat = PdfPageFormat(
          pageWidth,
          customPageHeight,
          marginAll: margin,
        );

        // Add single page with custom height to fit all content
        pdf.addPage(
          pw.Page(
            pageFormat: customPageFormat,
            margin: const pw.EdgeInsets.all(margin),
            build: (pw.Context context) {
              return pw.Image(
                pdfImage,
                width: availableWidth,
                fit: pw.BoxFit.fitWidth,
                alignment: pw.Alignment.topCenter,
              );
            },
          ),
        );
      }

      // Save PDF to file
      final Directory directory = await getApplicationDocumentsDirectory();
      final String pisCorePath = '${directory.path}/PIS Core';
      final Directory pisCoreDir = Directory(pisCorePath);
      
      if (!await pisCoreDir.exists()) {
        await pisCoreDir.create(recursive: true);
      }
      
      final String filePath = '$pisCorePath/$filename';
      final File file = File(filePath);
      await file.writeAsBytes(await pdf.save());
      
      return file;
    } catch (e) {
      throw Exception('Failed to generate multi-page PDF from widgets: $e');
    }
  }

  /// Share generated PDF report
  static Future<void> shareReport(File file, String title) async {
    await SharePlus.instance.share(
      ShareParams(
        files: [XFile(file.path)],
        text: title,
        subject: title,
      ),
    );
  }

  /// Generate filename with timestamp
  static String generateFilename(String prefix) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix}_Report_$timestamp.pdf';
  }
}
