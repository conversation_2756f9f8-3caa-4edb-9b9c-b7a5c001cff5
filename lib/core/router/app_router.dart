import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pis_core/features/earthing/ui/earthing_page.dart';
import 'package:pis_core/features/lightning/ui/lightning_page.dart';
import 'package:pis_core/features/settings/settings_page.dart';
import 'package:pis_core/core/widgets/splash_screen.dart';
import 'package:pis_core/ui/pages/home_page.dart';
import 'package:pis_core/ui/pages/module_selection_page.dart';


class AppRouter {
  static final GoRouter router = GoRouter(
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/home',
        builder: (context, state) => const HomePage(),
      ),
      GoRoute(
        path: '/modules',
        builder: (context, state) => const ModuleSelectionPage(),
      ),
      GoRoute(
        path: '/earthing',
        pageBuilder: (context, state) => CustomTransitionPage<void>(
          key: state.pageKey,
          child: const EarthingPage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // Forward animation: slide up from bottom
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            // For pop animation: slide down to bottom
            if (secondaryAnimation.status == AnimationStatus.forward) {
              return SlideTransition(
                position: secondaryAnimation.drive(
                  Tween(begin: Offset.zero, end: const Offset(0.0, -1.0)).chain(
                    CurveTween(curve: curve),
                  ),
                ),
                child: child,
              );
            }

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        ),
      ),
      GoRoute(
        path: '/lightning',
        pageBuilder: (context, state) => CustomTransitionPage<void>(
          key: state.pageKey,
          child: const LightningPage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // Forward animation: slide up from bottom
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            // For pop animation: slide down to bottom
            if (secondaryAnimation.status == AnimationStatus.forward) {
              return SlideTransition(
                position: secondaryAnimation.drive(
                  Tween(begin: Offset.zero, end: const Offset(0.0, -1.0)).chain(
                    CurveTween(curve: curve),
                  ),
                ),
                child: child,
              );
            }

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        ),
      ),

      GoRoute(
        path: '/settings',
        builder: (context, state) => const SettingsPage(),
      ),
    ],
  );
}
