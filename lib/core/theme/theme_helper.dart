import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pis_core/core/theme/theme_settings_bloc.dart';

/// Helper class to provide easy access to theme settings throughout the app
class ThemeHelper {
  /// Get the current font size scale factor
  static double getFontSizeScale(BuildContext context) {
    final themeState = context.read<ThemeSettingsBloc>().state;
    return themeState.fontSize / 14.0; // Base font size is 14
  }

  /// Get the current font size
  static double getFontSize(BuildContext context) {
    final themeState = context.read<ThemeSettingsBloc>().state;
    return themeState.fontSize;
  }

  /// Get a scaled font size based on the current theme settings
  static double getScaledFontSize(BuildContext context, double baseFontSize) {
    final scale = getFontSizeScale(context);
    return baseFontSize * scale;
  }

  /// Get responsive text style with scaled font size
  static TextStyle getResponsiveTextStyle(
    BuildContext context, {
    double baseFontSize = 14.0,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
  }) {
    final scaledFontSize = getScaledFontSize(context, baseFontSize);
    return TextStyle(
      fontSize: scaledFontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      decoration: decoration,
    );
  }

  /// Get responsive padding based on font size scale
  static EdgeInsets getResponsivePadding(
    BuildContext context, {
    double top = 0,
    double right = 0,
    double bottom = 0,
    double left = 0,
  }) {
    final scale = getFontSizeScale(context);
    return EdgeInsets.only(
      top: top * scale,
      right: right * scale,
      bottom: bottom * scale,
      left: left * scale,
    );
  }

  /// Get responsive symmetric padding
  static EdgeInsets getResponsiveSymmetricPadding(
    BuildContext context, {
    double horizontal = 0,
    double vertical = 0,
  }) {
    final scale = getFontSizeScale(context);
    return EdgeInsets.symmetric(
      horizontal: horizontal * scale,
      vertical: vertical * scale,
    );
  }

  /// Get responsive all padding
  static EdgeInsets getResponsiveAllPadding(BuildContext context, double padding) {
    final scale = getFontSizeScale(context);
    return EdgeInsets.all(padding * scale);
  }

  /// Get responsive sized box
  static SizedBox getResponsiveSizedBox(
    BuildContext context, {
    double? width,
    double? height,
  }) {
    final scale = getFontSizeScale(context);
    return SizedBox(
      width: width != null ? width * scale : null,
      height: height != null ? height * scale : null,
    );
  }

  /// Get responsive border radius
  static BorderRadius getResponsiveBorderRadius(
    BuildContext context,
    double radius,
  ) {
    final scale = getFontSizeScale(context);
    return BorderRadius.circular(radius * scale);
  }

  /// Get responsive icon size
  static double getResponsiveIconSize(BuildContext context, double baseSize) {
    final scale = getFontSizeScale(context);
    return baseSize * scale;
  }
}

/// Extension to make it easier to use responsive values
extension ResponsiveContext on BuildContext {
  double get fontSizeScale => ThemeHelper.getFontSizeScale(this);
  double get fontSize => ThemeHelper.getFontSize(this);
  
  double scaledFontSize(double baseFontSize) => 
      ThemeHelper.getScaledFontSize(this, baseFontSize);
  
  TextStyle responsiveTextStyle({
    double baseFontSize = 14.0,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
  }) => ThemeHelper.getResponsiveTextStyle(
    this,
    baseFontSize: baseFontSize,
    fontWeight: fontWeight,
    color: color,
    height: height,
    decoration: decoration,
  );
  
  EdgeInsets responsivePadding({
    double top = 0,
    double right = 0,
    double bottom = 0,
    double left = 0,
  }) => ThemeHelper.getResponsivePadding(
    this,
    top: top,
    right: right,
    bottom: bottom,
    left: left,
  );
  
  EdgeInsets responsiveSymmetricPadding({
    double horizontal = 0,
    double vertical = 0,
  }) => ThemeHelper.getResponsiveSymmetricPadding(
    this,
    horizontal: horizontal,
    vertical: vertical,
  );
  
  EdgeInsets responsiveAllPadding(double padding) => 
      ThemeHelper.getResponsiveAllPadding(this, padding);
  
  SizedBox responsiveSizedBox({double? width, double? height}) => 
      ThemeHelper.getResponsiveSizedBox(this, width: width, height: height);
  
  BorderRadius responsiveBorderRadius(double radius) => 
      ThemeHelper.getResponsiveBorderRadius(this, radius);
  
  double responsiveIconSize(double baseSize) => 
      ThemeHelper.getResponsiveIconSize(this, baseSize);
}
