import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'theme_settings_bloc.freezed.dart';
part 'theme_settings_bloc.g.dart';

// Events
@freezed
class ThemeSettingsEvent with _$ThemeSettingsEvent {
  const factory ThemeSettingsEvent.changeFontSize(double fontSize) = _ChangeFontSize;
  const factory ThemeSettingsEvent.reset() = _Reset;
}

// State
@freezed
abstract class ThemeSettingsState with _$ThemeSettingsState {
  const factory ThemeSettingsState({
    @Default(14.0) double fontSize,
  }) = _ThemeSettingsState;

  factory ThemeSettingsState.fromJson(Map<String, dynamic> json) =>
      _$ThemeSettingsStateFromJson(json);
}

// Bloc
class ThemeSettingsBloc extends HydratedBloc<ThemeSettingsEvent, ThemeSettingsState> {
  ThemeSettingsBloc() : super(const ThemeSettingsState()) {
    on<_ChangeFontSize>(_onChangeFontSize);
    on<_Reset>(_onReset);
  }

  void _onChangeFontSize(_ChangeFontSize event, Emitter<ThemeSettingsState> emit) {
    emit(state.copyWith(fontSize: event.fontSize));
  }

  void _onReset(_Reset event, Emitter<ThemeSettingsState> emit) {
    emit(const ThemeSettingsState());
  }

  @override
  ThemeSettingsState? fromJson(Map<String, dynamic> json) {
    try {
      return ThemeSettingsState.fromJson(json);
    } catch (_) {
      return null;
    }
  }

  @override
  Map<String, dynamic>? toJson(ThemeSettingsState state) {
    try {
      return state.toJson();
    } catch (_) {
      return null;
    }
  }
}
