import 'package:flutter/material.dart';

/// Brand colors for PIS Calculations app
class AppColors {
  // Primary brand colors
  static const Color primaryRed = Color(0xFFCD1A20);
  static const Color primaryBlue = Color(0xFF03386A);
  static const Color primaryGreen = Color(0xFF388E3C);
  
  // Extended palette based on brand colors
  static const Color lightRed = Color(0xFFE57373);
  static const Color darkRed = Color(0xFF8B0000);
  static const Color lightBlue = Color(0xFF4FC3F7);
  static const Color darkBlue = Color(0xFF01579B);
  
  // Neutral colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color lightGrey = Color(0xFFF5F5F5);
  static const Color mediumGrey = Color(0xFF9E9E9E);
  static const Color darkGrey = Color(0xFF424242);
  static const Color black = Color(0xFF212121);
  
  // Surface colors
  static const Color surface = Color(0xFFFAFAFA);
  static const Color surfaceVariant = Color(0xFFF0F0F0);
  static const Color outline = Color(0xFFE0E0E0);
  
  // Status colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFE53935);
  static const Color info = Color(0xFF2196F3);
}

/// App theme configuration
class AppTheme {
  static ThemeData lightTheme(double fontSize) {
    final fontSizeScale = fontSize / 14.0; // Base font size is 14

    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primaryBlue,
        primary: AppColors.primaryBlue,
        secondary: AppColors.primaryRed,
        surface: AppColors.surface,
        error: AppColors.error,
        brightness: Brightness.light,
      ),

      // Text Theme with responsive font sizes
      textTheme: TextTheme(
        displayLarge: TextStyle(fontSize: 57 * fontSizeScale, fontWeight: FontWeight.w400),
        displayMedium: TextStyle(fontSize: 45 * fontSizeScale, fontWeight: FontWeight.w400),
        displaySmall: TextStyle(fontSize: 36 * fontSizeScale, fontWeight: FontWeight.w400),
        headlineLarge: TextStyle(fontSize: 32 * fontSizeScale, fontWeight: FontWeight.w400),
        headlineMedium: TextStyle(fontSize: 28 * fontSizeScale, fontWeight: FontWeight.w400),
        headlineSmall: TextStyle(fontSize: 24 * fontSizeScale, fontWeight: FontWeight.w400),
        titleLarge: TextStyle(fontSize: 22 * fontSizeScale, fontWeight: FontWeight.w500),
        titleMedium: TextStyle(fontSize: 16 * fontSizeScale, fontWeight: FontWeight.w500),
        titleSmall: TextStyle(fontSize: 14 * fontSizeScale, fontWeight: FontWeight.w500),
        bodyLarge: TextStyle(fontSize: 16 * fontSizeScale, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontSize: 14 * fontSizeScale, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontSize: 12 * fontSizeScale, fontWeight: FontWeight.w400),
        labelLarge: TextStyle(fontSize: 14 * fontSizeScale, fontWeight: FontWeight.w500),
        labelMedium: TextStyle(fontSize: 12 * fontSizeScale, fontWeight: FontWeight.w500),
        labelSmall: TextStyle(fontSize: 11 * fontSizeScale, fontWeight: FontWeight.w500),
      ),
      
      // App Bar Theme
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        elevation: 2,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: AppColors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
      
      // Card Theme
      cardTheme: CardThemeData(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: AppColors.white,
        shadowColor: AppColors.darkGrey.withValues(alpha: 0.2),
      ),
      
      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryRed,
          foregroundColor: AppColors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primaryBlue,
          side: const BorderSide(color: AppColors.primaryBlue, width: 2),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primaryBlue,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8 * fontSizeScale),
          borderSide: const BorderSide(color: AppColors.mediumGrey, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8 * fontSizeScale),
          borderSide: const BorderSide(color: AppColors.mediumGrey, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8 * fontSizeScale),
          borderSide: const BorderSide(color: AppColors.primaryBlue, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8 * fontSizeScale),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8 * fontSizeScale),
          borderSide: const BorderSide(color: AppColors.outline, width: 1),
        ),
        filled: true,
        fillColor: AppColors.white,
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16 * fontSizeScale,
          vertical: 12 * fontSizeScale,
        ),
      ),

      // Divider Theme
      dividerTheme: const DividerThemeData(
        color: AppColors.outline,
        thickness: 1,
        space: 1,
      ),
      
      // Icon Theme
      iconTheme: const IconThemeData(
        color: AppColors.darkGrey,
        size: 24,
      ),
      
      // Primary Icon Theme
      primaryIconTheme: const IconThemeData(
        color: AppColors.white,
        size: 24,
      ),
    );
  }
}
