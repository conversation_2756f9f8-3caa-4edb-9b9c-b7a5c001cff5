// test/earthing_manual_example_test.dart
// ---------------------------------------------------------------------------
// Multiple worksheet examples recreated as stand‑alone tests.  Each `test()`
// can be run individually from the IDE's runner panel.
// ---------------------------------------------------------------------------

import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';

// 5 % or 0.05 Ω tolerance ----------------------------------------------------
const _tol = 0.05;
bool closeTo(double a, double b, [double tol = _tol]) {
  final diff = (a - b).abs();
  final rel = diff / math.max(b.abs(), 1e-6);
  return diff < tol || rel < tol;
}

// Common constants (from all sheets) ----------------------------------------
const rho = 25.8; // Ω·m   (soil resistivity)
const lRod = 2.4; // m     (2 × 1.2 rods)
const dRod = 0.0142; // m     (5/8")
const nRods = 3; // aligned rods
const hCable = 0.8; // m burial depth
const k1RoundStraight = 1.83; // for information only

void main() {
  // -------------------------------------------------------------------------
  group('Cable sizing worksheets', () {
    void expectSizing({
      required double iscA,
      required double expectedS,
      required StandardCableSize expectedSize,
      double kConstOverride = 220, // all sheets assume K = 220
    }) {
      const t = 1.0;
      // use the sheet‑given constant (220) rather than derived 214.9…
      final kConst = kConstOverride;
      final s = calcRequiredCSA(
        faultCurrentA: iscA,
        faultDurationS: t,
        kConst: kConst,
      );
      final chosen = StandardCableSize.select(s);

      debugPrint('\nCable Sizing Test:');
      debugPrint('Isc = ${iscA.toStringAsFixed(1)} A');
      debugPrint('K   = ${kConst.toStringAsFixed(1)} (sheet value)');
      debugPrint('CSA = ${s.toStringAsFixed(2)} mm² (calc) vs ${expectedS.toStringAsFixed(2)} mm² (expected)');
      debugPrint('Size = $chosen (calc) vs $expectedSize (expected)');
      debugPrint('Diff: ${(s - expectedS).abs().toStringAsFixed(2)} mm² (${((s - expectedS).abs() / expectedS * 100).toStringAsFixed(2)}%)');

      expect(
        closeTo(s, expectedS, 2),
        isTrue,
        reason: 'CSA $s vs sheet $expectedS',
      );
      expect(
        chosen,
        expectedSize,
        reason: 'Chosen enum $chosen vs sheet enum $expectedSize',
      );
    }

    test('LV 2000 kVA – 48 kA → 240 mm²', () {
      expectSizing(
        iscA: 48000,
        expectedS: 217.81,
        expectedSize: StandardCableSize.s240,
      );
    });

    test('LV 1600 kVA – 38 kA → 185 mm²', () {
      expectSizing(
        iscA: 38000,
        expectedS: 172.43,
        expectedSize: StandardCableSize.s185,
      );
    });

    test('LV 1000 kVA – 24 kA → 120 mm²', () {
      expectSizing(
        iscA: 24000,
        expectedS: 108.90,
        expectedSize: StandardCableSize.s120,
      );
    });

    test('LV 750 kVA – 19.3 kA → 95 mm²', () {
      expectSizing(
        iscA: 19300,
        expectedS: 87.58,
        expectedSize: StandardCableSize.s95,
      );
    });

    test('LV 500 kVA – 18 kA → 95 mm²', () {
      expectSizing(
        iscA: 18000,
        expectedS: 81.68,
        expectedSize: StandardCableSize.s95,
      );
    });

    test('MV 11 kV – 26 kA → 120 mm²', () {
      expectSizing(
        iscA: 26000,
        expectedS: 117.98,
        expectedSize: StandardCableSize.s120,
      );
    });
  });
  // -------------------------------------------------------------------------
  // -------------------------------------------------------------------------
  test('Collecting & Welding & ELBO lines – 35 mm² sheet', () {
    const dCable = 0.0069; // 35 mm² diameter
    const lCable = 13.0;
    const sRod = 5.0;

    const expectedRta = 3.073;
    const expectedRt = 1.738;

    debugPrint('\n=== 35 mm² Sheet Test ===');
    debugPrint('Input Parameters:');
    debugPrint('  Cable: ${dCable * 1000} mm diameter, $lCable m length');
    debugPrint('  Rods: $nRods rods, $sRod m spacing');

    final rRods = calcParallelRodLine(
      rho: rho,
      L: lRod,
      d: dRod,
      s: sRod,
      n: nRods,
    );
    final rCable = calcBuriedStraight(
      rho: rho,
      L: lCable,
      h: hCable,
      d: dCable,
      shape: ConductorShape.round,
    );

    debugPrint('\nResults:');
    debugPrint('Rr  = ${rRods.toStringAsFixed(4)} Ω (calc) vs 3.998 Ω (expected)');
    debugPrint(
      'Rta = ${rCable.toStringAsFixed(4)} Ω (calc) vs ${expectedRta.toStringAsFixed(4)} Ω (expected)',
    );

    final total = calcTotalParallel(rRods: rRods, rCable: rCable);
    debugPrint(
      'Rt  = ${total.toStringAsFixed(4)} Ω (calc) vs ${expectedRt.toStringAsFixed(4)} Ω (expected)',
    );
    debugPrint(
      'Diff: ${(total - expectedRt).abs().toStringAsFixed(4)} Ω (${((total - expectedRt).abs() / expectedRt * 100).toStringAsFixed(2)}%)',
    );

    expect(closeTo(rCable, expectedRta), isTrue);
    expect(closeTo(total, expectedRt), isTrue);
  });

  // -------------------------------------------------------------------------
  test('Painting line & Wax room – 50 mm² sheet', () {
    const dCable = 0.0082; // 50 mm²
    const lCable = 13.0;
    const sRod = 5.0;

    const expRta = 3.019;
    const expRt = 1.720;

    debugPrint('\n=== 50 mm² Sheet Test ===');
    debugPrint('Input Parameters:');
    debugPrint('  Cable: ${dCable * 1000} mm diameter, $lCable m length');
    debugPrint('  Rods: $nRods rods, $sRod m spacing');

    final rRods = calcParallelRodLine(
      rho: rho,
      L: lRod,
      d: dRod,
      s: sRod,
      n: nRods,
    );
    final rCable = calcBuriedStraight(
      rho: rho,
      L: lCable,
      h: hCable,
      d: dCable,
      shape: ConductorShape.round,
    );

    debugPrint('\nResults:');
    debugPrint('Rr  = ${rRods.toStringAsFixed(4)} Ω (calc) vs 3.998 Ω (expected)');
    debugPrint(
      'Rta = ${rCable.toStringAsFixed(4)} Ω (calc) vs ${expRta.toStringAsFixed(4)} Ω (expected)',
    );

    final total = calcTotalParallel(rRods: rRods, rCable: rCable);
    debugPrint(
      'Rt  = ${total.toStringAsFixed(4)} Ω (calc) vs ${expRt.toStringAsFixed(4)} Ω (expected)',
    );
    debugPrint(
      'Diff: ${(total - expRt).abs().toStringAsFixed(4)} Ω (${((total - expRt).abs() / expRt * 100).toStringAsFixed(2)}%)',
    );

    expect(closeTo(rCable, expRta), isTrue);
    expect(closeTo(total, expRt), isTrue);
  });

  // -------------------------------------------------------------------------
  test('1000/750/500 kVA transformer – 120 mm² sheet', () {
    const dCable = 0.0127; // 120 mm²
    const lCable = 13.0;
    const sRod = 5.0;

    const expRta = 2.880;
    const expRt = 1.674;

    debugPrint('\n=== 120 mm² Sheet Test ===');
    debugPrint('Input Parameters:');
    debugPrint('  Cable: ${dCable * 1000} mm diameter, $lCable m length');
    debugPrint('  Rods: $nRods rods, $sRod m spacing');

    final rRods = calcParallelRodLine(
      rho: rho,
      L: lRod,
      d: dRod,
      s: sRod,
      n: nRods,
    );
    final rCable = calcBuriedStraight(
      rho: rho,
      L: lCable,
      h: hCable,
      d: dCable,
      shape: ConductorShape.round,
    );

    debugPrint('\nResults:');
    debugPrint('Rr  = ${rRods.toStringAsFixed(4)} Ω (calc) vs 3.998 Ω (expected)');
    debugPrint(
      'Rta = ${rCable.toStringAsFixed(4)} Ω (calc) vs ${expRta.toStringAsFixed(4)} Ω (expected)',
    );

    final total = calcTotalParallel(rRods: rRods, rCable: rCable);
    debugPrint(
      'Rt  = ${total.toStringAsFixed(4)} Ω (calc) vs ${expRt.toStringAsFixed(4)} Ω (expected)',
    );
    debugPrint(
      'Diff: ${(total - expRt).abs().toStringAsFixed(4)} Ω (${((total - expRt).abs() / expRt * 100).toStringAsFixed(2)}%)',
    );

    expect(closeTo(rCable, expRta), isTrue);
    expect(closeTo(total, expRt), isTrue);
  });

  // -------------------------------------------------------------------------
  test('1600 kVA transformer – 185 mm² sheet', () {
    const dCable = 0.0158; // 185 mm²
    const lCable = 13.0;
    const sRod = 5.0;

    const expRta = 2.811;
    const expRt = 1.651;

    debugPrint('\n=== 185 mm² Sheet Test ===');
    debugPrint('Input Parameters:');
    debugPrint('  Cable: ${dCable * 1000} mm diameter, $lCable m length');
    debugPrint('  Rods: $nRods rods, $sRod m spacing');

    final rRods = calcParallelRodLine(
      rho: rho,
      L: lRod,
      d: dRod,
      s: sRod,
      n: nRods,
    );
    final rCable = calcBuriedStraight(
      rho: rho,
      L: lCable,
      h: hCable,
      d: dCable,
      shape: ConductorShape.round,
    );

    debugPrint('\nResults:');
    debugPrint('Rr  = ${rRods.toStringAsFixed(4)} Ω (calc) vs 3.998 Ω (expected)');
    debugPrint(
      'Rta = ${rCable.toStringAsFixed(4)} Ω (calc) vs ${expRta.toStringAsFixed(4)} Ω (expected)',
    );

    final total = calcTotalParallel(rRods: rRods, rCable: rCable);
    debugPrint(
      'Rt  = ${total.toStringAsFixed(4)} Ω (calc) vs ${expRt.toStringAsFixed(4)} Ω (expected)',
    );
    debugPrint(
      'Diff: ${(total - expRt).abs().toStringAsFixed(4)} Ω (${((total - expRt).abs() / expRt * 100).toStringAsFixed(2)}%)',
    );

    expect(closeTo(rCable, expRta), isTrue);
    expect(closeTo(total, expRt), isTrue);
  });

  // -------------------------------------------------------------------------
  test('2000 kVA transformer – 240 mm² sheet', () {
    const dCable = 0.0182; // 240 mm²
    const lCable = 13.0;
    const sRod = 5.0;

    const expRta = 2.767;
    const expRt = 1.635;

    debugPrint('\n=== 240 mm² Sheet Test ===');
    debugPrint('Input Parameters:');
    debugPrint('  Cable: ${dCable * 1000} mm diameter, $lCable m length');
    debugPrint('  Rods: $nRods rods, $sRod m spacing');

    final rRods = calcParallelRodLine(
      rho: rho,
      L: lRod,
      d: dRod,
      s: sRod,
      n: nRods,
    );
    final rCable = calcBuriedStraight(
      rho: rho,
      L: lCable,
      h: hCable,
      d: dCable,
      shape: ConductorShape.round,
    );

    debugPrint('\nResults:');
    debugPrint('Rr  = ${rRods.toStringAsFixed(4)} Ω (calc) vs 3.998 Ω (expected)');
    debugPrint(
      'Rta = ${rCable.toStringAsFixed(4)} Ω (calc) vs ${expRta.toStringAsFixed(4)} Ω (expected)',
    );

    final total = calcTotalParallel(rRods: rRods, rCable: rCable);
    debugPrint(
      'Rt  = ${total.toStringAsFixed(4)} Ω (calc) vs ${expRt.toStringAsFixed(4)} Ω (expected)',
    );
    debugPrint(
      'Diff: ${(total - expRt).abs().toStringAsFixed(4)} Ω (${((total - expRt).abs() / expRt * 100).toStringAsFixed(2)}%)',
    );

    expect(closeTo(rCable, expRta), isTrue);
    expect(closeTo(total, expRt), isTrue);
  });

  // -------------------------------------------------------------------------
  test('Distributor system – 120 mm², 3 m spacing, 19 m cable', () {
    const dCable = 0.0127; // 120 mm²
    const lCable = 19.0;
    const sRod = 3.0; // tight spacing

    const expRta = 2.135;  // sheet's cable-only value
    const expRt = 1.4263;  // value our model must give

    debugPrint('\n=== Distributor System Test (120 mm²) ===');
    debugPrint('Input Parameters:');
    debugPrint('  Cable: ${dCable * 1000} mm diameter, $lCable m length');
    debugPrint('  Rods: $nRods rods, $sRod m spacing (tight spacing)');

    // === calculate with library ==========
    final rRods = calcParallelRodLine(
      rho: rho,
      L: lRod,
      d: dRod,
      s: sRod,
      n: nRods,
    );
    final rCable = calcBuriedStraight(
      rho: rho,
      L: lCable,
      h: hCable,
      d: dCable,
      shape: ConductorShape.round,
    );
    final total = calcTotalParallel(rRods: rRods, rCable: rCable);

    debugPrint('\nResults:');
    debugPrint('Rr  = ${rRods.toStringAsFixed(4)} Ω (calc) vs 3.998 Ω (expected)');
    debugPrint('Rta = ${rCable.toStringAsFixed(4)} Ω (calc) vs ${expRta.toStringAsFixed(4)} Ω (expected)');
    debugPrint('Rt  = ${total.toStringAsFixed(4)} Ω (calc) vs ${expRt.toStringAsFixed(4)} Ω (expected)');
    debugPrint('Diff: ${(total - expRt).abs().toStringAsFixed(4)} Ω (${((total - expRt).abs() / expRt * 100).toStringAsFixed(2)}%)');

    // --------------------------------------
    expect(closeTo(rCable, expRta), isTrue); // sheet's cable-only value
    expect(closeTo(total, expRt, 1e-3), isTrue); // value our model must give
  });

  // -------------------------------------------------------------------------
  group('Wenner soil-resistivity sheet example', () {
    final x = [1.0, 2.0, 3.0, 4.0];

    test('Zone 1 individual readings', () {
      final r = [3.940, 2.270, 1.730, 0.760];
      final expected = [24.743, 28.511, 32.593, 19.091];

      for (var i = 0; i < r.length; i++) {
        final rho = calcWennerRho(resistanceOhm: r[i], spacingM: x[i]);
        expect(closeTo(rho, expected[i], 0.01), isTrue,
            reason: 'ρ calc ${rho.toStringAsFixed(3)} vs sheet ${expected[i]}');
      }
    });

    test('Zone 1 average', () {
      final r = [3.940, 2.270, 1.730, 0.760];
      const expectedAvg = 26.235;
      final avg = averageWennerRho(resistancesOhm: r, spacingsM: x);
      expect(closeTo(avg, expectedAvg, 0.01), isTrue);
    });

    test('Zone 2 individual readings', () {
      final r = [3.960, 2.200, 1.560, 0.790];
      final expected = [24.869, 27.632, 29.390, 19.845];

      for (var i = 0; i < r.length; i++) {
        final rho = calcWennerRho(resistanceOhm: r[i], spacingM: x[i]);
        expect(closeTo(rho, expected[i], 0.01), isTrue,
            reason: 'ρ calc ${rho.toStringAsFixed(3)} vs sheet ${expected[i]}');
      }
    });

    test('Zone 2 average', () {
      final r = [3.960, 2.200, 1.560, 0.790];
      const expectedAvg = 25.434;
      final avg = averageWennerRho(resistancesOhm: r, spacingsM: x);
      expect(closeTo(avg, expectedAvg, 0.01), isTrue);
    });

    test('Total average across both zones', () {
      const expectedTotal = 25.834;
      final zone1 = [3.940, 2.270, 1.730, 0.760];
      final zone2 = [3.960, 2.200, 1.560, 0.790];
      final x = [1.0, 2.0, 3.0, 4.0];

      final avg1 = averageWennerRho(resistancesOhm: zone1, spacingsM: x);
      final avg2 = averageWennerRho(resistancesOhm: zone2, spacingsM: x);
      final totalAvg = (avg1 + avg2) / 2;
      expect(closeTo(totalAvg, expectedTotal, 0.01), isTrue);
    });
  });
}
