// test/earthing_bloc_test.dart
// ---------------------------------------------------------------------------
// Comprehensive unit‑tests for earthing_calculations.dart helpers *and*
// EarthingBloc integration.
// ---------------------------------------------------------------------------
// Run with:  flutter test  (after build_runner for Freezed part‑files)
// ---------------------------------------------------------------------------

import 'dart:math' as math;

import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pis_core/features/earthing/bloc/earthing_bloc.dart';
import 'package:pis_core/features/earthing/calculation/earthing_formulas.dart';
import 'package:pis_core/features/earthing/calculation/earthing_input.dart';

// ---------------------------------------------------------------------------
// Helper – tolerant double comparison (5 % or 0.05 Ω)
// ---------------------------------------------------------------------------

const _tol = 0.05; // 5 % or 0.05 Ω whichever is bigger

bool closeTo(double a, double b, [double tol = _tol]) {
  final diff = (a - b).abs();
  final rel  = diff / math.max(b.abs(), 1e-6);
  return diff < tol || rel < tol;
}

void main() {
  // -------------------------------------------------------------------------
  group('Clause 9.5 formula checks', () {
    test('Single rod – Annex B example (ρ = 350, L = 2.4 m, d = 0.016 m)', () {
      const rho = 350.0;
      const L   = 2.4;
      const d   = 0.016;
      const expected = 141.35; // Ω
      final r = calcSingleRod(rho: rho, L: L, d: d);
      expect(closeTo(r, expected), isTrue,
          reason: 'Calculated $r Ω vs expected $expected Ω');
    });

    test('Hollow square – Annex B example (ρ = 100 Ω·m, 2 rods/side, s = 4 m)', () {
      const rho = 100.0;  // Annex B table uses 100 Ω·m
      const L   = 2.4;
      const d   = 0.016;
      const s   = 4.0;
      const perSide = 2;
      const expected = 43.08; // ≈ calculated by BS formula
      final r = calcHollowSquareRod(
        rho: rho, L: L, d: d, s: s, rodsPerSide: perSide,
      );
      expect(closeTo(r, expected), isTrue);
    });

    test('Parallel line – Annex B (n = 6, s = 3.6 m)', () {
      const rho = 350.0;
      const L   = 2.4;
      const d   = 0.016;
      const s   = 3.6;
      const n   = 6;
      const expected = 31.04;
      final r = calcParallelRodLine(
        rho: rho, L: L, d: d, s: s, n: n,
      );
      expect(closeTo(r, expected), isTrue);
    });

    test('Buried straight strip – sanity check (> 0 Ω)', () {
      const rho = 100.0;
      const L   = 50.0;
      const h   = 0.6;
      const d   = 0.05;
      final r = calcBuriedStraight(
        rho: rho, L: L, h: h, d: d,
        shape: ConductorShape.strip,
      );
      expect(r, greaterThan(0));
    });
  });

  // -------------------------------------------------------------------------
  group('Cable helpers', () {
    test('CSA adiabatic – 26 kA 1 s copper, K = 220', () {
      const isc = 26_000.0; // A
      const t   = 1.0;      // s
      final k = calcKConstant(material: ConductorMaterial.copper,
          initialTempC: 30, finalTempC: 450);
      final s = calcRequiredCSA(
        faultCurrentA: isc,
        faultDurationS: t,
        kConst: k,
      );
      // Expected 117.98 mm² (spreadsheet) choose tol 2 mm²
      expect(closeTo(s, 117.98, 2), isTrue);
    });

    test('Cable resistance direct – 30 m, CSA 240 mm² copper', () {
      const len = 30.0;
      const csa = 240.0;
      final r = calcCableResistance(length: len, areaMm2: csa);
      expect(closeTo(r, 0.00215, 1e-4), isTrue);
    });
  });

  // -------------------------------------------------------------------------
  group('Resistance combiner', () {
    test('Parallel 10 Ω ‖ 10 Ω = 5 Ω', () {
      final rt = combineParallel([10, 10]);
      expect(closeTo(rt, 5, 1e-6), isTrue);
    });
  });

  // -------------------------------------------------------------------------
  group('EarthingBloc integration', () {
    blocTest<EarthingBloc, EarthingBlocState>(
      'emits state with non‑zero Rt for single‑rod default input',
      build: () => EarthingBloc(),
      act: (b) {
        b.add(EarthingBlocEvent.updateInput(const EarthingCalculationInput(
          soilResistivity: 100,
          customRodDiameter: 0.014,
          rodLength: 1.5,
          arrangement: EarthingArrangement.singleRod,
          numberOfWells: 1,
          rodSpacing: 1.5,
          isClosedLoop: false,
        )));
      },
      wait: const Duration(milliseconds: 25), // allow bloc async dispatch
      expect: () => [
        isA<EarthingBlocState>().having(
          (s) => s.result.totalResistance,
          'Rt',
          greaterThan(0),
        ),
      ],
    );
  });
}
