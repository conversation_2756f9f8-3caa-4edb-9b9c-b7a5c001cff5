// test/features/lightning/lightning_bloc_test.dart
// ---------------------------------------------------------------------------
// Tests for the Lightning BLoC functionality
// ---------------------------------------------------------------------------

import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';
import 'package:pis_core/features/lightning/bloc/lightning_bloc.dart';
import 'package:pis_core/features/lightning/bloc/lightning_event.dart';
import 'package:pis_core/features/lightning/bloc/lightning_state.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';

void main() {
  group('LightningBloc', () {
    late LightningBloc lightningBloc;

    setUp(() {
      lightningBloc = LightningBloc();
    });

    tearDown(() {
      lightningBloc.close();
    });

    test('initial state is LightningInitialState', () {
      expect(lightningBloc.state, equals(const LightningState.initial()));
    });

    blocTest<LightningBloc, LightningState>(
      'emits [loading, success] when calculation is performed with valid input',
      build: () => lightningBloc,
      act: (bloc) => bloc.add(const LightningEvent.calculate(
        LightningCalculationInput(
          lengthM: 41.0,
          widthM: 8.0,
          heightM: 11.0,
          location: 'Egypt',
          flashDensityNg: 1.0,
          locationFactor: LocationFactorCoeff.isolated,
          structureMaterial: StructureMaterial.metal,
          roofMaterial: RoofMaterial.nonmetallic,
          contentsCoeff: StructureContentsCoeff.lowValue,
          occupancyCoeff: StructureOccupancyCoeff.normallyOccupied,
          consequenceCoeff: LightningConsequenceCoeff.continuityNotRequired,
        ),
      )),
      expect: () => [
        const LightningState.loading(),
        isA<LightningSuccessState>(),
      ],
    );

    blocTest<LightningBloc, LightningState>(
      'emits [loading, error] when calculation is performed with invalid input',
      build: () => lightningBloc,
      act: (bloc) => bloc.add(const LightningEvent.calculate(
        LightningCalculationInput(
          lengthM: -1.0, // Invalid negative length
          widthM: 8.0,
          heightM: 11.0,
        ),
      )),
      expect: () => [
        const LightningState.loading(),
        isA<LightningErrorState>(),
      ],
    );

    blocTest<LightningBloc, LightningState>(
      'emits [initial] when reset event is triggered',
      build: () => lightningBloc,
      act: (bloc) => bloc.add(const LightningEvent.reset()),
      expect: () => [
        const LightningState.initial(),
      ],
    );

    test('successful calculation produces expected results', () async {
      // Arrange
      const input = LightningCalculationInput(
        lengthM: 41.0,
        widthM: 8.0,
        heightM: 11.0,
        location: 'Egypt',
        flashDensityNg: 1.0,
        locationFactor: LocationFactorCoeff.isolated,
        structureMaterial: StructureMaterial.metal,
        roofMaterial: RoofMaterial.nonmetallic,
        contentsCoeff: StructureContentsCoeff.lowValue,
        occupancyCoeff: StructureOccupancyCoeff.normallyOccupied,
        consequenceCoeff: LightningConsequenceCoeff.continuityNotRequired,
      );

      // Act
      lightningBloc.add(LightningEvent.calculate(input));
      
      // Wait for the calculation to complete
      await expectLater(
        lightningBloc.stream,
        emitsInOrder([
          const LightningState.loading(),
          isA<LightningSuccessState>(),
        ]),
      );

      // Assert
      final state = lightningBloc.state;
      expect(state, isA<LightningSuccessState>());
      
      if (state is LightningSuccessState) {
                // Verify key calculation results match our test expectations (1-2% tolerance)
        expect(state.result.expectedStrikeFrequency, closeTo(0.00698146, 0.00014)); // ~2% tolerance
        expect(state.result.tolerableStrikeFrequency, closeTo(0.003, 0.00006)); // ~2% tolerance
        expect(state.result.lpsRecommended, isTrue);
        expect(state.result.protectionLevel, equals(4));
        expect(state.result.riskLevel, closeTo(57.0, 1.2)); // ~2% tolerance
      }
    });
  });
} 