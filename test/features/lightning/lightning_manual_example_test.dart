// test/features/lightning/lightning_manual_example_test.dart
// ---------------------------------------------------------------------------
// Multiple calculation examples recreated as stand-alone tests based on
// the risk assessment sheet images provided. Each test can be run individually
// from the IDE's runner panel to verify calculations match expected values.
// ---------------------------------------------------------------------------

import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pis_core/core/constants/nfpa780_constants.dart';
import 'package:pis_core/features/lightning/calculation/lightning_formulas.dart';
import 'package:pis_core/features/lightning/calculation/lightning_input.dart';

// 5% tolerance for floating point comparisons
const _tol = 0.05;
bool closeTo(double a, double b, [double tol = _tol]) {
  final diff = (a - b).abs();
  final rel = diff / math.max(b.abs(), 1e-6);
  return diff < tol || rel < tol;
}

void main() {
  group('Lightning Protection System Calculations', () {
    
    // Test data from the provided risk assessment sheet images
    test('Data Center Example - Complete Risk Assessment', () {
      debugPrint('\n=== Data Center Example Test ===');
      
      const input = LightningCalculationInput(
        projectName: 'Data Center - Distributor Room',
        mainContractor: 'Hassan Allam',
        lengthM: 41.0,
        widthM: 8.0,
        heightM: 11.0,
        location: 'Egypt',
        flashDensityNg: 1.0,
        locationFactor: LocationFactorCoeff.isolated, // C1 = 1.0
        structureMaterial: StructureMaterial.metal,
        roofMaterial: RoofMaterial.nonmetallic, // C2 = 1.0
        contentsCoeff: StructureContentsCoeff.lowValue, // C3 = 0.5
        occupancyCoeff: StructureOccupancyCoeff.normallyOccupied, // C4 = 1.0
        consequenceCoeff: LightningConsequenceCoeff.continuityNotRequired, // C5 = 1.0
      );

      final result = performLightningRiskAssessment(input: input);

      debugPrint('\nInput Parameters:');
      debugPrint('Building: ${input.lengthM}m × ${input.widthM}m × ${input.heightM}m');
      debugPrint('Location: ${input.location} (Ng = ${input.flashDensityNg})');
      debugPrint('C1 = ${input.c1}, C2 = ${input.c2}, C3 = ${input.c3}, C4 = ${input.c4}, C5 = ${input.c5}');

      debugPrint('\nCalculated Results:');
      debugPrint('Collective Area: ${result.collectiveAreaKm2.toStringAsFixed(6)} km²');
      debugPrint('Expected frequency (Ne): ${result.expectedStrikeFrequency.toStringAsFixed(4)} events/year');
      debugPrint('Tolerable frequency (Nt): ${result.tolerableStrikeFrequency.toStringAsFixed(4)} events/year');
      debugPrint('Risk Level: ${result.riskLevel.toStringAsFixed(1)}%');
      debugPrint('LPS Recommended: ${result.lpsRecommended}');
      debugPrint('Protection Level: ${result.protectionLevel}');

      // Expected values from the calculation sheet images (more precise)
      const expectedCollectiveAreaKm2 = 0.006981462; // 6981.462 m² converted to km²
      const expectedNe = 0.00698146; // events/year (more precise)
      const expectedNt = 0.003; // events/year  
      const expectedRiskLevel = 57.0; // % (1 - Nt/Ne) * 100 = (1 - 0.003/0.00698146) * 100
      const expectedProtectionLevel = 4;
      const expectedLpsRecommended = true;

      debugPrint('\nExpected vs Actual:');
      debugPrint('Collective Area: ${expectedCollectiveAreaKm2.toStringAsFixed(6)} vs ${result.collectiveAreaKm2.toStringAsFixed(6)} km²');
      debugPrint('Ne: ${expectedNe.toStringAsFixed(4)} vs ${result.expectedStrikeFrequency.toStringAsFixed(4)} events/year');
      debugPrint('Nt: ${expectedNt.toStringAsFixed(4)} vs ${result.tolerableStrikeFrequency.toStringAsFixed(4)} events/year');
      debugPrint('Risk: ${expectedRiskLevel.toStringAsFixed(1)}% vs ${result.riskLevel.toStringAsFixed(1)}%');

      // Verify calculations match expected values (1-2% tolerance)
      expect(closeTo(result.collectiveAreaKm2, expectedCollectiveAreaKm2, 0.00014), isTrue, // ~2% tolerance
          reason: 'Collective area calculation mismatch');
      expect(closeTo(result.expectedStrikeFrequency, expectedNe, 0.00014), isTrue, // ~2% tolerance
          reason: 'Expected strike frequency calculation mismatch');
      expect(closeTo(result.tolerableStrikeFrequency, expectedNt, 0.00006), isTrue, // ~2% tolerance
          reason: 'Tolerable strike frequency calculation mismatch');
      // Risk level calculation: 1 - (Nt/Ne) = 1 - (0.003/0.00698146) = 0.570 = 57.0%
      expect(closeTo(result.riskLevel, expectedRiskLevel, 1.2), isTrue, // ~2% tolerance
          reason: 'Risk level calculation mismatch');
      expect(result.protectionLevel, expectedProtectionLevel,
          reason: 'Protection level determination mismatch');
      expect(result.lpsRecommended, expectedLpsRecommended,
          reason: 'LPS recommendation mismatch');
    });

    test('Collective Area Calculation - Rectangular Structure', () {
      debugPrint('\n=== Collective Area Calculation Test ===');
      
      const length = 41.0;
      const width = 8.0;
      const height = 11.0;
      
      final area = calcCollectiveAreaRectangular(
        lengthM: length,
        widthM: width,
        heightM: height,
      );
      
      // Manual calculation: L×W + 6H(L + W) + 9πH²
      final expectedAreaM2 = length * width + 
                             6 * height * (length + width) + 
                             9 * math.pi * height * height;
      final expectedAreaKm2 = expectedAreaM2 / 1e6;
      
      debugPrint('Building: ${length}m × ${width}m × ${height}m');
      debugPrint('Calculated area: ${area.toStringAsFixed(6)} km²');
      debugPrint('Expected area: ${expectedAreaKm2.toStringAsFixed(6)} km²');
      debugPrint('Area in m²: ${(area * 1e6).toStringAsFixed(1)} m²');
      
      expect(closeTo(area, expectedAreaKm2, 1e-6), isTrue,
          reason: 'Collective area formula implementation error');
      expect(closeTo(area * 1e6, 6981.462, 14), isTrue, // ~2% tolerance
          reason: 'Area should match sheet value of 6981.462 m²');
    });

    test('Construction Coefficient Lookup Table', () {
      debugPrint('\n=== Construction Coefficient Lookup Test ===');
      
      // Test all combinations from the lookup table
      final testCases = [
        (StructureMaterial.metal, RoofMaterial.metal, 0.5),
        (StructureMaterial.metal, RoofMaterial.nonmetallic, 1.0),
        (StructureMaterial.metal, RoofMaterial.combustible, 2.0),
        (StructureMaterial.nonmetallic, RoofMaterial.metal, 1.0),
        (StructureMaterial.nonmetallic, RoofMaterial.nonmetallic, 1.0),
        (StructureMaterial.nonmetallic, RoofMaterial.combustible, 2.5),
        (StructureMaterial.combustible, RoofMaterial.metal, 2.0),
        (StructureMaterial.combustible, RoofMaterial.nonmetallic, 2.5),
        (StructureMaterial.combustible, RoofMaterial.combustible, 3.0),
      ];
      
      for (final (structure, roof, expected) in testCases) {
        final c2 = getConstructionCoefficient(
          structureMaterial: structure,
          roofMaterial: roof,
        );
        
        debugPrint('${structure.label} + ${roof.label} = $c2 (expected: $expected)');
        expect(c2, expected,
            reason: 'C2 lookup failed for ${structure.label} + ${roof.label}');
      }
    });

    test('Expected Strike Frequency Calculation', () {
      debugPrint('\n=== Expected Strike Frequency Test ===');
      
      const ng = 1.0; // fl/km²/year (Egypt)
      const ac = 0.006981462; // km² (more precise from previous test)
      const c1 = 1.0; // isolated structure
      
      final ne = calcExpectedStrikeFrequency(
        flashDensityNg: ng,
        collectiveAreaKm2: ac,
        locationFactorC1: c1,
      );
      
      // Ne = Ng × Ac × C1 × 10^-6
      // But wait, looking at the formula, it should be just Ng × Ac × C1
      // The 10^-6 factor seems to be already included in the formula
      const expectedNe = 0.00698146; // more precise from the sheet
      
      debugPrint('Ng = $ng fl/km²/year');
      debugPrint('Ac = $ac km²');
      debugPrint('C1 = $c1');
      debugPrint('Ne = ${ne.toStringAsFixed(4)} events/year (calculated)');
      debugPrint('Expected = ${expectedNe.toStringAsFixed(4)} events/year');
      
      expect(closeTo(ne, expectedNe, 0.00014), isTrue, // ~2% tolerance
          reason: 'Expected strike frequency calculation mismatch');
    });

    test('Tolerable Strike Frequency Calculation', () {
      debugPrint('\n=== Tolerable Strike Frequency Test ===');
      
      const c2 = 1.0; // metal structure + nonmetallic roof
      const c3 = 0.5; // low value contents
      const c4 = 1.0; // normally occupied
      const c5 = 1.0; // continuity not required
      
      final nt = calcTolerableStrikeFrequency(
        constructionCoeffC2: c2,
        contentsCoeffC3: c3,
        occupancyCoeffC4: c4,
        consequenceCoeffC5: c5,
      );
      
      // Nt = (1.5 × 10^-5) / (C2 × C3 × C4 × C5)
      const expectedNt = 0.0030; // from the sheet
      
      debugPrint('C2 = $c2, C3 = $c3, C4 = $c4, C5 = $c5');
      debugPrint('Nt = ${nt.toStringAsFixed(4)} events/year (calculated)');
      debugPrint('Expected = ${expectedNt.toStringAsFixed(4)} events/year');
      
      expect(closeTo(nt, expectedNt, 0.00006), isTrue, // ~2% tolerance
          reason: 'Tolerable strike frequency calculation mismatch');
    });

    test('Risk Level and Protection Level Determination', () {
      debugPrint('\n=== Risk Level and Protection Test ===');
      
      const ne = 0.00698146; // events/year (more precise)
      const nt = 0.003; // events/year
      
      final riskLevel = calcRiskLevel(
        expectedFrequencyNe: ne,
        tolerableFrequencyNt: nt,
      );
      
      final protectionLevel = determineProtectionLevel(riskLevel);
      final lpsRecommended = shouldInstallLPS(
        expectedFrequencyNe: ne,
        tolerableFrequencyNt: nt,
      );
      
      const expectedRiskLevel = 57.0; // % (1 - Nt/Ne) * 100 = (1 - 0.003/0.00698146) * 100
      const expectedProtectionLevel = 4;
      const expectedLpsRecommended = true;
      
      debugPrint('Ne = $ne, Nt = $nt');
      debugPrint('Risk Level = ${riskLevel.toStringAsFixed(1)}% (calculated: ${expectedRiskLevel.toStringAsFixed(1)}%)');
      debugPrint('Protection Level = $protectionLevel (expected: $expectedProtectionLevel)');
      debugPrint('LPS Recommended = $lpsRecommended (expected: $expectedLpsRecommended)');
      
      expect(closeTo(riskLevel, expectedRiskLevel, 1.2), isTrue, // ~2% tolerance
          reason: 'Risk level calculation mismatch');
      expect(protectionLevel, expectedProtectionLevel,
          reason: 'Protection level determination mismatch');
      expect(lpsRecommended, expectedLpsRecommended,
          reason: 'LPS recommendation mismatch');
    });

    test('Input Validation', () {
      debugPrint('\n=== Input Validation Test ===');
      
      // Valid input
      const validInput = LightningCalculationInput();
      expect(validateLightningInput(validInput), isTrue,
          reason: 'Default input should be valid');
      
      // Invalid building dimensions
      const invalidInput1 = LightningCalculationInput(lengthM: -1);
      expect(validateLightningInput(invalidInput1), isFalse,
          reason: 'Negative length should be invalid');
      
      const invalidInput2 = LightningCalculationInput(widthM: 0);
      expect(validateLightningInput(invalidInput2), isFalse,
          reason: 'Zero width should be invalid');
      
      const invalidInput3 = LightningCalculationInput(heightM: -5);
      expect(validateLightningInput(invalidInput3), isFalse,
          reason: 'Negative height should be invalid');
      
      // Invalid flash density
      const invalidInput4 = LightningCalculationInput(flashDensityNg: -1);
      expect(validateLightningInput(invalidInput4), isFalse,
          reason: 'Negative flash density should be invalid');
      
      debugPrint('All validation tests passed');
    });

    test('Edge Cases and Boundary Conditions', () {
      debugPrint('\n=== Edge Cases Test ===');
      
      // Very small building
      final smallBuildingArea = calcCollectiveAreaRectangular(
        lengthM: 1.0,
        widthM: 1.0,
        heightM: 1.0,
      );
      expect(smallBuildingArea > 0, isTrue,
          reason: 'Small building should have positive area');
      
      // Very large building  
      final largeBuildingArea = calcCollectiveAreaRectangular(
        lengthM: 1000.0,
        widthM: 1000.0,
        heightM: 100.0,
      );
      expect(largeBuildingArea > smallBuildingArea, isTrue,
          reason: 'Large building should have larger area than small building');
      
      // Risk level boundaries (updated based on image reference)
      expect(determineProtectionLevel(0), 1, reason: '0% risk should be level 1');
      expect(determineProtectionLevel(24.9), 1, reason: '24.9% risk should be level 1');
      expect(determineProtectionLevel(25), 2, reason: '25% risk should be level 2');
      expect(determineProtectionLevel(39.9), 2, reason: '39.9% risk should be level 2');
      expect(determineProtectionLevel(40), 3, reason: '40% risk should be level 3');
      expect(determineProtectionLevel(54.9), 3, reason: '54.9% risk should be level 3');
      expect(determineProtectionLevel(55), 4, reason: '55% risk should be level 4');
      expect(determineProtectionLevel(57), 4, reason: '57% risk should be level 4');
      expect(determineProtectionLevel(100), 4, reason: '100% risk should be level 4');
      
      debugPrint('All edge case tests passed');
    });
  });
} 